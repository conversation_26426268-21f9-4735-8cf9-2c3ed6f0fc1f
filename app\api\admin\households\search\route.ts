import { type NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { getTranslations } from 'next-intl/server';
import { z } from 'zod/v4';
import { authOptions } from '@/lib/auth/auth-options';
import {
  getHouseholdsCount,
  getHouseholdsWithDetails,
} from '@/lib/db/households';
import { getLocaleFromCookies } from '@/lib/utils/server-messages';

// Validation schema for search parameters
const searchParamsSchema = z.object({
  searchTerm: z.string().optional(),
  censusYearId: z.coerce.number().optional(),
  page: z.coerce.number().min(1).default(1),
  pageSize: z.coerce.number().min(1).max(100).default(20),
  sortBy: z.string().default('id'),
  sortOrder: z.enum(['asc', 'desc']).default('asc'),
});

/**
 * GET /api/admin/households/search
 *
 * Direct database search for households without caching
 * Used by client-side components for real-time search and filtering
 */
export async function GET(request: NextRequest) {
  // Get locale from centralized utility at the beginning
  const locale = await getLocaleFromCookies();
  const tAdmin = await getTranslations({ locale, namespace: 'admin' });

  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Require admin authentication
    if (!session || session.user.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse and validate query parameters
    const { searchParams } = new URL(request.url);
    const params = searchParamsSchema.parse({
      searchTerm: searchParams.get('searchTerm') || undefined,
      censusYearId: searchParams.get('censusYearId') || undefined,
      page: searchParams.get('page') || undefined,
      pageSize: searchParams.get('pageSize') || undefined,
      sortBy: searchParams.get('sortBy') || undefined,
      sortOrder: searchParams.get('sortOrder') || undefined,
    });

    const offset = (params.page - 1) * params.pageSize;

    // Direct database access without caching for real-time results
    let households;
    let total;

    // Use the same Prisma-based function for both search and non-search cases
    // This eliminates BigInt serialization issues and provides type safety
    households = await getHouseholdsWithDetails({
      limit: params.pageSize,
      offset,
      searchTerm: params.searchTerm || '',
      censusYearId: params.censusYearId,
    });

    // Get total count for results
    total = await getHouseholdsCount({
      searchTerm: params.searchTerm,
      censusYearId: params.censusYearId,
    });

    // Calculate pagination metadata
    const totalPages = Math.ceil(total / params.pageSize);

    // No BigInt serialization needed - Prisma handles this automatically
    return NextResponse.json({
      households,
      pagination: {
        page: params.page,
        pageSize: params.pageSize,
        total,
        totalPages,
      },
    });
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
    }

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: tAdmin('invalidParameters'), details: error.issues },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: tAdmin('householdSearchFailed') },
      { status: 500 }
    );
  }
}

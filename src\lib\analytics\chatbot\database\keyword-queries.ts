/**
 * Keyword Queries Module
 *
 * Contains keyword-based fallback query functions that process user messages
 * when intent-based analysis doesn't provide sufficient confidence.
 *
 * These functions handle:
 * - Main keyword query execution with timeout handling
 * - Age-based query processing
 * - Sacrament query processing
 * - Census year query processing
 * - Relationship query processing
 * - Location/suburb query processing
 * - Census form status query processing
 */

import type { NextRequest } from 'next/server';
import {
  getSecureErrorResponse,
  logSecureError,
} from '@/lib/analytics/chatbot/security';
import { prisma } from '@/lib/db/prisma';
import { executeQueriesWithTimeout } from './keyword-query-executor';

// Main keyword query execution function with timeout handling
export async function executeKeywordQuery(
  userMessage: string,
  request: NextRequest,
  locale: 'en' | 'zh-CN'
): Promise<string[]> {
  const results: string[] = [];
  const message = userMessage.toLowerCase();

  // Query timeout configuration
  const QUERY_TIMEOUT = 10_000; // 10 seconds

  try {
    // Professional timeout implementation with proper cleanup
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), QUERY_TIMEOUT);

    try {
      const result = await executeQueriesWithTimeout(message, results, request);
      clearTimeout(timeoutId);
      return result;
    } catch (queryError) {
      clearTimeout(timeoutId);
      throw queryError;
    }
  } catch (error) {
    // SECURITY: Secure error logging for keyword queries
    logSecureError('keyword_query', error, {
      messageLength: userMessage.length,
      hasTimeout:
        error instanceof Error && error.message === 'Database query timeout',
    });

    // SECURITY: Determine appropriate error response
    if (error instanceof Error && error.message === 'Database query timeout') {
      results.push(await getSecureErrorResponse('timeout', request, locale));
    } else {
      results.push(await getSecureErrorResponse('database', request, locale));
    }
    return results;
  }
}

// Age-Based Query Handler - RESTORED: Full original comprehensive implementation
export async function handleAgeBasedQueries(
  message: string,
  results: string[]
): Promise<void> {
  try {
    const currentYear = new Date().getFullYear();

    // Extract year from message (e.g., "born after 2000", "born before 1990")
    const yearMatch = message.match(/\b(19|20)\d{2}\b/);
    const year = yearMatch ? Number.parseInt(yearMatch[0], 10) : null;

    if (year) {
      // Specific year-based filtering
      const whereCondition: {
        dateOfBirth:
          | { not: null }
          | { gte: Date }
          | { lt: Date }
          | { gte: Date; lt: Date };
      } = { dateOfBirth: { not: null } };
      let description = '';

      if (message.includes('after') || message.includes('since')) {
        whereCondition.dateOfBirth = { gte: new Date(year, 0, 1) };
        description = `born after ${year}`;
      } else if (message.includes('before')) {
        whereCondition.dateOfBirth = { lt: new Date(year, 0, 1) };
        description = `born before ${year}`;
      } else if (message.includes('in') || message.includes('during')) {
        whereCondition.dateOfBirth = {
          gte: new Date(year, 0, 1),
          lt: new Date(year + 1, 0, 1),
        };
        description = `born in ${year}`;
      } else {
        // Default to "after" if no specific keyword
        whereCondition.dateOfBirth = { gte: new Date(year, 0, 1) };
        description = `born after ${year}`;
      }

      const filteredMembers = await prisma.member.findMany({
        where: whereCondition,
        select: {
          firstName: true,
          lastName: true,
          dateOfBirth: true,
          gender: true,
        },
        orderBy: { dateOfBirth: 'desc' },
      });

      if (filteredMembers.length === 0) {
        results.push(`No members found ${description}.`);
      } else {
        results.push(
          `Found ${filteredMembers.length} member(s) ${description}:`
        );

        const memberList = filteredMembers
          .map((member) => {
            const birthYear = member.dateOfBirth?.getFullYear();
            const age = birthYear ? currentYear - birthYear : 'Unknown';
            return `${member.firstName} ${member.lastName} (born ${birthYear}, age ${age}, ${member.gender})`;
          })
          .join(', ');

        results.push(memberList);

        // Add gender breakdown if multiple members
        if (filteredMembers.length > 1) {
          const genderCounts: Record<string, number> = {};
          filteredMembers.forEach((member) => {
            genderCounts[member.gender] =
              (genderCounts[member.gender] || 0) + 1;
          });

          const genderBreakdown = Object.entries(genderCounts)
            .map(([gender, count]) => `${gender}: ${count}`)
            .join(', ');
          results.push(`Gender breakdown: ${genderBreakdown}`);
        }
      }
    } else {
      // General age analysis without specific year
      const membersWithBirthDates = await prisma.member.findMany({
        where: {
          dateOfBirth: { not: null },
        },
        select: {
          firstName: true,
          lastName: true,
          dateOfBirth: true,
          gender: true,
        },
      });

      if (membersWithBirthDates.length === 0) {
        results.push('No members found with birth date information.');
        return;
      }

      // Calculate age groups - Updated to match getAgeGroup function
      const ageGroups = {
        'Under 18': 0,
        '18-30': 0,
        '31-50': 0,
        '51-70': 0,
        'Over 70': 0,
      };
      const memberDetails: string[] = [];

      membersWithBirthDates.forEach((member) => {
        if (member.dateOfBirth) {
          const age = currentYear - member.dateOfBirth.getFullYear();
          const birthYear = member.dateOfBirth.getFullYear();

          if (age < 18) {
            ageGroups['Under 18']++;
          } else if (age >= 18 && age <= 30) {
            ageGroups['18-30']++;
          } else if (age >= 31 && age <= 50) {
            ageGroups['31-50']++;
          } else if (age >= 51 && age <= 70) {
            ageGroups['51-70']++;
          } else {
            ageGroups['Over 70']++;
          }

          memberDetails.push(
            `${member.firstName} ${member.lastName} (born ${birthYear}, age ${age})`
          );
        }
      });

      const ageSummary = Object.entries(ageGroups)
        .filter(([, count]) => count > 0)
        .map(([group, count]) => `${group}: ${count}`)
        .join(', ');

      results.push(`Age group distribution: ${ageSummary}`);
      results.push(`Members with birth dates: ${memberDetails.join(', ')}`);
    }
  } catch (error) {
    logSecureError('age_query', error, { message: message.substring(0, 100) });
    results.push('Unable to process age-based query at this time.');
  }
}

// Sacrament Query Handler
export async function handleSacramentQueries(
  message: string,
  results: string[]
): Promise<void> {
  try {
    const sacramentCount = await prisma.sacrament.count();
    results.push(`Total sacrament records: ${sacramentCount}`);

    if (sacramentCount > 0) {
      // Get sacrament type breakdown
      const sacramentStats = await prisma.sacrament.groupBy({
        by: ['sacramentTypeId'],
        _count: { sacramentTypeId: true },
        orderBy: { _count: { sacramentTypeId: 'desc' } },
      });

      const sacramentTypes = await prisma.sacramentType.findMany({
        where: { id: { in: sacramentStats.map((s) => s.sacramentTypeId) } },
      });

      const typeMap = Object.fromEntries(
        sacramentTypes.map((t) => [t.id, t.name])
      );

      const summary = sacramentStats
        .map(
          (stat) =>
            `${typeMap[stat.sacramentTypeId]}: ${stat._count.sacramentTypeId}`
        )
        .join(', ');
      results.push(`Sacrament distribution: ${summary}`);

      // Recent sacraments if requested - RESTORED: Missing functionality
      if (
        message.includes('recent') ||
        message.includes('latest') ||
        message.includes('list')
      ) {
        const recentSacraments = await prisma.sacrament.findMany({
          take: 5,
          orderBy: { date: 'desc' },
          include: {
            member: { select: { firstName: true, lastName: true } },
            sacramentType: { select: { name: true } },
          },
        });

        if (recentSacraments.length > 0) {
          const sacramentList = recentSacraments
            .map(
              (s) =>
                `${s.member.firstName} ${s.member.lastName} - ${s.sacramentType.name} (${s.date?.toDateString() || 'No date'})`
            )
            .join(', ');
          results.push(`Recent sacraments: ${sacramentList}`);
        }
      }
    }
  } catch (error) {
    logSecureError('sacrament_query', error, {
      message: message.substring(0, 100),
    });
    results.push('Unable to process sacrament query at this time.');
  }
}

// Census Year Query Handler
export async function handleCensusYearQueries(
  message: string,
  results: string[]
): Promise<void> {
  try {
    const censusYears = await prisma.censusYear.findMany({
      orderBy: { year: 'desc' },
    });

    if (censusYears.length === 0) {
      results.push('No census years found in the database.');
      return;
    }

    const activeCensus = censusYears.find((cy) => cy.isActive);
    if (activeCensus) {
      results.push(`Active census year: ${activeCensus.year}`);
    }

    results.push(`Total census years: ${censusYears.length}`);

    const yearList = censusYears
      .map((cy) => `${cy.year}${cy.isActive ? ' (active)' : ''}`)
      .join(', ');
    results.push(`Census years: ${yearList}`);

    // Census year statistics if requested - RESTORED: Missing functionality
    if (
      message.includes('stat') ||
      message.includes('count') ||
      message.includes('member')
    ) {
      const yearStats = await Promise.all(
        censusYears.slice(0, 3).map(async (cy) => {
          const memberCount = await prisma.householdMember.count({
            where: { censusYearId: cy.id },
          });
          return `${cy.year}: ${memberCount} members`;
        })
      );
      results.push(`Member counts by year: ${yearStats.join(', ')}`);
    }
  } catch (error) {
    logSecureError('census_year_query', error, {
      message: message.substring(0, 100),
    });
    results.push('Unable to process census year query at this time.');
  }
}

// Relationship Query Handler
export async function handleRelationshipQueries(
  message: string,
  results: string[]
): Promise<void> {
  try {
    const relationshipStats = await prisma.householdMember.groupBy({
      by: ['relationship'],
      where: { isCurrent: true },
      _count: { relationship: true },
      orderBy: { _count: { relationship: 'desc' } },
    });

    if (relationshipStats.length === 0) {
      results.push('No household relationship data found.');
      return;
    }

    const totalMembers = relationshipStats.reduce(
      (sum, stat) => sum + stat._count.relationship,
      0
    );
    results.push(`Total household members: ${totalMembers}`);

    const relationshipSummary = relationshipStats
      .map((stat) => `${stat.relationship}: ${stat._count.relationship}`)
      .join(', ');
    results.push(`Relationship breakdown: ${relationshipSummary}`);

    // Handle specific relationship queries
    const relationshipKeywords = [
      'head',
      'spouse',
      'child',
      'parent',
      'sibling',
    ];
    const foundKeyword = relationshipKeywords.find((keyword) =>
      message.includes(keyword)
    );

    if (foundKeyword) {
      const specificStat = relationshipStats.find((stat) =>
        stat.relationship.toLowerCase().includes(foundKeyword)
      );

      if (specificStat) {
        results.push(
          `${specificStat.relationship} members: ${specificStat._count.relationship}`
        );
      }
    }
  } catch (error) {
    logSecureError('relationship_query', error, {
      message: message.substring(0, 100),
    });
    results.push('Unable to process relationship query at this time.');
  }
}

// Location/Suburb Query Handler
export async function handleLocationQueries(
  message: string,
  results: string[]
): Promise<void> {
  try {
    const suburbStats = await prisma.household.groupBy({
      by: ['suburb'],
      _count: { suburb: true },
      orderBy: { _count: { suburb: 'desc' } },
    });

    if (suburbStats.length === 0) {
      results.push('No location data found.');
      return;
    }

    const totalHouseholds = suburbStats.reduce(
      (sum, stat) => sum + stat._count.suburb,
      0
    );
    results.push(`Total households: ${totalHouseholds}`);
    results.push(`Locations covered: ${suburbStats.length} suburbs`);

    const suburbSummary = suburbStats
      .slice(0, 10)
      .map(
        (stat) =>
          `${stat.suburb}: ${stat._count.suburb} household${stat._count.suburb > 1 ? 's' : ''}`
      )
      .join(', ');
    results.push(`Suburb distribution: ${suburbSummary}`);

    // Chart data for location visualization - RESTORED: Missing functionality
    if (
      message.includes('chart') ||
      message.includes('graph') ||
      message.includes('visual')
    ) {
      const chartData = {
        type: 'bar',
        title: 'Households by Suburb',
        data: suburbStats.slice(0, 10).map((stat) => ({
          name: stat.suburb,
          value: stat._count.suburb,
        })),
      };
      results.push(`CHART_DATA: ${JSON.stringify(chartData)}`);
    }
  } catch (error) {
    logSecureError('location_query', error, {
      message: message.substring(0, 100),
    });
    results.push('Unable to process location query at this time.');
  }
}

// Census Form Status Query Handler - RESTORED: Original comprehensive implementation
export async function handleCensusFormQueries(
  message: string,
  results: string[]
): Promise<void> {
  try {
    const formStats = await prisma.censusForm.groupBy({
      by: ['status'],
      _count: { status: true },
    });

    if (formStats.length === 0) {
      results.push('No census form data found.');
      return;
    }

    const total = formStats.reduce((sum, stat) => sum + stat._count.status, 0);
    results.push(`Total census forms: ${total}`);

    const statusSummary = formStats
      .map((stat) => `${stat.status.replace('_', ' ')}: ${stat._count.status}`)
      .join(', ');
    results.push(`Form status distribution: ${statusSummary}`);

    // Completion rate calculation - RESTORED: Missing functionality
    const completed =
      formStats.find((s) => s.status === 'completed')?._count.status || 0;
    const completionRate =
      total > 0 ? ((completed / total) * 100).toFixed(1) : '0';
    results.push(`Completion rate: ${completionRate}% (${completed}/${total})`);

    // Chart data for form status visualization - RESTORED: Missing functionality
    if (
      message.includes('chart') ||
      message.includes('graph') ||
      message.includes('visual')
    ) {
      const chartData = {
        type: 'pie',
        title: 'Census Form Status Distribution',
        data: formStats.map((stat) => ({
          name: stat.status.replace('_', ' '),
          value: stat._count.status,
        })),
      };
      results.push(`CHART_DATA: ${JSON.stringify(chartData)}`);
    }
  } catch (error) {
    logSecureError('census_form_query', error, {
      message: message.substring(0, 100),
    });
    results.push('Unable to process census form query at this time.');
  }
}

// Re-export the main query executor
export { executeQueriesWithTimeout } from './keyword-query-executor';

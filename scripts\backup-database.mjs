/**
 * PostgreSQL Database Backup Script
 *
 * This script creates a backup of the PostgreSQL database and saves it to the backups directory.
 * It can be run manually or scheduled using a cron job.
 *
 * Usage:
 *   node scripts/backup-database.mjs
 *
 * Environment variables:
 *   DATABASE_URL - PostgreSQL connection string (required)
 *   BACKUP_DIR - Directory to store backups (default: ./backups)
 */

import { execSync } from 'child_process';
import { format as formatDate } from 'date-fns';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const config = {
  databaseUrl: process.env.DATABASE_URL,
  backupDir: process.env.BACKUP_DIR || path.join(__dirname, '..', 'backups'),
};

// Validate required environment variables
if (!config.databaseUrl) {
  console.error('ERROR: DATABASE_URL environment variable is required');
  process.exit(1);
}

// Parse database name from DATABASE_URL for filename
const dbName =
  config.databaseUrl.split('/').pop()?.split('?')[0] || 'wsccc_census';

// Create backup directory if it doesn't exist
if (!fs.existsSync(config.backupDir)) {
  fs.mkdirSync(config.backupDir, { recursive: true });
  console.log(`Created backup directory: ${config.backupDir}`);
}

// Generate filename with timestamp
const timestamp = formatDate(new Date(), 'yyyyMMdd_HHmmss');
const filename = `${dbName}_backup_${timestamp}.sql`;
const filepath = path.join(config.backupDir, filename);

try {
  // Use pg_dump for PostgreSQL backups
  let pgDumpCommand;

  if (process.platform === 'win32') {
    // For Windows, try common PostgreSQL installation paths
    const commonPaths = [
      'C:\\Program Files\\PostgreSQL\\16\\bin\\pg_dump.exe',
      'C:\\Program Files\\PostgreSQL\\15\\bin\\pg_dump.exe',
      'C:\\Program Files\\PostgreSQL\\14\\bin\\pg_dump.exe',
      'C:\\Program Files (x86)\\PostgreSQL\\16\\bin\\pg_dump.exe',
      'C:\\Program Files (x86)\\PostgreSQL\\15\\bin\\pg_dump.exe',
    ];

    pgDumpCommand =
      commonPaths.find((path) => fs.existsSync(path)) || 'pg_dump';
  } else {
    // For other platforms, assume pg_dump is in the PATH
    pgDumpCommand = 'pg_dump';
  }

  // Build the command for PostgreSQL
  const command = `"${pgDumpCommand}" "${config.databaseUrl}" > "${filepath}"`;

  // Execute the command
  execSync(command, { stdio: 'inherit' });

  console.log(`Database backup created successfully: ${filepath}`);

  // Clean up old backups (keep only the last 10)
  const files = fs
    .readdirSync(config.backupDir)
    .filter(
      (file) => file.startsWith(`${dbName}_backup_`) && file.endsWith('.sql')
    )
    .map((file) => ({
      name: file,
      path: path.join(config.backupDir, file),
      time: fs.statSync(path.join(config.backupDir, file)).mtime.getTime(),
    }))
    .sort((a, b) => b.time - a.time); // Sort by time (newest first)

  // Remove old backups (keep the 10 most recent)
  if (files.length > 10) {
    console.log('Cleaning up old backups (keeping the 10 most recent)...');

    for (let i = 10; i < files.length; i++) {
      fs.unlinkSync(files[i].path);
      console.log(`Deleted old backup: ${files[i].name}`);
    }
  }

  console.log('Backup process completed successfully.');
} catch (error) {
  console.error('Error creating database backup:', error.message);
  process.exit(1);
}

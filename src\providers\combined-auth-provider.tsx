'use client';

import { usePathname } from 'next/navigation';
import { SessionProvider } from 'next-auth/react';
import { createContext, type ReactNode, useContext } from 'react';

// Create a context to track which auth system is active
type AuthSystemType = 'admin' | 'census' | null;
const AuthSystemContext = createContext<{
  activeSystem: AuthSystemType;
  setActiveSystem: (system: AuthSystemType) => void;
}>({
  activeSystem: null,
  setActiveSystem: () => {},
});

// Hook to use the auth system context
export const useAuthSystem = () => useContext(AuthSystemContext);

/**
 * Combined Auth Provider
 *
 * CLEAN APPROACH: Pure pathname-based provider selection.
 * Simple, reliable, and eliminates race conditions.
 */
export function CombinedAuthProvider({ children }: { children: ReactNode }) {
  const pathname = usePathname();

  // Determine which SessionProvider to render based on pathname
  const renderAuthProvider = () => {
    // Simple rule: admin pages use admin provider, everything else uses census provider
    const isAdminPath = pathname?.startsWith('/admin');

    if (isAdminPath) {
      if (process.env.NODE_ENV === 'development') {
        console.log('Using admin auth provider for pathname:', pathname);
      }
      return <SessionProvider basePath="/api/auth">{children}</SessionProvider>;
    }

    // Default to census provider for all other paths
    if (process.env.NODE_ENV === 'development') {
      console.log('Using census auth provider for pathname:', pathname);
    }
    return (
      <SessionProvider
        basePath="/api/census/auth"
        refetchInterval={0}
        refetchOnWindowFocus={false}
      >
        {children}
      </SessionProvider>
    );
  };

  // Determine activeSystem for context (for any components that still need it)
  const activeSystem: AuthSystemType = pathname?.startsWith('/admin')
    ? 'admin'
    : 'census';

  // No-op setActiveSystem for backward compatibility
  const setActiveSystem = () => {
    if (process.env.NODE_ENV === 'development') {
      console.log(
        'setActiveSystem called but ignored - using pathname-based logic'
      );
    }
  };

  return (
    <AuthSystemContext.Provider value={{ activeSystem, setActiveSystem }}>
      {renderAuthProvider()}
    </AuthSystemContext.Provider>
  );
}

import { useCallback, useEffect, useRef } from 'react';
import { StateManager } from '../utils/memory/state-manager';

interface UseMemoryCleanupOptions {
  keys?: string[];
  cleanupOnUnmount?: boolean;
  cleanupInterval?: number;
}

/**
 * Hook for automatic memory cleanup and management
 * Prevents memory leaks from global state managers
 */
export function useMemoryCleanup({
  keys = [],
  cleanupOnUnmount = true,
  cleanupInterval,
}: UseMemoryCleanupOptions = {}) {
  const keysRef = useRef<Set<string>>(new Set());
  const intervalRef = useRef<number | null>(null);

  // Track keys for cleanup
  const trackKey = useCallback((key: string) => {
    keysRef.current.add(key);
  }, []);

  const untrackKey = useCallback((key: string) => {
    keysRef.current.delete(key);
    StateManager.cleanup(key);
  }, []);

  // Manual cleanup function
  const cleanup = useCallback((specificKeys?: string[]) => {
    const keysToClean = specificKeys || Array.from(keysRef.current);
    keysToClean.forEach((key) => {
      StateManager.cleanup(key);
      keysRef.current.delete(key);
    });
  }, []);

  // Track provided keys
  useEffect(() => {
    keys.forEach((key) => {
      keysRef.current.add(key);
    });

    // Capture ref value at effect creation time (ESLint recommended pattern)
    const currentKeys = keysRef.current;

    return () => {
      if (cleanupOnUnmount) {
        keys.forEach((key) => {
          currentKeys.delete(key);
          StateManager.cleanup(key);
        });
      }
    };
  }, [keys, cleanupOnUnmount]);

  // Setup cleanup interval if specified
  useEffect(() => {
    if (cleanupInterval && cleanupInterval > 0) {
      intervalRef.current = setInterval(() => {
        const stats = StateManager.getStats();
        if (stats.totalEntries > stats.maxEntries * 0.7) {
          // Trigger cleanup of tracked keys when threshold is reached
          Array.from(keysRef.current).forEach((key) => {
            StateManager.cleanup(key);
          });
          // Clear tracked keys since they've been cleaned up
          keysRef.current.clear();
        }
      }, cleanupInterval) as unknown as number;

      return () => {
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
        }
      };
    }
  }, [cleanupInterval]);

  return {
    trackKey,
    untrackKey,
    cleanup,
    getStats: StateManager.getStats.bind(StateManager),
  };
}

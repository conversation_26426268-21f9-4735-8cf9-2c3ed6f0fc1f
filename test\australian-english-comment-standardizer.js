#!/usr/bin/env node

/**
 * WSCCC Census System - Australian English Comment Standardizer
 *
 * This tool scans all TypeScript/JavaScript files for code comments and identifies
 * American English spellings that should be converted to Australian English.
 *
 * Features:
 * - Scans single-line and multi-line comments
 * - Identifies American spellings and suggests Australian alternatives
 * - Respects technical terms, API names, and code-specific terminology
 * - Provides detailed reports with before/after examples
 * - Excludes build directories and generated files
 *
 * Usage: node test/australian-english-comment-standardizer.js
 */

import fs from 'node:fs';
import path from 'node:path';
import { fileURLToPath } from 'node:url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Get project root directory
function getProjectRoot() {
  let currentDir = __dirname;
  while (currentDir !== path.dirname(currentDir)) {
    if (fs.existsSync(path.join(currentDir, 'package.json'))) {
      return currentDir;
    }
    currentDir = path.dirname(currentDir);
  }
  return process.cwd();
}

const PROJECT_ROOT = getProjectRoot();

// Regular expressions for performance optimization
const SINGLE_LINE_COMMENT_REGEX = /\/\/\s*(.+)/;
const MULTI_LINE_COMMENT_REGEX = /\/\*\*([\s\S]*?)\*\//g;
const COMMENT_ASTERISK_REGEX = /^\s*\*\s?/;
const WORD_BOUNDARY_REGEX = /[^\w]/g;
const CAMEL_CASE_REGEX = /^[a-z]+[A-Z]/;
const PASCAL_CASE_REGEX = /^[A-Z][a-z]+[A-Z]/;
const WHITESPACE_REGEX = /\s+/;

// Configuration
const CONFIG = {
  // File extensions to scan
  fileExtensions: ['.ts', '.tsx', '.js', '.jsx'],

  // Directories to scan
  scanDirectories: [
    path.join(PROJECT_ROOT, 'src'),
    path.join(PROJECT_ROOT, 'app'),
    path.join(PROJECT_ROOT, 'components'),
    path.join(PROJECT_ROOT, 'lib'),
    path.join(PROJECT_ROOT, 'hooks'),
    path.join(PROJECT_ROOT, 'test'),
    path.join(PROJECT_ROOT, 'scripts'),
  ],

  // Directories and files to exclude
  excludePatterns: [
    'node_modules',
    '.next',
    '.git',
    'dist',
    'build',
    'out',
    'coverage',
    'public',
    '*.generated.*',
    'lang/en.d.json.ts', // Auto-generated file
    '*.backup',
    '*.log',
    'new_server',
  ],
};

// Australian English spelling conversions
const AUSTRALIAN_SPELLINGS = {
  // -or to -our
  color: 'colour',
  colors: 'colours',
  colored: 'coloured',
  coloring: 'colouring',
  colorful: 'colourful',
  colorless: 'colourless',
  behavior: 'behaviour',
  behaviors: 'behaviours',
  behavioral: 'behavioural',
  honor: 'honour',
  honors: 'honours',
  honored: 'honoured',
  honoring: 'honouring',
  honorable: 'honourable',
  favor: 'favour',
  favors: 'favours',
  favored: 'favoured',
  favoring: 'favouring',
  favorable: 'favourable',
  flavor: 'flavour',
  flavors: 'flavours',
  flavored: 'flavoured',
  flavoring: 'flavouring',
  humor: 'humour',
  humorous: 'humorous', // No change
  labor: 'labour',
  labored: 'laboured',
  laboring: 'labouring',
  neighbor: 'neighbour',
  neighbors: 'neighbours',
  neighboring: 'neighbouring',
  rumor: 'rumour',
  rumors: 'rumours',
  rumored: 'rumoured',
  vapor: 'vapour',
  vapors: 'vapours',

  // -er to -re
  center: 'centre',
  centers: 'centres',
  centered: 'centred',
  centering: 'centring',
  theater: 'theatre',
  theaters: 'theatres',
  meter: 'metre',
  meters: 'metres',
  fiber: 'fibre',
  fibers: 'fibres',
  liter: 'litre',
  liters: 'litres',
  caliber: 'calibre',
  saber: 'sabre',
  somber: 'sombre',

  // -ize to -ise
  realize: 'realise',
  realizes: 'realises',
  realized: 'realised',
  realizing: 'realising',
  realization: 'realisation',
  organize: 'organise',
  organizes: 'organises',
  organized: 'organised',
  organizing: 'organising',
  organization: 'organisation',
  organizations: 'organisations',
  organizational: 'organisational',
  recognize: 'recognise',
  recognizes: 'recognises',
  recognized: 'recognised',
  recognizing: 'recognising',
  recognition: 'recognition', // No change
  analyze: 'analyse',
  analyzes: 'analyses',
  analyzed: 'analysed',
  analyzing: 'analysing',
  analysis: 'analysis', // No change
  customize: 'customise',
  customizes: 'customises',
  customized: 'customised',
  customizing: 'customising',
  customization: 'customisation',
  optimize: 'optimise',
  optimizes: 'optimises',
  optimized: 'optimised',
  optimizing: 'optimising',
  optimization: 'optimisation',
  minimize: 'minimise',
  minimizes: 'minimises',
  minimized: 'minimised',
  minimizing: 'minimising',
  maximize: 'maximise',
  maximizes: 'maximises',
  maximized: 'maximised',
  maximizing: 'maximising',
  utilize: 'utilise',
  utilizes: 'utilises',
  utilized: 'utilised',
  utilizing: 'utilising',
  utilization: 'utilisation',
  synchronize: 'synchronise',
  synchronizes: 'synchronises',
  synchronized: 'synchronised',
  synchronizing: 'synchronising',
  synchronization: 'synchronisation',
  initialize: 'initialise',
  initializes: 'initialises',
  initialized: 'initialised',
  initializing: 'initialising',
  initialization: 'initialisation',
  finalize: 'finalise',
  finalizes: 'finalises',
  finalized: 'finalised',
  finalizing: 'finalising',
  finalization: 'finalisation',
  visualize: 'visualise',
  visualizes: 'visualises',
  visualized: 'visualised',
  visualizing: 'visualising',
  visualization: 'visualisation',
  categorize: 'categorise',
  categorizes: 'categorises',
  categorized: 'categorised',
  categorizing: 'categorising',
  categorization: 'categorisation',
  prioritize: 'prioritise',
  prioritizes: 'prioritises',
  prioritized: 'prioritised',
  prioritizing: 'prioritising',
  prioritization: 'prioritisation',
  standardize: 'standardise',
  standardizes: 'standardises',
  standardized: 'standardised',
  standardizing: 'standardising',
  standardization: 'standardisation',
  localize: 'localise',
  localizes: 'localises',
  localized: 'localised',
  localizing: 'localising',
  localization: 'localisation',
  globalize: 'globalise',
  globalizes: 'globalises',
  globalized: 'globalised',
  globalizing: 'globalising',
  globalization: 'globalisation',
  modernize: 'modernise',
  modernizes: 'modernises',
  modernized: 'modernised',
  modernizing: 'modernising',
  modernization: 'modernisation',
  capitalize: 'capitalise',
  capitalizes: 'capitalises',
  capitalized: 'capitalised',
  capitalizing: 'capitalising',
  capitalization: 'capitalisation',

  // Other common differences
  defense: 'defence',
  defenses: 'defences',
  defensive: 'defensive', // No change
  offense: 'offence',
  offenses: 'offences',
  offensive: 'offensive', // No change
  license: 'licence', // noun form
  practice: 'practise', // verb form (noun stays 'practice')
  advice: 'advice', // No change (noun)
  advise: 'advise', // No change (verb)
  device: 'device', // No change (noun)
  devise: 'devise', // No change (verb)
  gray: 'grey',
  grays: 'greys',
  grayish: 'greyish',
  aging: 'ageing',
  catalog: 'catalogue',
  catalogs: 'catalogues',
  dialog: 'dialogue',
  dialogs: 'dialogues',
  program: 'programme', // Context-dependent
  programs: 'programmes', // Context-dependent
  check: 'check', // No change in most contexts
  cheque: 'cheque', // Financial context
  tire: 'tyre', // Vehicle context
  tires: 'tyres', // Vehicle context
  pajamas: 'pyjamas',
  skeptical: 'sceptical',
  skepticism: 'scepticism',
  artifact: 'artefact',
  artifacts: 'artefacts',
  maneuver: 'manoeuvre',
  maneuvers: 'manoeuvres',
  maneuvered: 'manoeuvred',
  maneuvering: 'manoeuvring',
};

// Technical terms that should NOT be changed (API names, libraries, etc.)
const TECHNICAL_EXCLUSIONS = [
  // Programming terms that should stay American
  'color', // CSS property
  'backgroundColor', // CSS property
  'textColor', // CSS property
  'borderColor', // CSS property
  'colorScheme', // CSS property
  'colorSpace', // CSS property
  'colorProfile', // CSS property
  'colorMode', // CSS property
  'colorPicker', // Component name
  'colorPalette', // Component name
  'colorWheel', // Component name
  'colorValue', // Variable name
  'colorCode', // Variable name
  'colorHex', // Variable name
  'colorRgb', // Variable name
  'colorHsl', // Variable name

  // Library/framework specific terms
  'center', // CSS value
  'textAlign', // CSS property
  'alignItems', // CSS property
  'justifyContent', // CSS property
  'flexDirection', // CSS property
  'centerX', // Coordinate
  'centerY', // Coordinate
  'centerPoint', // Coordinate

  // API and method names
  'analyze', // Method name in libraries
  'analyzer', // Class name
  'customizer', // Class name
  'optimizer', // Class name
  'minimizer', // Class name
  'maximizer', // Class name
  'synchronizer', // Class name
  'initializer', // Class name
  'finalizer', // Class name
  'visualizer', // Class name
  'categorizer', // Class name
  'prioritizer', // Class name
  'standardizer', // Class name
  'localizer', // Class name
  'globalizer', // Class name
  'modernizer', // Class name
  'capitalizer', // Class name

  // Brand names and proper nouns
  'Color', // Brand name
  'Center', // Place name
  'Theater', // Place name
  'Organization', // Proper noun
  'Behavior', // Proper noun
  'Honor', // Proper noun
  'Favor', // Proper noun
  'Labor', // Proper noun
  'Neighbor', // Proper noun
  'Rumor', // Proper noun
  'Vapor', // Proper noun
  'Fiber', // Proper noun
  'Meter', // Proper noun
  'Liter', // Proper noun
  'Caliber', // Proper noun
  'Saber', // Proper noun
  'Defense', // Proper noun
  'Offense', // Proper noun
  'License', // Proper noun
  'Practice', // Proper noun
  'Program', // Proper noun
  'Dialog', // Proper noun
  'Catalog', // Proper noun
  'Artifact', // Proper noun
  'Maneuver', // Proper noun
];

// Statistics tracking
const stats = {
  filesScanned: 0,
  totalComments: 0,
  commentsWithAmericanSpellings: 0,
  totalReplacements: 0,
  filesByType: {},
  spellingsByType: {},
};

// Results storage
const results = [];

console.log(
  '🇦🇺 WSCCC Census System - Australian English Comment Standardizer\n'
);
console.log('📋 Scanning for American English spellings in code comments...\n');

/**
 * Check if a file should be excluded from scanning
 */
function shouldExcludeFile(filePath) {
  const relativePath = path.relative(PROJECT_ROOT, filePath);
  return CONFIG.excludePatterns.some((pattern) =>
    relativePath.includes(pattern)
  );
}

/**
 * Check if a file has a valid extension for scanning
 */
function hasValidExtension(filePath) {
  const ext = path.extname(filePath);
  return CONFIG.fileExtensions.includes(ext);
}

/**
 * Extract comments from file content
 */
function extractComments(content, filePath) {
  const comments = [];
  const lines = content.split('\n');

  // Single-line comments
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    const singleLineMatch = line.match(SINGLE_LINE_COMMENT_REGEX);
    if (singleLineMatch) {
      comments.push({
        type: 'single',
        content: singleLineMatch[1].trim(),
        line: i + 1,
        filePath,
      });
    }
  }

  // Multi-line comments
  let match;
  while ((match = MULTI_LINE_COMMENT_REGEX.exec(content)) !== null) {
    const commentContent = match[1]
      .split('\n')
      .map((line) => line.replace(COMMENT_ASTERISK_REGEX, '').trim())
      .filter((line) => line.length > 0)
      .join(' ');

    if (commentContent.trim()) {
      const lineNumber = content.substring(0, match.index).split('\n').length;
      comments.push({
        type: 'multi',
        content: commentContent,
        line: lineNumber,
        filePath,
      });
    }
  }

  return comments;
}

/**
 * Check if a word should be excluded from conversion
 */
function shouldExcludeWord(word, context) {
  // Check if it's a technical exclusion
  if (TECHNICAL_EXCLUSIONS.includes(word)) {
    return true;
  }

  // Check if it's in a code context (camelCase, PascalCase, etc.)
  if (CAMEL_CASE_REGEX.test(word) || PASCAL_CASE_REGEX.test(word)) {
    return true;
  }

  // Check if it's part of a URL or file path
  if (
    context.includes('http') ||
    context.includes('www.') ||
    context.includes('/')
  ) {
    return true;
  }

  return false;
}

/**
 * Find American spellings in a comment
 */
function findAmericanSpellings(comment) {
  const findings = [];
  const words = comment.content.split(WHITESPACE_REGEX);

  for (const word of words) {
    // Clean the word of punctuation
    const cleanWord = word.replace(WORD_BOUNDARY_REGEX, '');
    const lowerWord = cleanWord.toLowerCase();

    if (
      AUSTRALIAN_SPELLINGS[lowerWord] &&
      !shouldExcludeWord(cleanWord, comment.content)
    ) {
      // Preserve original case
      let suggestion = AUSTRALIAN_SPELLINGS[lowerWord];
      if (cleanWord[0] === cleanWord[0].toUpperCase()) {
        suggestion = suggestion.charAt(0).toUpperCase() + suggestion.slice(1);
      }
      if (cleanWord === cleanWord.toUpperCase()) {
        suggestion = suggestion.toUpperCase();
      }

      findings.push({
        original: cleanWord,
        suggestion,
        context: comment.content,
        line: comment.line,
        filePath: comment.filePath,
      });
    }
  }

  return findings;
}

/**
 * Scan a single file for American spellings
 */
async function scanFile(filePath) {
  try {
    const content = await fs.promises.readFile(filePath, 'utf8');
    const comments = extractComments(content, filePath);

    stats.filesScanned++;
    stats.totalComments += comments.length;

    const ext = path.extname(filePath);
    stats.filesByType[ext] = (stats.filesByType[ext] || 0) + 1;

    const fileFindings = [];

    for (const comment of comments) {
      const spellings = findAmericanSpellings(comment);
      if (spellings.length > 0) {
        stats.commentsWithAmericanSpellings++;
        stats.totalReplacements += spellings.length;
        fileFindings.push(...spellings);

        for (const spelling of spellings) {
          const key = `${spelling.original} → ${spelling.suggestion}`;
          stats.spellingsByType[key] = (stats.spellingsByType[key] || 0) + 1;
        }
      }
    }

    if (fileFindings.length > 0) {
      results.push({
        filePath,
        findings: fileFindings,
      });
    }
  } catch (error) {
    console.error(`❌ Error scanning file ${filePath}:`, error.message);
  }
}

/**
 * Recursively scan a directory
 */
async function scanDirectory(dirPath) {
  try {
    const entries = await fs.promises.readdir(dirPath, { withFileTypes: true });

    const tasks = [];

    for (const entry of entries) {
      const fullPath = path.join(dirPath, entry.name);

      if (shouldExcludeFile(fullPath)) {
        continue;
      }

      if (entry.isDirectory()) {
        tasks.push(scanDirectory(fullPath));
      } else if (entry.isFile() && hasValidExtension(fullPath)) {
        tasks.push(scanFile(fullPath));
      }
    }

    await Promise.all(tasks);
  } catch (error) {
    console.error(`❌ Error scanning directory ${dirPath}:`, error.message);
  }
}

/**
 * Generate and display the report
 */
function generateReport() {
  console.log('📊 SCAN RESULTS\n');
  console.log(`📁 Files scanned: ${stats.filesScanned}`);
  console.log(`💬 Total comments: ${stats.totalComments}`);
  console.log(
    `🔍 Comments with American spellings: ${stats.commentsWithAmericanSpellings}`
  );
  console.log(`🔄 Total replacements suggested: ${stats.totalReplacements}\n`);

  if (Object.keys(stats.filesByType).length > 0) {
    console.log('📋 Files by type:');
    for (const [ext, count] of Object.entries(stats.filesByType)) {
      console.log(`  ${ext}: ${count} files`);
    }
    console.log();
  }

  if (stats.totalReplacements > 0) {
    console.log('🔤 Most common spelling suggestions:');
    const sortedSpellings = Object.entries(stats.spellingsByType)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10);

    for (const [spelling, count] of sortedSpellings) {
      console.log(`  ${spelling}: ${count} occurrences`);
    }
    console.log();

    console.log('📝 DETAILED FINDINGS\n');
    for (const result of results) {
      const relativePath = path.relative(PROJECT_ROOT, result.filePath);
      console.log(`📄 ${relativePath}`);

      for (const finding of result.findings) {
        console.log(
          `  Line ${finding.line}: "${finding.original}" → "${finding.suggestion}"`
        );
        console.log(`    Context: ${finding.context}`);
      }
      console.log();
    }
  } else {
    console.log(
      '✅ No American spellings found in comments! All comments are using Australian English.'
    );
  }
}

/**
 * Main execution function
 */
async function main() {
  try {
    const scanTasks = CONFIG.scanDirectories
      .filter((scanDir) => fs.existsSync(scanDir))
      .map((scanDir) => {
        console.log(`🔍 Scanning: ${path.relative(PROJECT_ROOT, scanDir)}`);
        return scanDirectory(scanDir);
      });

    await Promise.all(scanTasks);

    generateReport();

    console.log(
      '\n🎉 Australian English Comment Standardizer completed successfully!'
    );

    if (stats.totalReplacements > 0) {
      console.log('\n💡 To maintain Australian English standards:');
      console.log('   1. Review the suggested changes above');
      console.log('   2. Update comments manually to use Australian spellings');
      console.log('   3. Consider adding spell-check rules to your editor');
      console.log(
        '   4. Run this tool regularly to catch new American spellings'
      );
    }
  } catch (error) {
    console.error('❌ Fatal error:', error.message);
    process.exit(1);
  }
}

// Run the script
main();

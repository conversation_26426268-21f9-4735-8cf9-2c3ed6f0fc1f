/**
 * Analytics and Chatbot Type Definitions
 * 
 * This file contains all type definitions for the analytics chatbot system,
 * including query filters, intent detection, and distribution data structures.
 */

// Query filters interface for type safety
export interface QueryFilters {
  gender?: string | null;
  ageRange?: { min?: number; max?: number } | null;
  location?: string | null;
  sacramentType?: string | null;
  censusYear?: number | null;
  relationship?: string | null;
  hobby?: string | null;
  occupation?: string | null;
}

// Convert distribution data to table format for automatic pagination
export interface DistributionTableData {
  data: Array<Record<string, unknown>>;
  queryType: string;
  title: string;
  totalRecords: number;
}

// QueryIntent interface for AI-driven intent detection
export interface QueryIntent {
  dataType:
    | 'member_demographics'
    | 'household_info'
    | 'sacrament_records'
    | 'census_participation'
    | 'temporal_analysis'
    | 'general';
  analysisType:
    | 'count'
    | 'distribution'
    | 'list'
    | 'chart'
    | 'overview'
    | 'specific';
  filters?: {
    gender?: string | null;
    ageRange?: { min?: number; max?: number } | null;
    location?: string | null;
    sacramentType?: string | null;
    censusYear?: number | null;
    relationship?: string | null;
    hobby?: string | null;
    occupation?: string | null;
  };
  chartRequested?: boolean;
  confidence: number; // 0-1 confidence score from AI analysis
}

// Chatbot message interface for AI SDK compatibility
export interface ChatbotMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
  id?: string;
  createdAt?: string | Date;
}

// Chatbot request interface for API validation
export interface ChatbotRequest {
  messages: ChatbotMessage[];
}

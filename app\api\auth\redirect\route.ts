import { type NextRequest, NextResponse } from 'next/server';
import { getTranslations } from 'next-intl/server';
import { authErrorKeys } from '@/lib/errors/auth-errors';
import { getLocaleFromCookies } from '@/lib/utils/server-messages';

export async function GET(request: NextRequest) {
  // Get the reason from the query parameters
  const { searchParams } = new URL(request.url);
  const reason = searchParams.get('reason');
  const redirectTo = searchParams.get('redirectTo') || '/admin/login';

  // Create a response that redirects to the login page
  const response = NextResponse.redirect(new URL(redirectTo, request.url));

  // If there's a reason, set a cookie with the toast message
  if (reason) {
    // Get translated message based on user's locale
    const locale = await getLocaleFromCookies();
    const t = await getTranslations({ locale, namespace: 'auth' });
    const translationKey = authErrorKeys[reason] || authErrorKeys.default;
    const translatedMessage = t(translationKey as any);

    response.cookies.set(
      'auth_toast',
      JSON.stringify({
        type: 'warning',
        message: translatedMessage,
      }),
      {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        maxAge: 10, // 10 seconds - short expiry prevents persistence issues
        path: '/',
        sameSite: 'lax',
      }
    );
  }

  return response;
}

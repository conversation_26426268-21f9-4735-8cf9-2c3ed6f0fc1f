'use client';

import * as PopoverPrimitive from '@radix-ui/react-popover';
import * as React from 'react';
import { cn } from '@/lib/utils';

/**
 * Dialog-compatible PopoverContent that removes the Portal wrapper
 * to prevent z-index issues when used inside dialogs.
 *
 * This is a specialized version of PopoverContent that should only be used
 * when the popover needs to be rendered inside a dialog or modal.
 */
const DialogCompatiblePopoverContent = React.forwardRef<
  React.ElementRef<typeof PopoverPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof PopoverPrimitive.Content>
>(({ className, align = 'center', sideOffset = 4, ...props }, ref) => (
  <PopoverPrimitive.Content
    align={align}
    className={cn(
      'data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=closed]:animate-out data-[state=open]:animate-in',
      className
    )}
    ref={ref}
    sideOffset={sideOffset}
    {...props}
  />
));
DialogCompatiblePopoverContent.displayName =
  PopoverPrimitive.Content.displayName;

export { DialogCompatiblePopoverContent };

import { cookies } from 'next/headers';
import { getTranslations } from 'next-intl/server';
import { authErrorKeys } from '@/lib/errors/auth-errors';
import { censusErrorKeys } from '@/lib/errors/census-errors';
import { infoMessageKeys } from '@/lib/messages/info-messages';
import { successMessageKeys } from '@/lib/messages/success-messages';
import { warningMessageKeys } from '@/lib/messages/warning-messages';

/**
 * Get locale from cookies for server-side message handling
 * Centralized utility function for consistent locale detection across API routes
 */
export async function getLocaleFromCookies(): Promise<'en' | 'zh-CN'> {
  const cookieStore = await cookies();
  return cookieStore.get('NEXT_LOCALE')?.value === 'zh-CN' ? 'zh-CN' : 'en';
}

/**
 * Set server-to-client message with automatic translation and context awareness
 * @param type - Message type
 * @param messageKey - Message key to translate
 * @param authContext - Auth context (admin or census)
 * @param parameters - Optional parameters for translation interpolation
 */
export async function setServerMessage(
  type: 'success' | 'error' | 'warning' | 'info',
  messageKey: string,
  authContext: 'admin' | 'census',
  parameters?: Record<string, string | number>
) {
  const cookieStore = await cookies();
  const locale = await getLocaleFromCookies();

  let translatedMessage: string;
  let cookieName: string;

  // Determine cookie name based on auth context
  cookieName = authContext === 'admin' ? 'auth_toast' : 'census_toast';

  // Get translated message based on type and context
  switch (type) {
    case 'success': {
      const tNotifications = await getTranslations({
        locale,
        namespace: 'notifications',
      });
      const successKey =
        successMessageKeys[messageKey] || successMessageKeys.default;
      translatedMessage = parameters
        ? tNotifications(successKey as any, parameters as any)
        : tNotifications(successKey as any);
      break;
    }

    case 'error': {
      const tAuth = await getTranslations({ locale, namespace: 'auth' });
      const errorKeys =
        authContext === 'admin' ? authErrorKeys : censusErrorKeys;
      const errorKey = errorKeys[messageKey] || errorKeys.default;
      translatedMessage = parameters
        ? tAuth(errorKey as any, parameters as any)
        : tAuth(errorKey as any);
      break;
    }

    case 'warning': {
      const tWarnings = await getTranslations({
        locale,
        namespace: 'warnings',
      });
      const warningKey =
        warningMessageKeys[messageKey] || warningMessageKeys.default;
      translatedMessage = parameters
        ? tWarnings(warningKey as any, parameters as any)
        : tWarnings(warningKey as any);
      break;
    }

    case 'info': {
      const tCommon = await getTranslations({ locale, namespace: 'common' });
      const infoKey = infoMessageKeys[messageKey] || infoMessageKeys.default;
      translatedMessage = parameters
        ? tCommon(infoKey as any, parameters as any)
        : tCommon(infoKey as any);
      break;
    }

    default:
      translatedMessage = 'Unknown message type';
  }

  const messageData = {
    type,
    message: translatedMessage,
  };

  cookieStore.set({
    name: cookieName,
    value: JSON.stringify(messageData),
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    maxAge: 10, // 10 seconds - short expiry prevents persistence issues
    path: '/',
    sameSite: 'lax',
  });
}

/**
 * Set success message for server-to-client communication
 * @param messageKey - Success message key
 * @param authContext - Auth context (admin or census)
 * @param parameters - Optional parameters for translation interpolation
 */
export async function setSuccessMessage(
  messageKey: string,
  authContext: 'admin' | 'census',
  parameters?: Record<string, string | number>
) {
  return setServerMessage('success', messageKey, authContext, parameters);
}

/**
 * Set error message for server-to-client communication
 * @param errorKey - Error key
 * @param authContext - Auth context (admin or census)
 * @param parameters - Optional parameters for translation interpolation
 */
export async function setErrorMessage(
  errorKey: string,
  authContext: 'admin' | 'census',
  parameters?: Record<string, string | number>
) {
  return setServerMessage('error', errorKey, authContext, parameters);
}

/**
 * Set warning message for server-to-client communication
 * @param messageKey - Warning message key
 * @param authContext - Auth context (admin or census)
 * @param parameters - Optional parameters for translation interpolation
 */
export async function setWarningMessage(
  messageKey: string,
  authContext: 'admin' | 'census',
  parameters?: Record<string, string | number>
) {
  return setServerMessage('warning', messageKey, authContext, parameters);
}

/**
 * Set info message for server-to-client communication
 * @param messageKey - Info message key
 * @param authContext - Auth context (admin or census)
 * @param parameters - Optional parameters for translation interpolation
 */
export async function setInfoMessage(
  messageKey: string,
  authContext: 'admin' | 'census',
  parameters?: Record<string, string | number>
) {
  return setServerMessage('info', messageKey, authContext, parameters);
}

/**
 * Auto-detect auth context from request URL and set message
 * @param request - The incoming request
 * @param type - Message type
 * @param messageKey - Message key
 * @param parameters - Optional parameters for translation interpolation
 */
export async function setServerMessageFromRequest(
  request: Request,
  type: 'success' | 'error' | 'warning' | 'info',
  messageKey: string,
  parameters?: Record<string, string | number>
) {
  const url = new URL(request.url);
  const authContext = url.pathname.includes('/api/admin') ? 'admin' : 'census';

  return setServerMessage(type, messageKey, authContext, parameters);
}

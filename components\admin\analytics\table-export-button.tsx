'use client';

import { AlertCircle, Check, Download, FileText } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useEffect, useRef, useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  exportTableToCSV,
  generateExportFilename,
  prepareTableDataForExport,
  type TableExportOptions,
} from './table-export';

interface TableExportButtonProps {
  data: Record<string, unknown>[];
  title?: string;
  description?: string;
  queryType?: string;
  filename?: string;
  variant?: 'default' | 'ghost' | 'outline';
  size?: 'sm' | 'default' | 'lg';
  className?: string;
  disabled?: boolean;
  organisationName?: string;
  filenamePrefix?: string;
}

export function TableExportButton({
  data,
  title,
  description,
  queryType,
  filename,
  variant = 'outline',
  size = 'sm',
  className = '',
  disabled = false,
  organisationName = 'WSCCC Census Data Export',
  filenamePrefix = 'wsccc',
}: TableExportButtonProps) {
  const t = useTranslations('admin');
  // Use the improved hook instead of duplicating logic
  const { isExporting, exportStatus, exportTable } = useTableExport();

  const handleExport = async (options: Partial<TableExportOptions> = {}) => {
    if (disabled || data.length === 0) {
      return;
    }

    const metadata = {
      title,
      description,
      queryType,
      totalRecords: data.length,
    };

    const exportOptions: TableExportOptions = {
      filename:
        filename ||
        generateExportFilename(queryType, title, true, filenamePrefix),
      includeMetadata: true,
      includeTimestamp: true,
      dateFormat: 'australian',
      phoneFormat: 'formatted',
      organisationName,
      ...options,
    };

    await exportTable(data, metadata, exportOptions);
  };

  const getButtonContent = () => {
    if (isExporting) {
      return (
        <>
          <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
          <span>{t('exporting')}</span>
        </>
      );
    }

    if (exportStatus === 'success') {
      return (
        <>
          <Check className="h-4 w-4 text-green-600" />
          <span>{t('exported')}</span>
        </>
      );
    }

    if (exportStatus === 'error') {
      return (
        <>
          <AlertCircle className="h-4 w-4 text-red-600" />
          <span>{t('failed')}</span>
        </>
      );
    }

    return (
      <>
        <Download className="h-4 w-4" />
        <span>{t('exportCsv')}</span>
      </>
    );
  };

  const isDisabled = disabled || data.length === 0 || isExporting;

  // Simple button for basic export
  if (data.length <= 1000) {
    return (
      <Button
        className={`flex items-center gap-2 ${className}`}
        disabled={isDisabled}
        onClick={() => handleExport()}
        size={size}
        title={
          data.length === 0
            ? 'No data to export'
            : `Export ${data.length} records to CSV`
        }
        variant={variant}
      >
        {getButtonContent()}
      </Button>
    );
  }

  // Dropdown menu for advanced options when dealing with large datasets
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          className={`flex items-center gap-2 ${className}`}
          disabled={isDisabled}
          size={size}
          variant={variant}
        >
          {getButtonContent()}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuLabel className="flex items-center gap-2">
          <FileText className="h-4 w-4" />
          {t('exportOptions')}
        </DropdownMenuLabel>
        <DropdownMenuSeparator />

        <DropdownMenuItem
          className="flex items-center gap-2"
          onClick={() => handleExport({ includeMetadata: true })}
        >
          <Download className="h-4 w-4" />
          <div className="flex flex-col">
            <span>{t('fullExport')}</span>
            <span className="text-muted-foreground text-xs">
              All {data.length} records with metadata
            </span>
          </div>
        </DropdownMenuItem>

        <DropdownMenuItem
          className="flex items-center gap-2"
          onClick={() =>
            handleExport({
              includeMetadata: false,
              filename: generateExportFilename(
                queryType,
                title ? `${title}-data-only` : 'data-only',
                true,
                filenamePrefix
              ),
            })
          }
        >
          <FileText className="h-4 w-4" />
          <div className="flex flex-col">
            <span>{t('dataOnly')}</span>
            <span className="text-muted-foreground text-xs">
              Records without metadata headers
            </span>
          </div>
        </DropdownMenuItem>

        <DropdownMenuSeparator />

        <DropdownMenuItem
          className="flex items-center gap-2"
          onClick={() =>
            handleExport({
              dateFormat: 'iso',
              phoneFormat: 'raw',
              filename: generateExportFilename(
                queryType,
                title ? `${title}-raw` : 'raw-data',
                true,
                filenamePrefix
              ),
            })
          }
        >
          <FileText className="h-4 w-4" />
          <div className="flex flex-col">
            <span>{t('rawFormat')}</span>
            <span className="text-muted-foreground text-xs">
              ISO dates, unformatted phones
            </span>
          </div>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

// Utility component for export status display
interface ExportStatusProps {
  status: 'idle' | 'exporting' | 'success' | 'error';
  recordCount: number;
}

export function ExportStatus({ status, recordCount }: ExportStatusProps) {
  const t = useTranslations('admin');
  if (status === 'idle') {
    return null;
  }

  const getStatusContent = () => {
    switch (status) {
      case 'exporting':
        return (
          <div className="flex items-center gap-2 text-blue-600">
            <div className="h-4 w-4 animate-spin rounded-full border-2 border-blue-600 border-t-transparent" />
            <span>Exporting {recordCount} records...</span>
          </div>
        );
      case 'success':
        return (
          <div className="flex items-center gap-2 text-green-600">
            <Check className="h-4 w-4" />
            <span>Successfully exported {recordCount} records</span>
          </div>
        );
      case 'error':
        return (
          <div className="flex items-center gap-2 text-red-600">
            <AlertCircle className="h-4 w-4" />
            <span>{t('failedToExportData')}</span>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="rounded-md bg-slate-50 p-2 text-sm dark:bg-slate-800">
      {getStatusContent()}
    </div>
  );
}

// Hook for managing export state across components
export function useTableExport() {
  const [isExporting, setIsExporting] = useState(false);
  const [exportStatus, setExportStatus] = useState<
    'idle' | 'success' | 'error'
  >('idle');
  const statusTimeoutRef = useRef<number | undefined>(undefined);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (statusTimeoutRef.current) {
        clearTimeout(statusTimeoutRef.current);
      }
    };
  }, []);

  const exportTable = async (
    data: Record<string, unknown>[],
    metadata?: {
      title?: string;
      description?: string;
      queryType?: string;
      totalRecords?: number;
    },
    options: TableExportOptions = {}
  ) => {
    // Clear any existing timeout
    if (statusTimeoutRef.current) {
      clearTimeout(statusTimeoutRef.current);
    }

    setIsExporting(true);
    setExportStatus('idle');

    try {
      const exportData = prepareTableDataForExport(data, metadata);
      exportTableToCSV(exportData, options);
      setExportStatus('success');

      // Auto-reset with cleanup tracking
      statusTimeoutRef.current = setTimeout(
        () => setExportStatus('idle'),
        2000
      ) as unknown as number;
    } catch (_error) {
      setExportStatus('error');
      statusTimeoutRef.current = setTimeout(
        () => setExportStatus('idle'),
        3000
      ) as unknown as number;
    } finally {
      setIsExporting(false);
    }
  };

  return {
    isExporting,
    exportStatus,
    exportTable,
    resetStatus: () => {
      if (statusTimeoutRef.current) {
        clearTimeout(statusTimeoutRef.current);
      }
      setExportStatus('idle');
    },
  };
}

import { type NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { getTranslations } from 'next-intl/server';
import { z } from 'zod/v4';
import { authOptions } from '@/lib/auth/auth-options';
import {
  deleteMemberWithChecks,
  getMemberDeletionInfo,
} from '@/lib/db/members';
import { getErrorMessage } from '@/lib/utils/error-handling';
import { getLocaleFromCookies } from '@/lib/utils/server-messages';
import { createBulkMemberValidationSchema } from '@/lib/validation/members';

// Simple in-memory request deduplication (for production, consider Redis)
const activeRequests = new Map<string, Promise<NextResponse>>();

/**
 * POST /api/admin/members/bulk-delete
 *
 * Deletes multiple members with validation
 * Only accessible to admin users
 */
export async function POST(request: NextRequest) {
  // Get locale from centralized utility at the beginning
  const locale = await getLocaleFromCookies();
  const t = await getTranslations({ locale, namespace: 'errors' });

  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Require admin authentication
    if (!session || session.user.role !== 'admin') {
      return NextResponse.json({ error: t('unauthorized') }, { status: 401 });
    }

    // Parse and validate request body using factory function
    const body = await request.json();
    const bulkDeleteSchema = await createBulkMemberValidationSchema(locale);
    const validatedData = bulkDeleteSchema.parse(body);
    const { memberIds } = validatedData;

    // Create a unique key for this request to prevent duplicates
    const requestKey = `bulk-delete-${session.user.id}-${memberIds.sort().join(',')}`;

    // Check if this exact request is already being processed
    if (activeRequests.has(requestKey)) {
      if (process.env.NODE_ENV === 'development') {
      }
      return await activeRequests.get(requestKey)!;
    }

    // Create and store the promise for this request
    const requestPromise = processBulkDelete(
      memberIds,
      t,
      locale as 'en' | 'zh-CN'
    );
    activeRequests.set(requestKey, requestPromise);

    try {
      const result = await requestPromise;
      return result;
    } finally {
      // Clean up the active request
      activeRequests.delete(requestKey);
    }
  } catch (error) {
    // Handle validation errors (reuse locale and t from function scope)
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: t('invalidRequestData'),
          details: error.issues,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        error: t('failedToProcessBulkDelete'),
        details: getErrorMessage(error),
      },
      { status: 500 }
    );
  }
}

/**
 * Process the actual bulk delete operation
 */
async function processBulkDelete(
  memberIds: number[],
  t: any,
  locale: 'en' | 'zh-CN'
): Promise<NextResponse> {
  // Get additional translation namespaces for error messages
  const tErrors = await getTranslations({ locale, namespace: 'errors' });
  try {
    if (process.env.NODE_ENV === 'development') {
    }
    const _startTime = Date.now();

    // Validate each member and collect deletion info
    const memberValidations = [];
    const undeletableMembers = [];

    for (const memberId of memberIds) {
      try {
        const deletionInfo = await getMemberDeletionInfo(memberId);
        memberValidations.push({ memberId, deletionInfo });

        if (!deletionInfo.canDelete) {
          const tDialogs = await getTranslations({
            locale,
            namespace: 'dialogs',
          });
          undeletableMembers.push({
            id: memberId,
            reason:
              deletionInfo.warningMessage || tDialogs('cannotDeleteMember'),
          });
        }
      } catch (_error) {
        undeletableMembers.push({
          id: memberId,
          reason: tErrors('memberNotFound'),
        });
      }
    }

    // If there are undeletable members, return error with details
    if (undeletableMembers.length > 0) {
      const tAdmin = await getTranslations({
        locale: locale as 'en' | 'zh-CN',
        namespace: 'admin',
      });
      return NextResponse.json(
        {
          error: t('someMembersCannotBeDeleted'),
          undeletableMembers,
          message: tAdmin('reviewMembersCannotDelete'),
        },
        { status: 400 }
      );
    }

    // Perform deletions
    const results = [];
    const errors = [];

    for (const { memberId, deletionInfo } of memberValidations) {
      try {
        await deleteMemberWithChecks(memberId);
        results.push({
          id: memberId,
          deleted: true,
          type: deletionInfo.deleteType,
        });
      } catch (error) {
        // Handle specific Prisma errors more gracefully
        let errorMessage = tErrors('unknownError');
        if (error instanceof Error) {
          if (
            error.message.includes('P2025') ||
            error.message.includes('No record was found')
          ) {
            results.push({
              id: memberId,
              deleted: true,
              type: deletionInfo.deleteType,
            });
            continue;
          }
          errorMessage = error.message;
        }

        errors.push({
          id: memberId,
          error: errorMessage,
        });
      }
    }
    const _endTime = Date.now();

    // Determine response based on results
    const successCount = results.length;
    const errorCount = errors.length;

    if (errorCount === 0) {
      // All deletions successful
      const householdDeletions = results.filter(
        (r) => r.type === 'head_last_member'
      ).length;
      const memberDeletions = results.filter(
        (r) => r.type === 'regular'
      ).length;

      // Success message handled client-side for consistency with other admin operations
      return NextResponse.json({
        success: true,
        results,
        summary: {
          total: memberIds.length,
          successful: successCount,
          failed: 0,
          householdsDeleted: householdDeletions,
          membersDeleted: memberDeletions,
        },
      });
    }
    if (successCount === 0) {
      // All deletions failed
      return NextResponse.json(
        {
          error: t('failedToDeleteAnyMembers'),
          errors,
          summary: {
            total: memberIds.length,
            successful: 0,
            failed: errorCount,
          },
        },
        { status: 500 }
      );
    }
    // Partial success
    const householdDeletions = results.filter(
      (r) => r.type === 'head_last_member'
    ).length;
    const memberDeletions = results.filter((r) => r.type === 'regular').length;

    // Partial success message handled client-side for consistency with other admin operations
    return NextResponse.json({
      success: true,
      results,
      errors,
      summary: {
        total: memberIds.length,
        successful: successCount,
        failed: errorCount,
        householdsDeleted: householdDeletions,
        membersDeleted: memberDeletions,
      },
    });
  } catch (error) {
    return NextResponse.json(
      {
        error: t('failedToProcessBulkDelete'),
        details: getErrorMessage(error),
      },
      { status: 500 }
    );
  }
}

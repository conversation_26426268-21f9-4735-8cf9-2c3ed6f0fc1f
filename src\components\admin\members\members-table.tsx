'use client';

import {
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  ChevronUp,
  Eye,
  MoreVertical,
  SquarePen,
  Trash2,
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useEffect, useState } from 'react';
import { BulkDeleteDialog } from '@/components/admin/members/bulk-delete-dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
} from '@/components/ui/pagination';
import { Skeleton } from '@/components/ui/skeleton';
import { StatusBadge } from '@/components/ui/status-badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { formatDate } from '@/lib/utils/date-time';
import { highlightText } from '@/lib/utils/highlight-text';
import {
  getGenderBadgeColor,
  getRelationshipBadgeVariant,
} from '@/lib/utils/relationship-badge';
import type { ICensusYear, IMemberWithDetails } from '@/types';

interface PaginationState {
  page: number;
  pageSize: number;
  total: number;
  totalPages: number;
}

interface MembersTableProps {
  members: IMemberWithDetails[];
  loading: boolean;
  pagination: PaginationState;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
  activeCensusYear: ICensusYear | null;
  searchTerm?: string;
  isDeletingMembers?: boolean;
  onPageChange: (page: number) => void;
  onPageSizeChange: (pageSize: number) => void;
  onSortChange: (column: string) => void;
  onView: (member: IMemberWithDetails) => void;
  onEdit: (member: IMemberWithDetails) => void;
  onDeleteSelected: (memberIds: number[]) => void;
}

// Helper function to calculate age from date of birth
const calculateAge = (
  dateOfBirth: string | null,
  tCommon: any
): number | string => {
  if (!dateOfBirth) return tCommon('unknown');

  const today = new Date();
  const birthDate = new Date(dateOfBirth);

  // Check if the date is valid
  if (isNaN(birthDate.getTime())) return tCommon('unknown');

  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();

  if (
    monthDiff < 0 ||
    (monthDiff === 0 && today.getDate() < birthDate.getDate())
  ) {
    age--;
  }

  return age;
};

export function MembersTable({
  members,
  loading,
  pagination,
  sortBy,
  sortOrder,
  searchTerm = '',
  isDeletingMembers = false,
  onPageChange,
  onPageSizeChange,
  onSortChange,
  onView,
  onEdit,
  onDeleteSelected,
}: MembersTableProps) {
  const t = useTranslations('admin');
  const tCommon = useTranslations('common');
  const tEmptyStates = useTranslations('emptyStates');
  const [selectedMembers, setSelectedMembers] = useState<number[]>([]);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [pageInputValue, setPageInputValue] = useState(
    pagination.page.toString()
  );

  // Handle page input change
  const handlePageInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPageInputValue(e.target.value);
  };

  // Handle go to page
  const handleGoToPage = () => {
    const pageNumber = Number.parseInt(pageInputValue);
    if (
      !isNaN(pageNumber) &&
      pageNumber >= 1 &&
      pageNumber <= pagination.totalPages
    ) {
      onPageChange(pageNumber);
    } else {
      // Reset input to current page if invalid
      setPageInputValue(pagination.page.toString());
    }
  };

  // Update page input when pagination changes
  useEffect(() => {
    setPageInputValue(pagination.page.toString());
  }, [pagination.page]);

  // Handle selection of all members
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      // Select all members on current page
      const allMemberIds = members
        .map((member) => member.memberId)
        .filter((id) => id != null);
      setSelectedMembers(allMemberIds);
    } else {
      // Deselect all members
      setSelectedMembers([]);
    }
  };

  // Handle selection of a single member
  const handleSelectMember = (memberId: number, checked: boolean) => {
    if (checked) {
      setSelectedMembers((prev) => [...prev, memberId]);
    } else {
      setSelectedMembers((prev) => prev.filter((id) => id !== memberId));
    }
  };

  // Handle deletion of selected members
  const handleDeleteSelected = () => {
    setIsDeleteDialogOpen(true);
  };

  // Confirm deletion of selected members
  const confirmDeleteSelected = () => {
    onDeleteSelected(selectedMembers);
    setIsDeleteDialogOpen(false);
    setSelectedMembers([]);
  };

  // Check if all visible members are selected
  const allVisibleSelected =
    members.length > 0 &&
    members.every(
      (member) => member.memberId && selectedMembers.includes(member.memberId)
    );

  // Render sort icon (updated to match household table behaviour)
  const renderSortIcon = (column: string) => {
    if (sortBy !== column) {
      return (
        <div className="ml-0.5 flex flex-col">
          <ChevronUp className="h-3 w-3 text-muted-foreground" />
          <ChevronDown className="-mt-1 h-3 w-3 text-muted-foreground" />
        </div>
      );
    }

    return sortOrder === 'asc' ? (
      <ChevronUp className="ml-0.5 h-4 w-4 text-primary" />
    ) : (
      <ChevronDown className="ml-0.5 h-4 w-4 text-primary" />
    );
  };

  return (
    <div className="space-y-6">
      {/* Modern header section */}
      <div className="flex flex-col items-start justify-between gap-2 pb-2 sm:flex-row sm:items-center">
        <div className="space-y-1">
          <h2 className="font-semibold text-xl tracking-tight">
            {t('members')}
          </h2>
          <p className="text-muted-foreground text-sm">
            {pagination.total}{' '}
            {pagination.total === 1 ? t('member') : t('members')}{' '}
            {tCommon('found')}
          </p>
        </div>
        {selectedMembers.length > 0 && (
          <Button
            className="cursor-pointer"
            disabled={isDeletingMembers}
            onClick={handleDeleteSelected}
            size="sm"
            variant="destructive"
          >
            {isDeletingMembers ? (
              <>
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                {tCommon('deleting')}...
              </>
            ) : (
              <>
                <Trash2 className="mr-2 h-4 w-4" />
                {t('deleteSelected')} ({selectedMembers.length})
              </>
            )}
          </Button>
        )}
      </div>

      {/* Table section */}
      <div className="space-y-4">
        {/* Table */}
        <div className="rounded-lg border border-border/50 bg-background/50 backdrop-blur-sm">
          <Table>
            <TableHeader>
              <TableRow className="border-b">
                <TableHead className="w-12">
                  <Checkbox
                    aria-label={t('selectAllMembers')}
                    checked={allVisibleSelected}
                    className="cursor-pointer"
                    onCheckedChange={handleSelectAll}
                  />
                </TableHead>
                <TableHead
                  className="cursor-pointer text-center hover:bg-muted/50"
                  onClick={() => onSortChange('memberId')}
                >
                  <div className="flex items-center justify-center">
                    {tCommon('id')}
                    {renderSortIcon('memberId')}
                  </div>
                </TableHead>
                <TableHead
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => onSortChange('firstName')}
                >
                  <div className="flex items-center">
                    {tCommon('name')}
                    {renderSortIcon('firstName')}
                  </div>
                </TableHead>
                <TableHead
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => onSortChange('age')}
                >
                  <div className="flex items-center">
                    {tCommon('age')}
                    {renderSortIcon('age')}
                  </div>
                </TableHead>
                <TableHead>{tCommon('gender')}</TableHead>
                <TableHead
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => onSortChange('mobilePhone')}
                >
                  <div className="flex items-center">
                    {tCommon('mobile')}
                    {renderSortIcon('mobilePhone')}
                  </div>
                </TableHead>
                <TableHead
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => onSortChange('suburb')}
                >
                  <div className="flex items-center">
                    {t('household')}
                    {renderSortIcon('suburb')}
                  </div>
                </TableHead>
                <TableHead>{tCommon('relationship')}</TableHead>
                <TableHead className="text-center">
                  {tCommon('sacraments')}
                </TableHead>
                <TableHead className="text-center">{t('censusYear')}</TableHead>
                <TableHead className="text-center">
                  {tCommon('actions')}
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                // Loading skeleton
                Array.from({
                  length: pagination.pageSize > 5 ? 5 : pagination.pageSize,
                }).map((_, index) => (
                  <TableRow className="border-b-0" key={`loading-${index}`}>
                    <TableCell>
                      <Skeleton className="h-4 w-4" />
                    </TableCell>
                    <TableCell>
                      <Skeleton className="h-4 w-12" />
                    </TableCell>
                    <TableCell>
                      <Skeleton className="h-4 w-32" />
                    </TableCell>
                    <TableCell>
                      <Skeleton className="h-4 w-12" />
                    </TableCell>
                    <TableCell>
                      <Skeleton className="h-4 w-16" />
                    </TableCell>
                    <TableCell>
                      <Skeleton className="h-4 w-24" />
                    </TableCell>
                    <TableCell>
                      <Skeleton className="h-4 w-20" />
                    </TableCell>
                    <TableCell>
                      <Skeleton className="h-4 w-16" />
                    </TableCell>
                    <TableCell>
                      <Skeleton className="h-4 w-12" />
                    </TableCell>
                    <TableCell>
                      <Skeleton className="h-4 w-16" />
                    </TableCell>
                    <TableCell>
                      <Skeleton className="h-4 w-8" />
                    </TableCell>
                  </TableRow>
                ))
              ) : members.length === 0 ? (
                <TableRow className="border-b-0" key="empty-state">
                  <TableCell className="py-8 text-center" colSpan={11}>
                    <div className="flex flex-col items-center gap-2">
                      <p className="text-muted-foreground">
                        {tCommon('noMembersFound')}
                      </p>
                      {searchTerm && (
                        <p className="text-muted-foreground text-sm">
                          {tEmptyStates('tryAdjustingSearch')}
                        </p>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ) : (
                members.map((member, index) => (
                  <TableRow
                    className="border-b-0 hover:bg-muted/50"
                    key={member.memberId || `member-${index}`}
                  >
                    <TableCell>
                      <Checkbox
                        aria-label={
                          `Select ${member.firstName || ''} ${member.lastName || ''}`.trim() ||
                          'member'
                        }
                        checked={
                          member.memberId
                            ? selectedMembers.includes(member.memberId)
                            : false
                        }
                        className="cursor-pointer"
                        onCheckedChange={(checked) =>
                          member.memberId &&
                          handleSelectMember(
                            member.memberId,
                            checked as boolean
                          )
                        }
                      />
                    </TableCell>
                    <TableCell className="text-center">
                      {member.memberId}
                    </TableCell>
                    <TableCell className="font-medium">
                      {searchTerm
                        ? highlightText(
                            `${member.firstName || ''} ${member.lastName || ''}`.trim(),
                            searchTerm
                          )
                        : `${member.firstName || ''} ${member.lastName || ''}`.trim()}
                    </TableCell>
                    <TableCell>
                      <span
                        title={
                          member.dateOfBirth
                            ? formatDate(new Date(member.dateOfBirth))
                            : tCommon('unknown')
                        }
                      >
                        {calculateAge(member.dateOfBirth, tCommon)}
                      </span>
                    </TableCell>
                    <TableCell>
                      <Badge
                        className={getGenderBadgeColor(
                          member.gender || 'other'
                        )}
                      >
                        {member.gender
                          ? member.gender.charAt(0).toUpperCase() +
                            member.gender.slice(1)
                          : tCommon('unknown')}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {searchTerm
                        ? highlightText(member.mobilePhone || '', searchTerm)
                        : member.mobilePhone || ''}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <span className="text-sm">
                          {searchTerm
                            ? highlightText(
                                member.suburb || tCommon('unknown'),
                                searchTerm
                              )
                            : member.suburb || tCommon('unknown')}
                        </span>
                        <span className="text-muted-foreground text-xs">
                          (#{member.householdId || 'N/A'})
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <StatusBadge
                        variant={getRelationshipBadgeVariant(
                          member.relationship || 'other'
                        )}
                      >
                        {member.relationship
                          ? member.relationship.charAt(0).toUpperCase() +
                            member.relationship.slice(1)
                          : tCommon('unknown')}
                      </StatusBadge>
                    </TableCell>
                    <TableCell className="text-center">
                      <Badge
                        variant={
                          (member.sacramentCount || 0) > 0
                            ? 'default'
                            : 'secondary'
                        }
                      >
                        {member.sacramentCount || 0}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-center">
                      {member.census_year || 'N/A'}
                    </TableCell>
                    <TableCell className="text-center">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            aria-label={t('openActionsMenu')}
                            className="h-8 w-8 cursor-pointer"
                            size="icon"
                            variant="ghost"
                          >
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            className="cursor-pointer"
                            onClick={() => onView(member)}
                          >
                            <Eye className="mr-2 h-4 w-4" />
                            {tCommon('view')}
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            className="cursor-pointer"
                            onClick={() => onEdit(member)}
                          >
                            <SquarePen className="mr-2 h-4 w-4" />
                            {tCommon('edit')}
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>

        {/* Mobile-responsive pagination with rows per page selector */}
        {pagination.total > 0 && (
          <div className="w-full space-y-4 px-4 py-4 sm:flex sm:items-center sm:justify-between sm:space-y-0">
            {/* Rows per page selector */}
            <div className="flex items-center justify-center gap-2 sm:justify-start">
              <Label
                className="hidden font-medium text-sm sm:block"
                htmlFor="pageSize"
              >
                {tCommon('rows')}:
              </Label>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    className="flex h-9 items-center gap-2 bg-background/50 px-3 transition-colors hover:bg-background/80"
                    size="sm"
                    variant="outline"
                  >
                    {pagination.pageSize}
                    <ChevronDown className="h-4 w-4 opacity-70" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start">
                  <DropdownMenuItem
                    className="cursor-pointer"
                    onClick={() => onPageSizeChange(10)}
                  >
                    10
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    className="cursor-pointer"
                    onClick={() => onPageSizeChange(20)}
                  >
                    20
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    className="cursor-pointer"
                    onClick={() => onPageSizeChange(50)}
                  >
                    50
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    className="cursor-pointer"
                    onClick={() => onPageSizeChange(100)}
                  >
                    100
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            {/* Pagination controls */}
            <div className="flex justify-center sm:flex-1 sm:justify-center">
              <Pagination>
                <PaginationContent className="flex-wrap justify-center">
                  {/* First page button */}
                  <PaginationItem>
                    <Button
                      className="h-8 w-8"
                      disabled={pagination.page === 1}
                      onClick={() => onPageChange(1)}
                      size="icon"
                      title={tCommon('firstPage')}
                      variant="outline"
                    >
                      <span className="sr-only">{tCommon('firstPage')}</span>
                      <ChevronsLeft className="h-4 w-4" />
                    </Button>
                  </PaginationItem>

                  {/* Previous page button */}
                  <PaginationItem>
                    <Button
                      className="h-8 w-8"
                      disabled={pagination.page === 1}
                      onClick={() =>
                        onPageChange(Math.max(1, pagination.page - 1))
                      }
                      size="icon"
                      title={tCommon('previousPage')}
                      variant="outline"
                    >
                      <span className="sr-only">{tCommon('previousPage')}</span>
                      <ChevronLeft className="h-4 w-4" />
                    </Button>
                  </PaginationItem>

                  {/* Page input and info */}
                  <PaginationItem>
                    <div className="flex items-center gap-1 sm:gap-2">
                      <Input
                        className="h-8 w-10 text-center text-xs sm:w-12 sm:text-sm"
                        onChange={handlePageInputChange}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            handleGoToPage();
                          }
                        }}
                        type="text"
                        value={pageInputValue}
                      />
                      <span className="whitespace-nowrap text-muted-foreground text-xs sm:text-sm">
                        of {pagination.totalPages}
                      </span>
                      <Button
                        className="h-8 px-2 text-xs sm:px-3 sm:text-sm"
                        onClick={handleGoToPage}
                        size="sm"
                        variant="outline"
                      >
                        Go
                      </Button>
                    </div>
                  </PaginationItem>

                  {/* Next page button */}
                  <PaginationItem>
                    <Button
                      className="h-8 w-8"
                      disabled={pagination.page === pagination.totalPages}
                      onClick={() =>
                        onPageChange(
                          Math.min(pagination.totalPages, pagination.page + 1)
                        )
                      }
                      size="icon"
                      title={tCommon('nextPage')}
                      variant="outline"
                    >
                      <span className="sr-only">{tCommon('nextPage')}</span>
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </PaginationItem>

                  {/* Last page button */}
                  <PaginationItem>
                    <Button
                      className="h-8 w-8"
                      disabled={pagination.page === pagination.totalPages}
                      onClick={() => onPageChange(pagination.totalPages)}
                      size="icon"
                      title={tCommon('lastPage')}
                      variant="outline"
                    >
                      <span className="sr-only">{tCommon('lastPage')}</span>
                      <ChevronsRight className="h-4 w-4" />
                    </Button>
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          </div>
        )}
      </div>

      {/* Bulk delete dialogue */}
      <BulkDeleteDialog
        memberIds={selectedMembers}
        onMembersDeleted={confirmDeleteSelected}
        onOpenChange={setIsDeleteDialogOpen}
        open={isDeleteDialogOpen}
      />
    </div>
  );
}

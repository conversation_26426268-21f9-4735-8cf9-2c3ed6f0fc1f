'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect } from 'react';
import { useMessage } from '@/hooks/useMessage';

/**
 * Component that handles URL parameters and converts them to toast notifications
 * This is used to handle legacy URL parameters like ?reason=unauthenticated
 * Now uses the centralized message system with automatic translation
 */
export function AdminUrlParamHandler() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const { showError } = useMessage();

  useEffect(() => {
    // Check if there's a reason parameter in the URL
    const reason = searchParams?.get('reason');

    if (reason) {
      // Show translated error message using centralized system
      // Context is automatically detected as 'admin' for this component
      showError(reason, 'auth');

      // Remove the reason parameter from the URL (replace with clean URL)
      // This prevents the toast from showing again on refresh
      router.replace('/admin/login');
    }
  }, [searchParams, router, showError]); // showError is stable due to useCallback memoization

  // This component doesn't render anything
  return null;
}

/**
 * Member Distribution Queries Module
 *
 * Contains all distribution query functions related to member data:
 * - Hobby distributions
 * - Occupation distributions
 * - Gender distributions
 * - Age distributions
 */

import { prisma } from '@/lib/db/prisma';
import type { DistributionTableData, QueryFilters } from '@/types/analytics';

// Hobby distribution table generator - PostgreSQL optimized
export async function getHobbyDistributionTable(
  filters: QueryFilters
): Promise<DistributionTableData> {
  // 2025 Best Practice: Use PostgreSQL aggregation with conditional filtering
  let hobbyStats: Array<{ hobby: string; count: bigint; total_count: bigint }>;

  if (filters.hobby && filters.hobby !== 'all') {
    // Filtered hobby query
    hobbyStats = await prisma.$queryRaw<
      Array<{ hobby: string; count: bigint; total_count: bigint }>
    >`
      SELECT
        COALESCE(hobby, 'Unknown') as hobby,
        COUNT(*) as count,
        SUM(COUNT(*)) OVER() as total_count
      FROM members
      WHERE hobby IS NOT NULL
        AND hobby ILIKE ${`%${filters.hobby}%`}
      GROUP BY hobby
      ORDER BY count DESC
    `;
  } else {
    // All hobbies query
    hobbyStats = await prisma.$queryRaw<
      Array<{ hobby: string; count: bigint; total_count: bigint }>
    >`
      SELECT
        COALESCE(hobby, 'Unknown') as hobby,
        COUNT(*) as count,
        SUM(COUNT(*)) OVER() as total_count
      FROM members
      WHERE hobby IS NOT NULL
      GROUP BY hobby
      ORDER BY count DESC
    `;
  }

  if (hobbyStats.length === 0) {
    return {
      data: [],
      queryType: 'hobby_distribution',
      title: 'Member Hobby Distribution',
      totalRecords: 0,
    };
  }

  const totalCount = Number(hobbyStats[0].total_count);

  return {
    data: hobbyStats.map((stat) => ({
      hobby: stat.hobby,
      count: Number(stat.count),
      percentage:
        totalCount > 0
          ? `${((Number(stat.count) / totalCount) * 100).toFixed(1)}%`
          : '0.0%',
    })),
    queryType: 'hobby_distribution',
    title: 'Member Hobby Distribution',
    totalRecords: hobbyStats.length,
  };
}

// Occupation distribution table generator - PostgreSQL optimized
export async function getOccupationDistributionTable(
  filters: QueryFilters
): Promise<DistributionTableData> {
  // 2025 Best Practice: Use PostgreSQL aggregation with conditional filtering
  let occupationStats: Array<{
    occupation: string;
    count: bigint;
    total_count: bigint;
  }>;

  if (filters.occupation && filters.occupation !== 'all') {
    // Filtered occupation query
    occupationStats = await prisma.$queryRaw<
      Array<{ occupation: string; count: bigint; total_count: bigint }>
    >`
      SELECT
        COALESCE(occupation, 'Unknown') as occupation,
        COUNT(*) as count,
        SUM(COUNT(*)) OVER() as total_count
      FROM members
      WHERE occupation IS NOT NULL
        AND occupation ILIKE ${`%${filters.occupation}%`}
      GROUP BY occupation
      ORDER BY count DESC
    `;
  } else {
    // All occupations query
    occupationStats = await prisma.$queryRaw<
      Array<{ occupation: string; count: bigint; total_count: bigint }>
    >`
      SELECT
        COALESCE(occupation, 'Unknown') as occupation,
        COUNT(*) as count,
        SUM(COUNT(*)) OVER() as total_count
      FROM members
      WHERE occupation IS NOT NULL
      GROUP BY occupation
      ORDER BY count DESC
    `;
  }

  if (occupationStats.length === 0) {
    return {
      data: [],
      queryType: 'occupation_distribution',
      title: 'Member Occupation Distribution',
      totalRecords: 0,
    };
  }

  const totalCount = Number(occupationStats[0].total_count);

  return {
    data: occupationStats.map((stat) => ({
      occupation: stat.occupation,
      count: Number(stat.count),
      percentage:
        totalCount > 0
          ? `${((Number(stat.count) / totalCount) * 100).toFixed(1)}%`
          : '0.0%',
    })),
    queryType: 'occupation_distribution',
    title: 'Member Occupation Distribution',
    totalRecords: occupationStats.length,
  };
}

// Gender distribution table generator - PostgreSQL optimized
export async function getGenderDistributionTable(): Promise<DistributionTableData> {
  // 2025 Best Practice: Single PostgreSQL query for aggregation and total
  const genderStats = await prisma.$queryRaw<
    Array<{
      gender: string;
      count: bigint;
      total_count: bigint;
    }>
  >`
    SELECT
      COALESCE(gender, 'Unknown') as gender,
      COUNT(*) as count,
      SUM(COUNT(*)) OVER() as total_count
    FROM members
    GROUP BY gender
    ORDER BY count DESC
  `;

  if (genderStats.length === 0) {
    return {
      data: [],
      queryType: 'gender_distribution',
      title: 'Member Gender Distribution',
      totalRecords: 0,
    };
  }

  const totalCount = Number(genderStats[0].total_count);

  return {
    data: genderStats.map((stat) => ({
      gender: stat.gender,
      count: Number(stat.count),
      percentage:
        totalCount > 0
          ? `${((Number(stat.count) / totalCount) * 100).toFixed(1)}%`
          : '0.0%',
    })),
    queryType: 'gender_distribution',
    title: 'Member Gender Distribution',
    totalRecords: genderStats.length,
  };
}

// Age distribution table generator - PostgreSQL optimized
export async function getAgeDistributionTable(): Promise<DistributionTableData> {
  // 2025 Best Practice: Use PostgreSQL aggregation for performance
  const ageGroups = await prisma.$queryRaw<
    Array<{ age_group: string; count: bigint }>
  >`
    SELECT
      CASE
        WHEN EXTRACT(YEAR FROM AGE(date_of_birth)) < 18 THEN 'Under 18'
        WHEN EXTRACT(YEAR FROM AGE(date_of_birth)) BETWEEN 18 AND 30 THEN '18-30'
        WHEN EXTRACT(YEAR FROM AGE(date_of_birth)) BETWEEN 31 AND 50 THEN '31-50'
        WHEN EXTRACT(YEAR FROM AGE(date_of_birth)) BETWEEN 51 AND 70 THEN '51-70'
        WHEN EXTRACT(YEAR FROM AGE(date_of_birth)) > 70 THEN 'Over 70'
        ELSE 'Unknown'
      END as age_group,
      COUNT(*) as count
    FROM members
    WHERE date_of_birth IS NOT NULL
    GROUP BY age_group
    ORDER BY
      CASE age_group
        WHEN 'Under 18' THEN 1
        WHEN '18-30' THEN 2
        WHEN '31-50' THEN 3
        WHEN '51-70' THEN 4
        WHEN 'Over 70' THEN 5
        ELSE 6
      END
  `;

  if (ageGroups.length === 0) {
    return {
      data: [],
      queryType: 'age_distribution',
      title: 'Member Age Distribution',
      totalRecords: 0,
    };
  }

  const totalCount = ageGroups.reduce(
    (sum, group) => sum + Number(group.count),
    0
  );

  return {
    data: ageGroups.map((group) => ({
      age_group: group.age_group,
      count: Number(group.count),
      percentage:
        totalCount > 0
          ? `${((Number(group.count) / totalCount) * 100).toFixed(1)}%`
          : '0.0%',
    })),
    queryType: 'age_distribution',
    title: 'Member Age Distribution',
    totalRecords: ageGroups.length,
  };
}

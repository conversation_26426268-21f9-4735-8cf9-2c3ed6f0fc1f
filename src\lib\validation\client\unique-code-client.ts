import { z } from 'zod/v4';

/**
 * Client-side validation schemas for unique code forms
 * These schemas use translation functions for user-facing error messages
 * Use with useTranslations('validation') hook in client components
 */

/**
 * Create unique code generation form validation schema with translations
 * @param t - Translation function from useTranslations('validation')
 */
export function createClientGenerateUniqueCodesSchema(t: any) {
  return z.object({
    count: z.union([
      z
        .number()
        .int({ error: t('codeCountInteger') })
        .min(1, { error: t('codeCountMinimum') })
        .max(1000, { error: t('codeCountMaximum') }),
      z
        .string()
        .regex(/^\d+$/, { error: t('codeCountValidNumber') })
        .transform((val) => Number.parseInt(val, 10))
        .refine((val) => val >= 1 && val <= 1000, {
          error: t('codeCountRange'),
        }),
    ]),
    censusYearId: z.union([
      z
        .number()
        .int({ error: t('censusYearIdInteger') })
        .positive({ error: t('censusYearIdPositive') }),
      z
        .string()
        .regex(/^\d+$/, { error: t('censusYearIdValidNumber') })
        .transform((val) => Number.parseInt(val, 10))
        .refine((val) => val > 0, {
          error: t('censusYearIdPositive'),
        }),
    ]),
  });
}

/**
 * Create unique code deletion form validation schema with translations
 * @param t - Translation function from useTranslations('validation')
 */
export function createClientDeleteUniqueCodesSchema(t: any) {
  return z.object({
    codeIds: z
      .array(z.number().int().positive())
      .min(1, { error: t('codeSelectionRequired') }),
  });
}

/**
 * Create unique code search form validation schema with translations
 * @param t - Translation function from useTranslations('validation')
 */
export function createClientSearchUniqueCodesSchema(t: any) {
  return z.object({
    searchTerm: z.string().optional(),
    isAssigned: z.boolean().optional(),
    censusYearId: z.number().int().positive().optional(),
    page: z.number().int().min(1).optional().default(1),
    pageSize: z.number().int().min(1).max(100).optional().default(20),
  });
}

/**
 * Create unique code input validation schema with translations (for census authentication)
 * @param t - Translation function from useTranslations('validation')
 */
export function createClientUniqueCodeInputSchema(t: any) {
  return z
    .string()
    .min(1, { error: t('codeRequired') })
    .max(50, { error: t('codeTooLong') })
    .regex(/^[a-zA-Z0-9-]+$/, { error: t('codeInvalidFormat') })
    .transform((val) => val.trim().toLowerCase());
}

// Type exports for client-side forms
export type ClientGenerateUniqueCodesFormValues = z.infer<
  ReturnType<typeof createClientGenerateUniqueCodesSchema>
>;
export type ClientDeleteUniqueCodesFormValues = z.infer<
  ReturnType<typeof createClientDeleteUniqueCodesSchema>
>;
export type ClientSearchUniqueCodesFormValues = z.infer<
  ReturnType<typeof createClientSearchUniqueCodesSchema>
>;
export type ClientUniqueCodeInputFormValues = z.infer<
  ReturnType<typeof createClientUniqueCodeInputSchema>
>;

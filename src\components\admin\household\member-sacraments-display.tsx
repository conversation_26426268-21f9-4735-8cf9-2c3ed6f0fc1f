'use client';

import {
  Ch<PERSON>ronDown,
  <PERSON>,
  Crown,
  Droplets,
  FileText,
  Gift,
  Hand,
  Heart,
  Sparkles,
  Wheat,
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useEffect, useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { formatDate } from '@/lib/utils/date-time';

interface Sacrament {
  id: number;
  memberId: number;
  sacramentTypeId: number;
  date: string | null;
  place: string | null;
  notes: string | null;
  censusYearId: number;
  sacrament_name?: string;
  sacrament_code?: string;
  sacrament_description?: string;
  census_year?: number;
  created_at?: string;
  updated_at?: string;
}

interface MemberSacramentsDisplayProps {
  memberId: number;
  memberName: string;
}

// Sacrament icon mapping - consistent with dashboard
const getSacramentIcon = (code: string | undefined | null) => {
  if (!code) {
    return <FileText className="h-4 w-4" />;
  }

  switch (code.toLowerCase()) {
    case 'baptism':
      return <Droplets className="h-4 w-4" />;
    case 'confirmation':
      return <Hand className="h-4 w-4" />;
    case 'communion':
      return <Wheat className="h-4 w-4" />;
    case 'matrimony':
      return <Heart className="h-4 w-4" />;
    case 'holy_orders':
      return <Crown className="h-4 w-4" />;
    case 'anointing':
      return <Gift className="h-4 w-4" />;
    default:
      return <FileText className="h-4 w-4" />;
  }
};

// Sacrament color mapping - using your project's color scheme
const getSacramentColor = (code: string | undefined | null) => {
  if (!code) {
    return 'bg-slate-50 text-slate-700 border-slate-200 dark:bg-slate-950 dark:text-slate-300 dark:border-slate-800';
  }

  switch (code.toLowerCase()) {
    case 'baptism':
      return 'bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-300 dark:border-blue-800';
    case 'confirmation':
      return 'bg-red-50 text-red-700 border-red-200 dark:bg-red-950 dark:text-red-300 dark:border-red-800';
    case 'communion':
      return 'bg-amber-50 text-amber-700 border-amber-200 dark:bg-amber-950 dark:text-amber-300 dark:border-amber-800';
    case 'matrimony':
      return 'bg-pink-50 text-pink-700 border-pink-200 dark:bg-pink-950 dark:text-pink-300 dark:border-pink-800';
    case 'holy_orders':
      return 'bg-indigo-50 text-indigo-700 border-indigo-200 dark:bg-indigo-950 dark:text-indigo-300 dark:border-indigo-800';
    case 'anointing':
      return 'bg-emerald-50 text-emerald-700 border-emerald-200 dark:bg-emerald-950 dark:text-emerald-300 dark:border-emerald-800';
    default:
      return 'bg-slate-50 text-slate-700 border-slate-200 dark:bg-slate-950 dark:text-slate-300 dark:border-slate-800';
  }
};

export function MemberSacramentsDisplay({
  memberId,
}: MemberSacramentsDisplayProps) {
  const t = useTranslations();
  const tSacraments = useTranslations('sacraments');
  const [sacraments, setSacraments] = useState<Sacrament[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isExpanded, setIsExpanded] = useState(false);

  useEffect(() => {
    const fetchSacraments = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch(
          `/api/admin/members/${memberId}/sacraments`
        );

        if (!response.ok) {
          throw new Error(t('errors.failedToFetchSacraments'));
        }

        const data = await response.json();
        setSacraments(data.sacraments || []);
      } catch (err) {
        console.error('Error fetching sacraments:', err);
        setError(
          err instanceof Error
            ? err.message
            : t('errors.failedToFetchSacraments')
        );
      } finally {
        setLoading(false);
      }
    };

    if (memberId) {
      fetchSacraments();
    }
  }, [memberId, t]);

  return (
    <div className="space-y-4">
      {/* Collapsible Header */}
      <div
        aria-expanded={isExpanded}
        aria-label={`${isExpanded ? 'Collapse' : 'Expand'} sacraments section`}
        className="flex cursor-pointer items-center gap-2 rounded-md border border-border p-2 transition-colors hover:bg-muted/10"
        onClick={() => setIsExpanded(!isExpanded)}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            setIsExpanded(!isExpanded);
          }
        }}
        role="button"
        tabIndex={0}
      >
        <Cross className="h-4 w-4 text-primary" />
        <h4 className="font-medium text-sm">{t('common.sacraments')}</h4>
        {loading ? (
          <Skeleton className="h-5 w-16" />
        ) : (
          <Badge className="text-xs" variant="outline">
            {sacraments.length} recorded
          </Badge>
        )}
        <ChevronDown
          className={`ml-auto h-4 w-4 text-muted-foreground transition-transform duration-200 ${
            isExpanded ? 'rotate-180' : ''
          }`}
        />
      </div>

      {/* Collapsible Content */}
      <div
        className={`overflow-hidden transition-all duration-200 ease-in-out ${
          isExpanded ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
        }`}
      >
        <div className="pt-1">
          {loading ? (
            <div className="space-y-2">
              <Skeleton className="h-16 w-full" />
              <Skeleton className="h-16 w-full" />
            </div>
          ) : error ? (
            <div className="py-4 text-muted-foreground text-sm">
              <p>{t('emptyStates.unableToLoadSacramentInfo')}</p>
            </div>
          ) : sacraments.length === 0 ? (
            <div className="py-6 text-center">
              <FileText className="mx-auto mb-2 h-8 w-8 text-muted-foreground" />
              <p className="text-muted-foreground text-sm">
                {t('emptyStates.noSacramentsRecorded')}
              </p>
            </div>
          ) : (
            <div className="grid gap-3">
              {sacraments.map((sacrament) => {
                // Build the title line with available information
                const titleParts = [
                  sacrament.sacrament_code
                    ? tSacraments(sacrament.sacrament_code as any)
                    : sacrament.sacrament_name,
                ];

                if (sacrament.date) {
                  titleParts.push(formatDate(new Date(sacrament.date)));
                }

                if (sacrament.place) {
                  titleParts.push(sacrament.place);
                }

                return (
                  <div
                    className="rounded-md bg-muted/10 p-3"
                    key={sacrament.id}
                  >
                    <div className="flex items-center gap-2 text-sm">
                      <div
                        className={`rounded-full p-1.5 ${getSacramentColor(sacrament.sacrament_code)}`}
                      >
                        {getSacramentIcon(sacrament.sacrament_code)}
                      </div>
                      <span className="font-medium">
                        {titleParts.join(' • ')}
                      </span>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

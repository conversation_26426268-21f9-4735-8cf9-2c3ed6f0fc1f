import { getTranslations } from 'next-intl/server';
import { z } from 'zod/v4';

/**
 * Server-side validation schemas for unique code management
 * These schemas now support translations using next-intl's errorMap pattern
 * For client-side validation with translations, use the client validation utilities
 */

/**
 * Create generate unique codes schema with translations
 */
export async function createGenerateUniqueCodesSchema(
  locale: 'en' | 'zh-CN' = 'en'
) {
  const t = await getTranslations({ locale, namespace: 'validation' });

  return z.object({
    count: z.union([
      z
        .number()
        .int({ error: t('codeCountInteger') })
        .min(1, { error: t('atLeast1CodeMustBeGenerated') })
        .max(1000, { error: t('codeCountMaximum') }),
      z
        .string()
        .regex(/^\d+$/, { error: t('mustBeAValidNumber') })
        .transform((val) => Number.parseInt(val, 10))
        .refine((val) => val >= 1 && val <= 1000, {
          error: t('codeCountRange'),
        }),
    ]),
    censusYearId: z.union([
      z
        .number()
        .int({ error: t('censusYearIdInteger') })
        .positive({ error: t('censusYearIdMustBePositive') }),
      z
        .string()
        .regex(/^\d+$/, { error: t('mustBeAValidNumber') })
        .transform((val) => Number.parseInt(val, 10))
        .refine((val) => val > 0, { error: t('censusYearIdMustBePositive') }),
    ]),
  });
}

/**
 * Create delete unique codes schema with translations
 */
export async function createDeleteUniqueCodesSchema(
  locale: 'en' | 'zh-CN' = 'en'
) {
  const t = await getTranslations({ locale, namespace: 'validation' });

  return z.object({
    codeIds: z
      .array(z.number().int().positive())
      .min(1, { error: t('atLeastOneCodeIdMustBeProvided') }),
  });
}

// Type exports for server-side validation
export type ServerGenerateUniqueCodesFormValues = z.infer<
  Awaited<ReturnType<typeof createGenerateUniqueCodesSchema>>
>;
export type ServerDeleteUniqueCodesFormValues = z.infer<
  Awaited<ReturnType<typeof createDeleteUniqueCodesSchema>>
>;

export const searchUniqueCodesSchema = z.object({
  searchTerm: z.string().optional(),
  isAssigned: z.boolean().optional(),
  censusYearId: z.number().int().positive().optional(),
  page: z.number().int().min(1).optional().default(1),
  pageSize: z.number().int().min(1).max(100).optional().default(20),
});

/**
 * Create unique code input schema with translations
 */
export async function createUniqueCodeInputSchema(
  locale: 'en' | 'zh-CN' = 'en'
) {
  const t = await getTranslations({ locale, namespace: 'validation' });

  return z
    .string()
    .min(1, { error: t('codeRequired') })
    .max(50, { error: t('codeTooLong') })
    .regex(/^[a-zA-Z0-9-]+$/, { error: t('codeInvalidFormat') })
    .transform((val) => val.trim().toLowerCase());
}

// Type exports for client-side forms (using existing schemas)
export type GenerateUniqueCodesFormValues = {
  count: number | string;
  censusYearId: number | string;
};
export type SearchUniqueCodesFormValues = z.infer<
  typeof searchUniqueCodesSchema
>;

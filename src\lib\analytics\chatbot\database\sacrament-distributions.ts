/**
 * Sacrament Distribution Queries Module
 *
 * Contains all distribution query functions related to sacrament data:
 * - Sacrament type distributions
 */

import { prisma } from '@/lib/db/prisma';
import type { DistributionTableData } from '@/types/analytics';

// Sacrament distribution table generator - PostgreSQL optimized
export async function getSacramentDistributionTable(): Promise<DistributionTableData> {
  // 2025 Best Practice: Single PostgreSQL query with JOIN for aggregation and total
  const sacramentStats = await prisma.$queryRaw<
    Array<{
      sacrament: string;
      count: bigint;
      total_count: bigint;
    }>
  >`
    SELECT
      COALESCE(st.name, 'Unknown') as sacrament,
      COUNT(*) as count,
      SUM(COUNT(*)) OVER() as total_count
    FROM sacraments s
    LEFT JOIN sacrament_types st ON s.sacrament_type_id = st.id
    GROUP BY st.name, st.id
    ORDER BY count DESC
  `;

  if (sacramentStats.length === 0) {
    return {
      data: [],
      queryType: 'sacrament_distribution',
      title: 'Sacrament Distribution',
      totalRecords: 0,
    };
  }

  const totalCount = Number(sacramentStats[0].total_count);

  return {
    data: sacramentStats.map((stat) => ({
      sacrament: stat.sacrament,
      count: Number(stat.count),
      percentage:
        totalCount > 0
          ? `${((Number(stat.count) / totalCount) * 100).toFixed(1)}%`
          : '0.0%',
    })),
    queryType: 'sacrament_distribution',
    title: 'Sacrament Distribution',
    totalRecords: sacramentStats.length,
  };
}

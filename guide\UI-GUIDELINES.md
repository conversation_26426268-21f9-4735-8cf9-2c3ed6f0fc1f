# WSCCC Census System UI Guidelines

## Design Philosophy

The WSCCC Census System follows modern minimalist desktop application aesthetics with efficient functionality. The design emphasises:

- **现代简约的桌面应用美学** (Modern minimalist desktop aesthetics)
- **弥散光渐变效果** (Diffused light gradient effects)
- **清晰的信息架构与直观的导航体系** (Clear information architecture with intuitive navigation)
- **舒适的色彩搭配与品牌色系和谐统一** (Comfortable colour coordination harmonised with brand colours)
- **合理的留白与内容密度平衡** (Balanced whitespace and content density)
- **流畅的用户操作流程** (Smooth user operation workflow)
- **精心设计的按钮状态与交互反馈** (Carefully designed button states and interaction feedback)
- **清晰的层级关系与视觉引导** (Clear hierarchical relationships and visual guidance)
- **符合桌面操作习惯的交互模式** (Desktop-friendly interaction patterns)
- **专业而不失亲和力的视觉表达** (Professional yet approachable visual expression)

## Colour Palette

### Primary Colours
- **Orange**: `#FF6308` - Primary brand colour for CTAs and highlights
- **Light Lavender**: `#BDC9E6` - Secondary accent colour
- **Light Blue**: `#97A4FF` - Tertiary accent colour

### System Colours
- **Background**: CSS custom properties for light/dark mode
- **Foreground**: Adaptive text colours
- **Muted**: Subtle backgrounds and borders
- **Accent**: Interactive elements
- **Destructive**: Error states and dangerous actions

### Usage Guidelines
- Use primary orange sparingly for important actions
- Light lavender for subtle highlights and backgrounds
- Light blue for informational elements
- Maintain high contrast ratios for accessibility

## Typography

### Font Stack
- **Primary**: System font stack for optimal performance
- **Monospace**: For code and technical content
- **Fallbacks**: Comprehensive fallback chain

### Hierarchy
- **H1**: Page titles and main headings
- **H2**: Section headings
- **H3**: Subsection headings
- **Body**: Regular content text
- **Small**: Secondary information
- **Muted**: Subtle text elements

## Component System

### shadcn/ui Foundation

All UI components are built on shadcn/ui with consistent styling:

#### Button Components
```typescript
// Button variants
const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground shadow hover:bg-primary/90",
        destructive: "bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",
        outline: "border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",
        secondary: "bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline",
      },
      size: {
        default: "h-9 px-4 py-2",
        sm: "h-8 rounded-md px-3 text-xs",
        lg: "h-10 rounded-md px-8",
        icon: "h-9 w-9",
      },
    },
  }
)
```

#### Form Components
- **FormField**: Wrapper for form inputs with validation
- **FormLabel**: Consistent labelling with floating label support
- **FormControl**: Input wrapper with proper accessibility
- **FormMessage**: Error and help text display

### Custom Components

#### OTP Input Component
Specialised component for TOTP codes and backup codes:

**Design Specifications**:
- OTP input fields with separator in the middle
- No hyphens inside each field
- No placeholders inside each field
- Fixed borders for separator styling
- Validation after form submission
- Left-aligned labels above fields
- Centred OTP fields
- Left-aligned validation text

#### Floating Labels
Username and password input fields use floating label pattern:
- Label starts inside input field
- Animates to top when focused or filled
- Maintains accessibility standards
- Consistent across all forms

## Layout Patterns

### Global Layout Structure

#### Root Layout
- Wraps entire application
- Includes global providers (Theme, Alert, etc.)
- Contains RootLayoutClient component
- Uses `min-h-screen` for proper height

#### Admin Portal Layout
- Persistent sidebar with collapsible design
- Top navigation bar with search and user menu
- Main content area with proper spacing
- Responsive design for different screen sizes

#### Census Portal Layout
- Clean, minimal header with translucent design
- Centred content with card-based layout
- Footer with consistent branding
- Mobile-responsive design

### Responsive Design

#### Breakpoints
- **Mobile**: < 768px
- **Tablet**: 768px - 1024px
- **Desktop**: > 1024px

#### Dialog/Drawer Pattern
- **Large screens**: Modal dialogs
- **Mobile screens**: Bottom drawers
- Consistent API across both patterns
- Proper accessibility implementation

## Form Design

### Form Structure
All forms follow consistent patterns:

```typescript
// Standard form wrapper
<Form {...form}>
  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
    <FormField
      control={form.control}
      name="fieldName"
      render={({ field }) => (
        <FormItem>
          <FormLabel>Field Label</FormLabel>
          <FormControl>
            <Input placeholder="Enter value..." {...field} />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  </form>
</Form>
```

### Validation Display
- **Real-time validation**: Client-side with React Hook Form
- **Error messages**: Clear, actionable feedback
- **Success states**: Positive confirmation
- **Loading states**: Progress indicators

### Input Components
- **Text inputs**: Consistent styling with floating labels
- **Select dropdowns**: Custom styled with proper keyboard navigation
- **Date pickers**: Calendar integration with validation
- **Checkboxes/Radio**: Proper accessibility labels
- **File uploads**: Drag-and-drop support

## Navigation Design

### Admin Sidebar
- **Collapsible design**: Space-efficient navigation
- **Visual indicators**: Current section highlighting
- **Icon + text**: Clear navigation labels
- **Responsive behaviour**: Mobile-friendly adaptation

### Breadcrumbs
- **Hierarchical navigation**: Clear path indication
- **Clickable segments**: Easy navigation back
- **Separator styling**: Consistent visual hierarchy

### Pagination
- **Table pagination**: Consistent across data tables
- **Page size options**: User-configurable
- **Navigation controls**: First, previous, next, last
- **Current page indicator**: Clear visual feedback

## Data Display

### Tables
- **Sortable headers**: Chevron indicators for sort direction
- **Row selection**: Checkbox-based selection
- **Action columns**: Consistent button placement
- **Loading states**: Skeleton loaders
- **Empty states**: Helpful messaging

### Cards
- **Consistent spacing**: Proper padding and margins
- **Shadow elevation**: Subtle depth indication
- **Hover states**: Interactive feedback
- **Content hierarchy**: Clear information structure

### Charts and Analytics
- **shadcn charts**: Consistent with design system
- **OKLCH colours**: Theme-aware colour variables
- **Responsive sizing**: Adaptive to container
- **Accessibility**: Screen reader support
- **Tooltips**: Dark theme support

## Interactive Elements

### Buttons
- **Primary actions**: Orange brand colour
- **Secondary actions**: Outline or ghost variants
- **Destructive actions**: Red colour with confirmation
- **Loading states**: Spinner indicators
- **Disabled states**: Reduced opacity

### Modals and Dialogs
- **Backdrop**: Semi-transparent overlay
- **Focus management**: Proper keyboard navigation
- **Escape handling**: Close on escape key
- **Responsive sizing**: Adaptive to screen size

### Toast Notifications

#### Centralized Alert System
The system uses a **centralized alert system** for all toast notifications with automatic translation and auth context separation.

```typescript
import { useMessage } from '@/hooks/useMessage';

export function MyComponent() {
  const { showSuccess, showError, showWarning, showInfo } = useMessage();

  const handleSubmit = async () => {
    try {
      // ... operation logic
      showSuccess('memberAdded'); // Automatic translation
    } catch (error) {
      showError('updateFailed'); // Context-aware error handling
    }
  };
}
```

#### Key Features
- **🎯 Unified Interface**: Single `useMessage()` hook for all message types
- **🔄 Automatic Translation**: Context-aware translation with locale detection
- **🔐 Auth System Separation**: Independent admin and census message systems
- **✅ 100% Coverage**: All toast messages fully translated and verified
- **🚀 Performance Optimized**: Deduplication, caching, and efficient rendering

#### Message Types
| Type | Hook Function | Usage | Color |
|------|---------------|-------|-------|
| **Success** | `showSuccess('key')` | Operation confirmations | Green |
| **Error** | `showError('key')` | Error handling | Red |
| **Warning** | `showWarning('key')` | User warnings | Yellow |
| **Info** | `showInfo('key')` | Information messages | Blue |

#### Implementation Guidelines
- ✅ Always use `useMessage()` hook for new components
- ✅ Use semantic message keys from mapping files
- ✅ Test both admin and census contexts
- ✅ Verify messages in both English and Chinese
- ❌ Don't use direct `toast.success()` or `showDirect()` calls
- ❌ Don't hardcode English messages
- ❌ Don't mix auth contexts

#### Technical Details
- **Sonner integration**: Consistent notification system with deduplication
- **Success/Error/Info/Warning**: Appropriate colour coding and icons
- **Auto-dismiss**: Configurable timeout (default: 4 seconds)
- **Positioning**: Top-right corner placement
- **Accessibility**: Screen reader support and keyboard navigation

## Accessibility

### WCAG Compliance
- **Colour contrast**: Minimum 4.5:1 ratio
- **Keyboard navigation**: Full keyboard support
- **Screen readers**: Proper ARIA labels
- **Focus indicators**: Visible focus states

### Implementation
- **Semantic HTML**: Proper element usage
- **ARIA attributes**: Enhanced accessibility
- **Alt text**: Descriptive image alternatives
- **Form labels**: Proper input associations

## Animation and Transitions

### Micro-interactions
- **Hover effects**: Subtle colour and scale changes
- **Loading states**: Smooth progress indicators
- **Page transitions**: Fade effects between routes
- **Form validation**: Smooth error state transitions

### Performance
- **CSS transitions**: Hardware-accelerated animations
- **Reduced motion**: Respect user preferences
- **Optimised rendering**: Minimal layout thrashing

## Theme System

### Light/Dark Mode
- **CSS custom properties**: Theme-aware colours
- **System preference**: Automatic detection
- **User override**: Manual theme selection
- **Persistent storage**: Remember user choice

### Implementation
```css
:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --primary: 222.2 47.4% 11.2%;
  --primary-foreground: 210 40% 98%;
}

.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --primary: 210 40% 98%;
  --primary-foreground: 222.2 47.4% 11.2%;
}
```

## Related Documentation

- [Centralized Alert System Complete Guide](./CENTRALIZED-ALERT-SYSTEM-COMPLETE.md) - Comprehensive alert system documentation
- [Translation Guide](./TRANSLATION.md) - Internationalization implementation
- [Architecture Guide](./ARCHITECTURE.md) - System architecture overview

This comprehensive UI guideline ensures consistent, accessible, and beautiful user interfaces across the entire WSCCC Census System.

'use client';

import {
  <PERSON>ert<PERSON>ircle,
  CheckCircle2,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  Clock,
  FileText,
  Plus,
  Printer,
  Search,
  SlidersHorizontal,
  Trash2,
  X,
} from 'lucide-react';
import Image from 'next/image';
import { useLocale, useTranslations } from 'next-intl';
import type React from 'react';
import { useCallback, useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { Form } from '@/components/forms/common/Form';
import { FormField } from '@/components/forms/common/FormField';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
} from '@/components/ui/pagination';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectSeparator,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Skeleton } from '@/components/ui/skeleton';
import { StatusBadge } from '@/components/ui/status-badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useAlert } from '@/contexts/AlertContext';
import { useFormSubmit } from '@/hooks/useFormSubmit';
import { useMessage } from '@/hooks/useMessage';
import { formatForDisplay } from '@/lib/utils/date-time';
import { generateUniqueCodeQR } from '@/lib/utils/qr-code';
import { getSiteUrl } from '@/lib/utils/site-url';
import { zodResolver } from '@/lib/utils/zod-resolver-compat';
import { createClientGenerateUniqueCodesSchema } from '@/lib/validation/client/unique-code-client';
import type { GenerateUniqueCodesFormValues } from '@/lib/validation/unique-code';

interface UniqueCode {
  id: number;
  code: string;
  isAssigned: boolean;
  assignedAt: string | null;
  householdId: number | null;
  censusYearId: number;
  createdAt: string;
  updatedAt: string;
  census_year: number;
  is_active_year: boolean;
}

interface PaginationInfo {
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export function UniqueCodeClient() {
  const { showSuccess, showError, showWarning } = useMessage();
  const { showAlert } = useAlert();
  const locale = useLocale();
  const t = useTranslations('admin');
  const tCommon = useTranslations('common');
  const tTables = useTranslations('tables');
  const tValidation = useTranslations('validation');
  const tEmptyStates = useTranslations('emptyStates');
  const tErrors = useTranslations('errors');
  const tNotifications = useTranslations('notifications');
  const [uniqueCodes, setUniqueCodes] = useState<UniqueCode[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCodes, setSelectedCodes] = useState<number[]>([]);
  const [isGenerateDialogOpen, setIsGenerateDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isPrintConfirmDialogOpen, setIsPrintConfirmDialogOpen] =
    useState(false);
  const [selectedCode, setSelectedCode] = useState<UniqueCode | null>(null);
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');
  const [churchName, setChurchName] = useState<string>('WSCCC Church');
  const [isCardLoading, setIsCardLoading] = useState(false);
  const [cardError, setCardError] = useState<string | null>(null);
  const [codesToPrint, setCodesToPrint] = useState<UniqueCode[]>([]);
  const [pagination, setPagination] = useState<PaginationInfo>({
    total: 0,
    page: 1,
    pageSize: 20,
    totalPages: 0,
  });
  const [pageInputValue, setPageInputValue] = useState<string>('1');
  const [filterAssigned, setFilterAssigned] = useState<boolean | undefined>(
    undefined
  );
  const [filterCensusYearId, setFilterCensusYearId] = useState<number | null>(
    null
  );

  // Create client-side validation schema with translations
  const generateUniqueCodesSchema = createClientGenerateUniqueCodesSchema(
    (key: string) => tValidation(key as any)
  );

  // Form for generating unique codes
  const generateForm = useForm<GenerateUniqueCodesFormValues>({
    resolver: zodResolver(generateUniqueCodesSchema),
    defaultValues: {
      count: 10,
      censusYearId: 1, // Default to 1, will be updated when component mounts
    },
  });

  // State for census years
  const [censusYears, setCensusYears] = useState<
    { id: number; year: number; isActive: boolean }[]
  >([]);

  // Fetch census years for the generate form
  const fetchCensusYears = useCallback(async () => {
    try {
      // Get all census years
      const response = await fetch('/api/census-years');
      if (response.ok) {
        const data = await response.json();
        setCensusYears(data);

        // Find the active census year
        const activeYear = data.find(
          (year: { id: number; year: number; isActive: boolean }) =>
            year.isActive
        );
        if (activeYear) {
          setFilterCensusYearId(activeYear.id); // Set default filter to active year
          generateForm.setValue('censusYearId', activeYear.id);
          // Environment-aware logging - only in development
          if (process.env.NODE_ENV === 'development') {
          }
        } else if (data.length > 0) {
          // If no active year, use the most recent one
          const mostRecentYear = data.sort(
            (a: { year: number }, b: { year: number }) => b.year - a.year
          )[0];
          setFilterCensusYearId(mostRecentYear.id); // Set default filter to most recent year
          generateForm.setValue('censusYearId', mostRecentYear.id);
          if (process.env.NODE_ENV === 'development') {
          }
        } else {
          // If no census years at all, use default
          if (process.env.NODE_ENV === 'development') {
          }
          generateForm.setValue('censusYearId', 1);
        }
        return;
      }

      // Fallback to active year endpoint if all census years fails
      const activeResponse = await fetch('/api/census-years/active');
      if (activeResponse.ok) {
        const activeData = await activeResponse.json();
        if (activeData?.id) {
          setFilterCensusYearId(activeData.id);
          generateForm.setValue('censusYearId', activeData.id);
          if (process.env.NODE_ENV === 'development') {
          }
          return;
        }
      }
      const settingsResponse = await fetch('/api/settings/census-year');
      if (settingsResponse.ok) {
        const _settingsData = await settingsResponse.json();
      }
    } catch (_error) {
      showError('failedToFetchCensusYears');
    }
  }, [generateForm, showError]); // showError is stable due to useCallback memoization

  // Fetch unique codes with pagination and filtering
  const fetchUniqueCodes = useCallback(async () => {
    try {
      setLoading(true);

      // Build query parameters
      const params = new URLSearchParams();
      if (searchTerm) {
        params.append('searchTerm', searchTerm);
      }
      if (filterAssigned !== undefined) {
        params.append('isAssigned', filterAssigned.toString());
      }
      if (filterCensusYearId !== null) {
        params.append('censusYearId', filterCensusYearId.toString());
      }
      params.append('page', pagination.page.toString());
      params.append('pageSize', pagination.pageSize.toString());

      if (process.env.NODE_ENV === 'development') {
      }

      // No special handling needed for page size now that "All" option is removed

      const response = await fetch(`/api/unique-code?${params.toString()}`);

      if (!response.ok) {
        // Try to get more detailed error information
        let errorMessage = tErrors('failedToFetchUniqueCodes');
        try {
          const errorData = await response.json();
          if (errorData?.error) {
            errorMessage = errorData.error;
          }
        } catch (_parseError) {}
        throw new Error(errorMessage);
      }

      const data = await response.json();

      // If we got no data but there are other pages, adjust the page number
      if (data.data.length === 0 && data.pagination.total > 0) {
        // If we're beyond the last page, go to the last page
        if (pagination.page > data.pagination.totalPages) {
          setPagination((prev) => ({
            ...prev,
            page: Math.max(1, data.pagination.totalPages),
          }));

          // Fetch again with the corrected page number
          setTimeout(() => fetchUniqueCodes(), 0);
          return;
        }
      }

      setUniqueCodes(data.data);
      setPagination(data.pagination);
    } catch (_error) {
      // Use centralized error handling instead of raw error messages
      showError('failedToFetchUniqueCodes');
    } finally {
      setLoading(false);
    }
  }, [
    pagination.page,
    pagination.pageSize,
    searchTerm,
    filterAssigned,
    filterCensusYearId,
    showError,
    tErrors,
  ]);

  // Fetch unique codes on component mount
  useEffect(() => {
    fetchUniqueCodes();
  }, [fetchUniqueCodes]);

  // Fetch census years on component mount (only once)
  useEffect(() => {
    fetchCensusYears();
  }, [fetchCensusYears]);

  // Handle generating unique codes
  const { handleSubmit: submitGenerateForm, isSubmitting: isGenerating } =
    useFormSubmit<GenerateUniqueCodesFormValues>({
      onSubmit: async (data) => {
        try {
          // Ensure count is a number
          const formData = {
            count: Number(data.count),
            censusYearId: Number(data.censusYearId),
          };

          // Environment-aware logging - only in development
          if (process.env.NODE_ENV === 'development') {
          }

          const response = await fetch('/api/unique-code', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData),
          });

          if (!response.ok) {
            let errorMessage = tErrors('failedToGenerateUniqueCodes');
            try {
              const errorData = await response.json();
              if (errorData?.error) {
                errorMessage = errorData.error;
              }
            } catch (_parseError) {}
            return {
              success: false,
              message: errorMessage,
            };
          }

          const responseData = await response.json();

          // Get the active census year for proper display
          const activeYear = censusYears.find((year) => year.isActive);

          // Add the new codes to the state with proper census year information
          const codesWithProperYear = responseData.codes.map(
            (code: UniqueCode) => ({
              ...code,
              census_year: activeYear ? activeYear.year : code.census_year,
              is_active_year: true, // These are newly generated codes for the active year
            })
          );

          // Update pagination info but don't directly update the codes
          setPagination((prev) => ({
            ...prev,
            page: 1, // Navigate to first page to show new codes
            total: prev.total + codesWithProperYear.length,
            totalPages: Math.ceil(
              (prev.total + codesWithProperYear.length) / prev.pageSize
            ),
          }));

          // Update page input value to match the new page
          setPageInputValue('1');

          // Close the dialogue
          setIsGenerateDialogOpen(false);

          // Fetch the updated codes with the new pagination settings
          // This ensures we respect the page size and show the correct data
          setTimeout(() => fetchUniqueCodes(), 0);

          // Success - use centralized alert system with proper next-intl parameter passing
          const message = tNotifications('successfullyGeneratedUniqueCodes', {
            count: formData.count.toString(),
          });
          showAlert('success', message);
          return {
            success: true,
            suppressAlert: true,
          };
        } catch (_error) {
          showError('UpdateFailed', 'settings');
          return {
            success: false,
            suppressAlert: true,
          };
        }
      },
    });

  // Handle deleting unique codes
  const handleDeleteCodes = async () => {
    if (selectedCodes.length === 0) {
      return;
    }

    try {
      const response = await fetch('/api/unique-code', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ codeIds: selectedCodes }),
      });

      if (!response.ok) {
        const errorData = await response.json();

        // If some codes are assigned, show simple error message
        if (errorData.assignedCodes && errorData.assignedCodes.length > 0) {
          showError('cannotDeleteAssignedCodes', 'settings');
        } else {
          showError('failedToDeleteUniqueCodes', 'settings');
        }
        return;
      }

      // Remove the deleted codes from the state
      const updatedCodes = uniqueCodes.filter(
        (code) => !selectedCodes.includes(code.id)
      );
      setUniqueCodes(updatedCodes);

      // Update pagination info
      setPagination((prev) => ({
        ...prev,
        total: prev.total - selectedCodes.length,
        totalPages: Math.ceil(
          (prev.total - selectedCodes.length) / prev.pageSize
        ),
      }));

      // Clear selection
      setSelectedCodes([]);

      // Close the dialogue
      setIsDeleteDialogOpen(false);

      // Handle page navigation if current page is now empty
      if (
        updatedCodes.length === 0 &&
        pagination.total > selectedCodes.length
      ) {
        // If we're on the last page and it's now empty, go to the previous page
        if (pagination.page === pagination.totalPages && pagination.page > 1) {
          handlePageChange(pagination.page - 1);
        } else {
          // Otherwise, refresh the current page to show the next set of items
          fetchUniqueCodes();
        }
      }

      // Show success message using centralized alert system with proper next-intl parameter passing
      const message = tNotifications('successfullyDeletedUniqueCodes', {
        count: selectedCodes.length.toString(),
      });
      showAlert('success', message);
    } catch (_error) {
      showError('anErrorOccurredWhileDeletingUniqueCodes');
    }
  };

  // Handle printing unique code cards
  const handlePrintCards = async () => {
    try {
      // Check if any codes are selected
      if (selectedCodes.length === 0) {
        showError('pleaseSelectAtLeastOneCodeToPrint');
        return;
      }

      // Get the selected codes from the current data
      const selectedCodesData = uniqueCodes.filter((code) =>
        selectedCodes.includes(code.id)
      );

      // Check if we have any codes to print
      if (selectedCodesData.length === 0) {
        showWarning('noCodesSelectedForPrinting');
        return;
      }

      // Store the codes to print and open the confirmation dialogue
      setCodesToPrint(selectedCodesData);
      setIsPrintConfirmDialogOpen(true);
    } catch (_error) {
      if (process.env.NODE_ENV === 'development') {
      }

      // Show error message using centralized system
      showError('failedToPrepareCodesForPrinting', 'settings');
    }
  };

  // Handle print confirmation
  const handlePrintConfirm = async () => {
    try {
      // Get the code IDs
      const codeIds = codesToPrint.map((code) => code.id);

      // Create a JWT token with the code IDs
      const response = await fetch('/api/unique-code/print-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ codeIds }),
      });

      // Read the response body
      const responseData = await response.json();

      if (!response.ok) {
        throw new Error(
          responseData.error || tErrors('failedToCreatePrintSession')
        );
      }

      // The JWT token is now stored in an HTTP-only cookie
      // No need to include anything in the URL

      // Create a clean URL for the print cards page
      const printUrl = '/admin/unique-code/print-cards';

      // Show success message using centralized alert system with proper next-intl parameter passing
      const message = tNotifications('preparedCodesForPrinting', {
        count: codesToPrint.length.toString(),
      });
      showAlert('success', message);

      // Open the print cards page in a new tab
      // This will carry the HTTP-only cookie with the JWT token
      window.open(printUrl, '_blank');

      // Close the confirmation dialogue
      setIsPrintConfirmDialogOpen(false);
    } catch (_error) {
      if (process.env.NODE_ENV === 'development') {
      }
      showError('failedToPrepareCodesForPrinting', 'settings');
      setIsPrintConfirmDialogOpen(false);
    }
  };

  // Handle selecting/deselecting a code
  const handleSelectCode = (id: number) => {
    if (selectedCodes.includes(id)) {
      setSelectedCodes(selectedCodes.filter((codeId) => codeId !== id));
    } else {
      setSelectedCodes([...selectedCodes, id]);
    }
  };

  // Handle selecting/deselecting all codes
  const handleSelectAll = () => {
    if (selectedCodes.length === uniqueCodes.length) {
      setSelectedCodes([]);
    } else {
      setSelectedCodes(uniqueCodes.map((code) => code.id));
    }
  };

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  // Handle assignment filter change
  const handleFilterChange = (value: boolean | undefined) => {
    setFilterAssigned(value);
    setPagination((prev) => ({ ...prev, page: 1 })); // Reset to first page
  };

  // Handle census year filter change
  const handleCensusYearFilterChange = (yearId: number | null) => {
    setFilterCensusYearId(yearId);
    setPagination((prev) => ({ ...prev, page: 1 })); // Reset to first page
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setPagination((prev) => ({ ...prev, page }));
    setPageInputValue(page.toString());
  };

  // Handle page size change
  const handlePageSizeChange = (size: number) => {
    if (process.env.NODE_ENV === 'development') {
    }

    setPagination((prev) => {
      const newPagination = {
        ...prev,
        pageSize: size,
        page: 1, // Reset to first page when changing page size
        totalPages: Math.ceil(prev.total / size),
      };
      if (process.env.NODE_ENV === 'development') {
      }
      return newPagination;
    });
    setPageInputValue('1');
  };

  // Handle page input change
  const handlePageInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPageInputValue(e.target.value);
  };

  // Handle go to page
  const handleGoToPage = () => {
    const pageNumber = Number.parseInt(pageInputValue, 10);
    if (
      !Number.isNaN(pageNumber) &&
      pageNumber >= 1 &&
      pageNumber <= pagination.totalPages
    ) {
      handlePageChange(pageNumber);
    } else {
      // Reset input to current page if invalid
      setPageInputValue(pagination.page.toString());
    }
  };

  // Handle clicking on a unique code
  const handleCodeClick = async (code: UniqueCode) => {
    setSelectedCode(code);
    setIsCardLoading(true);
    setCardError(null);

    try {
      // Generate QR code
      await generateQrCode(code);

      // Fetch church name if not already fetched
      if (churchName === 'WSCCC Church') {
        await fetchChurchName();
      }

      // Open the dialogue
      setIsViewDialogOpen(true);
    } catch (_error) {
      if (process.env.NODE_ENV === 'development') {
      }
      setCardError(tErrors('failedToPrepareCodeCard'));
    } finally {
      setIsCardLoading(false);
    }
  };

  // Generate QR code for a single unique code
  const generateQrCode = async (code: UniqueCode) => {
    try {
      // Get the site URL from settings or fall back to default
      const baseUrl = await getSiteUrl();
      // Use the new QR code utility with size 128 (32 in Tailwind = 8rem = 128px)
      const qrDataUrl = await generateUniqueCodeQR(code.code, baseUrl, 128);
      setQrCodeUrl(qrDataUrl);
    } catch (_error) {
      if (process.env.NODE_ENV === 'development') {
      }
      setQrCodeUrl('');
      throw new Error(tErrors('failedToGenerateQrCode'));
    }
  };

  // Fetch church name
  const fetchChurchName = async () => {
    try {
      const response = await fetch('/api/settings/church-info');
      if (response.ok) {
        const data = await response.json();
        setChurchName(data.churchName || 'WSCCC Church');
      }
    } catch (_error) {
      if (process.env.NODE_ENV === 'development') {
      }
      // Keep the default church name
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center">
        <div>
          <h1 className="font-bold text-2xl">{t('uniqueCodeManagement')}</h1>
          <p className="text-muted-foreground">
            {t('generateAndManageUniqueCodes')}
          </p>
        </div>
        <div className="flex flex-wrap gap-2">
          <Button
            onClick={() => setIsGenerateDialogOpen(true)}
            variant="outline"
          >
            <Plus className="mr-1 h-4 w-4" />
            {t('generate')}
          </Button>
          <Button
            disabled={selectedCodes.length === 0}
            onClick={handlePrintCards}
            title={
              selectedCodes.length === 0
                ? t('selectCodesToPrint')
                : t('printSelectedCodes')
            }
            variant="outline"
          >
            <Printer className="mr-1 h-4 w-4" />
            {tCommon('print')}
          </Button>
          <AlertDialog
            onOpenChange={setIsDeleteDialogOpen}
            open={isDeleteDialogOpen}
          >
            <AlertDialogTrigger asChild>
              <Button
                disabled={selectedCodes.length === 0}
                variant="destructive"
              >
                <Trash2 className="mr-1 h-4 w-4" />
                {tCommon('delete')}
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>{tCommon('areYouSure')}</AlertDialogTitle>
                <AlertDialogDescription>
                  {t('deleteCodesConfirmation', {
                    count: selectedCodes.length.toString(),
                    codeText:
                      selectedCodes.length === 1 ? t('code') : t('codes'),
                  })}
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>{tCommon('cancel')}</AlertDialogCancel>
                <AlertDialogAction onClick={handleDeleteCodes}>
                  {tCommon('delete')}
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </div>

      {/* Advanced Search and Filter */}
      <div className="rounded-lg border border-border/50 bg-background/50 backdrop-blur-sm">
        <div className="p-6 pb-3">
          <h3 className="font-semibold text-lg tracking-tight">
            {t('searchAndFilter')}
          </h3>
          <p className="text-muted-foreground text-sm">
            {t('findSpecificCodes')}
          </p>
        </div>
        <div className="px-6 pb-6">
          <div className="space-y-4">
            {/* Search bar with integrated filter button */}
            <div className="flex gap-2">
              <div className="relative flex-1">
                <Search className="absolute top-3 left-0 h-4 w-4 text-muted-foreground" />
                <Input
                  className="pr-20 pl-6"
                  onChange={handleSearchChange}
                  placeholder={t('searchCodes')}
                  type="search"
                  value={searchTerm}
                  variant="line"
                />
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      className="absolute top-1 right-1 h-7 cursor-pointer gap-1 px-2 text-muted-foreground hover:text-foreground"
                      size="sm"
                      variant="ghost"
                    >
                      <SlidersHorizontal className="h-4 w-4" />
                      <span className="sr-only sm:not-sr-only sm:whitespace-nowrap">
                        {tCommon('filters')}
                      </span>
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent
                    align="end"
                    className="w-[240px] p-4"
                    sideOffset={8}
                  >
                    <div className="grid gap-4">
                      <div className="space-y-2">
                        <h4 className="font-medium leading-none">
                          {tEmptyStates('status')}
                        </h4>
                        <RadioGroup
                          className="flex flex-col space-y-1"
                          defaultValue={
                            filterAssigned === undefined
                              ? 'all'
                              : filterAssigned
                                ? 'assigned'
                                : 'unassigned'
                          }
                          onValueChange={(value: string) => {
                            if (value === 'all') {
                              handleFilterChange(undefined);
                            } else if (value === 'assigned') {
                              handleFilterChange(true);
                            } else {
                              handleFilterChange(false);
                            }
                          }}
                        >
                          <div className="flex cursor-pointer items-center space-x-2">
                            <RadioGroupItem
                              className="cursor-pointer"
                              id="all"
                              value="all"
                            />
                            <Label className="cursor-pointer" htmlFor="all">
                              {tCommon('all')}
                            </Label>
                          </div>
                          <div className="flex cursor-pointer items-center space-x-2">
                            <RadioGroupItem
                              className="cursor-pointer"
                              id="assigned"
                              value="assigned"
                            />
                            <Label
                              className="cursor-pointer"
                              htmlFor="assigned"
                            >
                              {tCommon('statusAssigned')}
                            </Label>
                          </div>
                          <div className="flex cursor-pointer items-center space-x-2">
                            <RadioGroupItem
                              className="cursor-pointer"
                              id="unassigned"
                              value="unassigned"
                            />
                            <Label
                              className="cursor-pointer"
                              htmlFor="unassigned"
                            >
                              {tCommon('statusUnassigned')}
                            </Label>
                          </div>
                        </RadioGroup>
                      </div>

                      <Separator />

                      <div className="space-y-2">
                        <h4 className="font-medium leading-none">
                          {t('censusYear')}
                        </h4>
                        <Select
                          onValueChange={(value: string) =>
                            handleCensusYearFilterChange(
                              value === 'all' ? null : Number(value)
                            )
                          }
                          value={
                            filterCensusYearId === null
                              ? 'all'
                              : filterCensusYearId.toString()
                          }
                        >
                          <SelectTrigger className="cursor-pointer">
                            <SelectValue placeholder={t('selectYear')} />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem className="cursor-pointer" value="all">
                              {t('allYears')}
                            </SelectItem>
                            <SelectSeparator />
                            {censusYears
                              .sort((a, b) => b.year - a.year) // Sort by newest first
                              .map((year) => (
                                <SelectItem
                                  className="cursor-pointer"
                                  key={year.id}
                                  value={year.id.toString()}
                                >
                                  {year.year}{' '}
                                  {year.isActive
                                    ? t('activeStatus')
                                    : t('archivedStatus')}
                                </SelectItem>
                              ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <Button
                        className="mt-2"
                        onClick={() => {
                          handleFilterChange(undefined);
                          handleCensusYearFilterChange(null);
                          setSearchTerm('');
                        }}
                        size="sm"
                        variant="outline"
                      >
                        {tCommon('resetFilters')}
                      </Button>
                    </div>
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            {/* Active filters display */}
            <div className="flex flex-wrap gap-2">
              {(searchTerm ||
                filterAssigned !== undefined ||
                filterCensusYearId !== null) && (
                <div className="flex flex-wrap items-center gap-2">
                  {searchTerm && (
                    <Badge
                      className="flex h-7 items-center gap-1 px-3"
                      variant="secondary"
                    >
                      <span>
                        {tCommon('search')}: {searchTerm}
                      </span>
                      <Button
                        className="-mr-1 ml-1 h-4 w-4 rounded-full"
                        onClick={() => setSearchTerm('')}
                        size="icon"
                        variant="ghost"
                      >
                        <X className="h-3 w-3" />
                        <span className="sr-only">
                          {t('removeSearchFilter')}
                        </span>
                      </Button>
                    </Badge>
                  )}

                  {filterAssigned !== undefined && (
                    <Badge
                      className="flex h-7 items-center gap-1 px-3"
                      variant="secondary"
                    >
                      <span>
                        {filterAssigned
                          ? t('statusAssigned')
                          : t('statusUnassigned')}
                      </span>
                      <Button
                        className="-mr-1 ml-1 h-4 w-4 rounded-full"
                        onClick={() => handleFilterChange(undefined)}
                        size="icon"
                        variant="ghost"
                      >
                        <X className="h-3 w-3" />
                        <span className="sr-only">
                          {t('removeStatusFilter')}
                        </span>
                      </Button>
                    </Badge>
                  )}

                  {filterCensusYearId !== null && (
                    <Badge
                      className="flex h-7 items-center gap-1 px-3"
                      variant="secondary"
                    >
                      <span>
                        {t('year')}:{' '}
                        {
                          censusYears.find((y) => y.id === filterCensusYearId)
                            ?.year
                        }
                        {censusYears.find((y) => y.id === filterCensusYearId)
                          ?.isActive
                          ? ` (${t('active')})`
                          : ` (${t('archived')})`}
                      </span>
                      <Button
                        className="-mr-1 ml-1 h-4 w-4 rounded-full"
                        onClick={() => handleCensusYearFilterChange(null)}
                        size="icon"
                        variant="ghost"
                      >
                        <X className="h-3 w-3" />
                        <span className="sr-only">{t('removeYearFilter')}</span>
                      </Button>
                    </Badge>
                  )}

                  {(searchTerm ||
                    filterAssigned !== undefined ||
                    filterCensusYearId !== null) && (
                    <Button
                      className="h-7 px-2 text-muted-foreground"
                      onClick={() => {
                        setSearchTerm('');
                        handleFilterChange(undefined);
                        handleCensusYearFilterChange(null);
                      }}
                      size="sm"
                      variant="ghost"
                    >
                      {tCommon('clear')} {tCommon('selectAll').toLowerCase()}
                    </Button>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Unique codes table */}
      <div className="space-y-6">
        {/* Modern header section */}
        <div className="flex flex-col items-start justify-between gap-2 pb-2 sm:flex-row sm:items-center">
          <div className="space-y-1">
            <h2 className="font-semibold text-xl tracking-tight">
              {t('uniqueCodes')}
            </h2>
            <p className="text-muted-foreground text-sm">
              {(() => {
                const displayYear =
                  filterCensusYearId !== null
                    ? censusYears.find((y) => y.id === filterCensusYearId)?.year
                    : censusYears.find((y) => y.isActive)?.year;

                const codeText =
                  pagination.total === 1 ? t('code') : t('codes');
                const yearText = displayYear
                  ? locale === 'zh-CN'
                    ? `（${displayYear}年）`
                    : ` for ${displayYear}`
                  : '';

                return tCommon('codesFoundSummary', {
                  count: pagination.total.toString(),
                  codeText,
                  yearText,
                });
              })()}
            </p>
          </div>
          <div className="flex flex-wrap items-center gap-2">
            {selectedCodes.length > 0 && (
              <Badge
                className="flex items-center gap-1 py-1.5"
                variant="outline"
              >
                {selectedCodes.length} {tCommon('selected')}
                <Button
                  className="h-5 w-5 rounded-full"
                  onClick={() => setSelectedCodes([])}
                  size="icon"
                  variant="ghost"
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            )}
          </div>
        </div>

        {/* Table section */}
        <div className="space-y-4">
          {/* Table */}
          <div className="rounded-lg border border-border/50 bg-background/50 backdrop-blur-sm">
            <Table>
              <TableHeader>
                <TableRow className="border-b">
                  <TableHead className="w-12">
                    <Checkbox
                      aria-label={t('selectAllCodes')}
                      checked={
                        selectedCodes.length === uniqueCodes.length &&
                        uniqueCodes.length > 0
                      }
                      className="cursor-pointer"
                      onCheckedChange={handleSelectAll}
                    />
                  </TableHead>
                  <TableHead className="text-center">{t('number')}</TableHead>
                  <TableHead>{t('code')}</TableHead>
                  <TableHead>{tTables('status')}</TableHead>
                  <TableHead>{t('censusYear')}</TableHead>
                  <TableHead>{t('createdDate')}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  // Loading skeleton
                  Array.from({
                    length: pagination.pageSize > 5 ? 5 : pagination.pageSize,
                  }).map((_, index) => (
                    <TableRow className="border-b-0" key={index}>
                      <TableCell>
                        <Skeleton className="h-4 w-4" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-8" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-32" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-20" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-16" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-24" />
                      </TableCell>
                    </TableRow>
                  ))
                ) : uniqueCodes.length === 0 ? (
                  <TableRow className="border-b-0">
                    <TableCell className="py-16 text-center" colSpan={6}>
                      <div className="flex flex-col items-center gap-4">
                        <div className="rounded-full bg-muted p-3">
                          <FileText className="h-8 w-8 text-muted-foreground" />
                        </div>
                        <div>
                          <p className="mb-4 text-muted-foreground">
                            {tEmptyStates('noUniqueCodesFound')}
                          </p>
                          <Button
                            onClick={() => setIsGenerateDialogOpen(true)}
                            variant="outline"
                          >
                            <Plus className="mr-2 h-4 w-4" />
                            {t('generateCodes')}
                          </Button>
                        </div>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  uniqueCodes.map((code) => (
                    <TableRow
                      className="border-b-0 hover:bg-muted/50"
                      key={code.id}
                    >
                      <TableCell>
                        <Checkbox
                          aria-label={`Select code ${code.code}`}
                          checked={selectedCodes.includes(code.id)}
                          className="cursor-pointer"
                          onCheckedChange={() => handleSelectCode(code.id)}
                        />
                      </TableCell>
                      <TableCell className="text-center">{code.id}</TableCell>
                      <TableCell className="font-medium">
                        <div
                          aria-label={`View card for code ${code.code}`}
                          className="cursor-pointer font-mono text-sm underline decoration-gray-400 transition-colors hover:text-primary"
                          onClick={() => handleCodeClick(code)}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter' || e.key === ' ') {
                              handleCodeClick(code);
                            }
                          }}
                          role="button"
                          tabIndex={0}
                        >
                          {code.code}
                        </div>
                      </TableCell>
                      <TableCell>
                        {code.isAssigned ? (
                          <StatusBadge variant="success">
                            <CheckCircle2 />
                            {tCommon('statusAssigned')}
                          </StatusBadge>
                        ) : (
                          <StatusBadge variant="warning">
                            <Clock />
                            {tCommon('statusUnassigned')}
                          </StatusBadge>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <span className="font-medium">
                            {code.census_year}
                          </span>
                          {code.is_active_year ? (
                            <StatusBadge variant="info">
                              {t('active')}
                            </StatusBadge>
                          ) : (
                            <StatusBadge variant="default">
                              {t('archived')}
                            </StatusBadge>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>{formatForDisplay(code.createdAt)}</TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          {/* Mobile-responsive pagination with rows per page selector */}
          {pagination.total > 0 && (
            <div className="w-full space-y-4 px-4 py-4 sm:flex sm:items-center sm:justify-between sm:space-y-0">
              {/* Rows per page selector */}
              <div className="flex items-center justify-center gap-2 sm:justify-start">
                <Label
                  className="hidden font-medium text-sm sm:block"
                  htmlFor="pageSize"
                >
                  {tCommon('rows')}:
                </Label>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      className="flex h-9 items-center gap-2 bg-background/50 px-3 transition-colors hover:bg-background/80"
                      size="sm"
                      variant="outline"
                    >
                      {pagination.pageSize}
                      <ChevronDown className="h-4 w-4 opacity-70" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="start">
                    <DropdownMenuItem
                      className="cursor-pointer"
                      onClick={() => handlePageSizeChange(10)}
                    >
                      10
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      className="cursor-pointer"
                      onClick={() => handlePageSizeChange(20)}
                    >
                      20
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      className="cursor-pointer"
                      onClick={() => handlePageSizeChange(50)}
                    >
                      50
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      className="cursor-pointer"
                      onClick={() => handlePageSizeChange(100)}
                    >
                      100
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>

              {/* Pagination controls */}
              <div className="flex justify-center sm:flex-1 sm:justify-center">
                <Pagination>
                  <PaginationContent className="flex-wrap justify-center">
                    {/* First page button */}
                    <PaginationItem>
                      <Button
                        className="h-8 w-8"
                        disabled={pagination.page === 1}
                        onClick={() => handlePageChange(1)}
                        size="icon"
                        title={t('firstPage')}
                        variant="outline"
                      >
                        <span className="sr-only">{t('firstPage')}</span>
                        <ChevronsLeft className="h-4 w-4" />
                      </Button>
                    </PaginationItem>

                    {/* Previous page button */}
                    <PaginationItem>
                      <Button
                        className="h-8 w-8"
                        disabled={pagination.page === 1}
                        onClick={() =>
                          handlePageChange(Math.max(1, pagination.page - 1))
                        }
                        size="icon"
                        title={t('previousPage')}
                        variant="outline"
                      >
                        <span className="sr-only">{t('previousPage')}</span>
                        <ChevronLeft className="h-4 w-4" />
                      </Button>
                    </PaginationItem>

                    {/* Page input and info */}
                    <PaginationItem>
                      <div className="flex items-center gap-1 sm:gap-2">
                        <Input
                          className="h-8 w-10 text-center text-xs sm:w-12 sm:text-sm"
                          onChange={handlePageInputChange}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                              handleGoToPage();
                            }
                          }}
                          type="text"
                          value={pageInputValue}
                        />
                        <span className="whitespace-nowrap text-muted-foreground text-xs sm:text-sm">
                          of {pagination.totalPages}
                        </span>
                        <Button
                          className="h-8 px-2 text-xs sm:px-3 sm:text-sm"
                          onClick={handleGoToPage}
                          size="sm"
                          variant="outline"
                        >
                          Go
                        </Button>
                      </div>
                    </PaginationItem>

                    {/* Next page button */}
                    <PaginationItem>
                      <Button
                        className="h-8 w-8"
                        disabled={pagination.page === pagination.totalPages}
                        onClick={() =>
                          handlePageChange(
                            Math.min(pagination.totalPages, pagination.page + 1)
                          )
                        }
                        size="icon"
                        title={t('nextPage')}
                        variant="outline"
                      >
                        <span className="sr-only">{t('nextPage')}</span>
                        <ChevronRight className="h-4 w-4" />
                      </Button>
                    </PaginationItem>

                    {/* Last page button */}
                    <PaginationItem>
                      <Button
                        className="h-8 w-8"
                        disabled={pagination.page === pagination.totalPages}
                        onClick={() => handlePageChange(pagination.totalPages)}
                        size="icon"
                        title={t('lastPage')}
                        variant="outline"
                      >
                        <span className="sr-only">{t('lastPage')}</span>
                        <ChevronsRight className="h-4 w-4" />
                      </Button>
                    </PaginationItem>
                  </PaginationContent>
                </Pagination>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Generate unique codes dialogue */}
      <Dialog
        onOpenChange={setIsGenerateDialogOpen}
        open={isGenerateDialogOpen}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('generateUniqueCodes')}</DialogTitle>
            <DialogDescription>
              {(() => {
                const activeYear = censusYears.find(
                  (year) => year.isActive
                )?.year;
                return activeYear
                  ? t('createNewUniqueCodesDescription', {
                      year: activeYear.toString(),
                    })
                  : t('createNewUniqueCodesDescriptionFallback');
              })()}
            </DialogDescription>
            <div className="mt-2 font-medium text-amber-600 text-sm">
              {t('createNewUniqueCodesNote')}
            </div>
          </DialogHeader>

          <Form
            className="space-y-4 pt-4"
            form={generateForm}
            isLoading={isGenerating}
            onSubmit={(data) => submitGenerateForm(data)}
            submitText={t('generateCodes')}
          >
            <FormField
              className="mb-2"
              error={generateForm.formState.errors.count}
              id="count"
              label={t('numberOfCodes')}
              placeholder={t('enterNumberOfCodes')}
              register={generateForm.register}
              required
              type="number"
            />
            <div className="text-muted-foreground text-sm">
              {t('generateBetween1And1000')}
              {t('theseCodesWillBeUsed')}
            </div>
          </Form>

          <DialogFooter className="mt-4">
            <Button
              onClick={() => setIsGenerateDialogOpen(false)}
              variant="outline"
            >
              {tCommon('cancel')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* View code card dialogue */}
      <Dialog onOpenChange={setIsViewDialogOpen} open={isViewDialogOpen}>
        <DialogContent className="sm:max-w-md md:max-w-lg">
          <DialogHeader>
            <DialogTitle>{t('uniqueCodeCard')}</DialogTitle>
            <DialogDescription>
              {t('viewDetailsForThisUniqueCode')}
            </DialogDescription>
          </DialogHeader>

          {isCardLoading ? (
            <div className="space-y-4 py-4">
              <Skeleton className="mx-auto h-8 w-32" />
              <Skeleton className="mx-auto h-4 w-24" />
              <Skeleton className="mx-auto h-32 w-32" />
              <Skeleton className="mx-auto h-4 w-40" />
              <Skeleton className="mx-auto h-6 w-32" />
              <Skeleton className="mx-auto h-3 w-48" />
            </div>
          ) : cardError ? (
            <Alert className="my-4" variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>{tCommon('error')}</AlertTitle>
              <AlertDescription>{cardError}</AlertDescription>
            </Alert>
          ) : (
            selectedCode && (
              <div className="card-container relative my-4 flex flex-col items-center justify-center border border-gray-300 border-dashed p-4">
                {/* Church name and ID */}
                <div className="mb-2 text-center">
                  <div className="mb-1 flex items-center justify-between">
                    <div className="rounded bg-muted/30 px-2 py-0.5 font-medium text-xs">
                      #{selectedCode.id}
                    </div>
                    <div className="text-muted-foreground text-xs">
                      {tCommon('censusYear')} {selectedCode.census_year}
                    </div>
                  </div>
                  <h2 className="font-bold text-lg">{churchName}</h2>
                </div>

                {/* QR Code */}
                <div className="mb-4 rounded-md border border-gray-200 bg-white p-1">
                  {qrCodeUrl ? (
                    <Image
                      alt={`QR Code for ${selectedCode.code}`}
                      className="h-32 w-32"
                      height={128}
                      src={qrCodeUrl}
                      width={128}
                    />
                  ) : (
                    <div className="flex h-32 w-32 items-center justify-center bg-gray-100">
                      <p className="text-gray-500 text-sm">
                        {t('qrCodeError')}
                      </p>
                    </div>
                  )}
                </div>

                {/* Unique Code */}
                <div className="text-center">
                  <p className="mb-1 text-muted-foreground text-sm">
                    {t('yourUniqueCode')}
                  </p>
                  <p className="font-bold font-mono text-lg">
                    {selectedCode.code}
                  </p>
                </div>

                {/* Instructions */}
                <div className="mt-2 text-center">
                  <p className="text-muted-foreground text-xs">
                    {t('scanThisQrCodeToAccessCensus')}
                  </p>
                </div>

                {/* Cut marks */}
                <div className="absolute top-0 left-0 h-3 w-3 border-gray-400 border-t border-l" />
                <div className="absolute top-0 right-0 h-3 w-3 border-gray-400 border-t border-r" />
                <div className="absolute bottom-0 left-0 h-3 w-3 border-gray-400 border-b border-l" />
                <div className="absolute right-0 bottom-0 h-3 w-3 border-gray-400 border-r border-b" />
              </div>
            )
          )}

          <DialogFooter className="mt-4">
            <Button onClick={() => setIsViewDialogOpen(false)}>
              {tCommon('close')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Print confirmation dialogue */}
      <AlertDialog
        onOpenChange={setIsPrintConfirmDialogOpen}
        open={isPrintConfirmDialogOpen}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t('printSelectedCodes')}</AlertDialogTitle>
            <AlertDialogDescription>
              {t('printConfirmDescription', {
                count: codesToPrint.length.toString(),
                pages: Math.ceil(codesToPrint.length / 9).toString(),
              })}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{tCommon('cancel')}</AlertDialogCancel>
            <AlertDialogAction onClick={handlePrintConfirm}>
              {tCommon('print')}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}

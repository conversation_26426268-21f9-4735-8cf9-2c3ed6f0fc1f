import type { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import { requireCensusAuth } from '@/lib/census-auth/census-auth-utils';
import { CensusFormClient } from './census-form-client';

// Force dynamic rendering to prevent DYNAMIC_SERVER_USAGE errors
export const dynamic = 'force-dynamic';

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations('metadata');

  return {
    title: t('censusFormTitle'),
    description: t('censusFormDescription'),
  };
}

// This is needed for TypeScript in Next.js 15.3.1 with dynamic routes
export async function generateStaticParams() {
  return [{ code: 'example' }];
}

export default async function CensusFormPage({
  params,
}: {
  params: Promise<{ code: string }>;
}) {
  // Server-side authentication check
  const session = await requireCensusAuth();

  // Get the code from the URL params - await params in Next.js 15
  const resolvedParams = await params;
  const code = resolvedParams.code;

  // Pass the initial session data to the client component
  // This ensures we have data to display even before the client-side session is loaded
  const initialSessionData = {
    id: session.user.id,
    name: session.user.name,
    censusYearId: session.user.censusYearId,
    householdId: session.user.householdId || undefined,
  };

  // Use the client component with SessionLoader to ensure session data is fully loaded
  return (
    <CensusFormClient code={code} initialSessionData={initialSessionData} />
  );
}

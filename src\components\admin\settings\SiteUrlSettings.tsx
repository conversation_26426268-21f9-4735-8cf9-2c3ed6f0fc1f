'use client';

import { useTranslations } from 'next-intl';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { FormFieldWithTooltip } from '@/components/admin/settings/FormFieldWithTooltip';
import { SettingsCard } from '@/components/admin/settings/SettingsCard';
import { useFormSubmit } from '@/hooks/useFormSubmit';
import { useMessage } from '@/hooks/useMessage';
import { zodResolver } from '@/lib/utils/zod-resolver-compat';
import {
  type ClientSiteUrlFormValues,
  createClientSiteUrlSchema,
} from '@/lib/validation/client/settings-client';

export function SiteUrlSettings() {
  const t = useTranslations('admin');
  const { showError, showSuccess, showWarning, showDirect } = useMessage();
  const tValidation = useTranslations('validation');

  // Create client schema with translations
  const siteUrlSchema = createClientSiteUrlSchema(tValidation);

  const form = useForm<ClientSiteUrlFormValues>({
    resolver: zodResolver(siteUrlSchema),
    defaultValues: {
      siteUrl: 'http://localhost:3000',
    },
  });

  // Use our custom hook for form submission
  const { handleSubmit: submitSiteUrl, isSubmitting } =
    useFormSubmit<ClientSiteUrlFormValues>({
      onSubmit: async (data) => {
        try {
          const response = await fetch('/api/settings/site-url', {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(data),
          });

          if (!response.ok) {
            const errorData = await response.json();
            return {
              success: false,
              errorCode: errorData.error || 'UpdateFailed',
            };
          }

          return {
            success: true,
            suppressAlert: true, // Suppress the alert in onSubmit since we'll show it in onSuccess
          };
        } catch (error) {
          console.error('Error updating site URL:', error);
          return {
            success: false,
            message: t('failedToUpdateSiteUrl'),
            suppressAlert: true, // Suppress the alert in onSubmit since we'll handle it in onError
          };
        }
      },
      onSuccess: (result) => {
        showSuccess('siteUrlUpdated');
      },
      onError: (error) => {
        if (error && typeof error === 'object' && 'errorCode' in error) {
          showError(String(error.errorCode), 'settings');
        } else {
          showError('errorOccurredUpdatingSiteUrl');
        }
      },
    });

  // Fetch site URL on component mount
  useEffect(() => {
    const fetchSiteUrl = async () => {
      try {
        const response = await fetch('/api/settings/site-url');

        if (!response.ok) {
          throw new Error('Failed to fetch site URL');
        }

        const data = await response.json();

        // Update form values
        form.reset({
          siteUrl: data.siteUrl || 'http://localhost:3000',
        });
      } catch (error) {
        console.error('Error fetching site URL:', error);
        // For development purposes, use default value if the API call fails
        form.reset({
          siteUrl: 'http://localhost:3000',
        });
        showWarning('usingDefaultUrlDatabaseUnavailable');
      }
    };

    fetchSiteUrl();
  }, [form, showWarning, t]);

  return (
    <SettingsCard
      description={t('configureBaseUrl')}
      form={form}
      isSubmitting={isSubmitting}
      onFormSubmit={submitSiteUrl}
      title={t('siteUrl')}
    >
      <div className="space-y-6">
        <FormFieldWithTooltip
          error={form.formState.errors.siteUrl}
          helperText={t('enterFullUrl')}
          id="siteUrl"
          label={t('siteUrl')}
          placeholder={t('siteUrlPlaceholder')}
          register={form.register}
          required
          tooltip={t('urlForQrCodes')}
          type="url"
        />
      </div>
    </SettingsCard>
  );
}

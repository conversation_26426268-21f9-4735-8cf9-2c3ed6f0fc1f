'use client';

import { useSessionMonitor } from '@/hooks/useSessionMonitor';

/**
 * Session Monitor Component
 *
 * This component automatically monitors census session validity and handles
 * logout/redirect when a household is deleted by an admin.
 *
 * Should be included in census pages to provide automatic session management.
 */
export function SessionMonitor() {
  useSessionMonitor();

  // This component doesn't render anything visible
  // It just runs the session monitoring logic in the background
  return null;
}

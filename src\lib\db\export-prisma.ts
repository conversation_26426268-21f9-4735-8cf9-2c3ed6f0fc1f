/**
 * Database Export Functions with Prisma
 * Replaces MySQL-based export implementation with Prisma ORM
 */

import { formatForDatabase, formatForFilename } from '@/lib/utils/date-time';
import { prisma } from './prisma';

// Utility function to safely format CSV cells and prevent injection attacks
function formatSafeCSVCell(value: string): string {
  // Prevent CSV injection by checking for dangerous characters at the start
  const dangerousChars = /^[=+\-@\t\r]/;
  let sanitizedValue = value;

  if (dangerousChars.test(value)) {
    // Prepend a space to neutralize formula injection
    sanitizedValue = ' ' + value;
  }

  // Escape quotes by doubling them and wrap in quotes
  return `"${sanitizedValue.replace(/"/g, '""')}"`;
}

// Types for export options
export type ExportFormat = 'sql' | 'csv' | 'json';
export type ExportTables = 'all' | 'members' | 'households' | 'settings';

interface ExportOptions {
  format: ExportFormat;
  tables: ExportTables;
  adminId: number;
}

// Log export activity using Prisma
export async function logExport(
  adminId: number,
  exportType: string,
  format: string,
  recordCount: number,
  ipAddress?: string
): Promise<void> {
  try {
    const parameters = JSON.stringify({
      exportType,
      format,
      timestamp: formatForDatabase(new Date()),
    });

    await prisma.exportLog
      ?.create({
        data: {
          adminId,
          exportType,
          selection: 'all',
          parameters,
          fileFormat: format,
          recordCount,
          ipAddress: ipAddress || null,
          createdAt: new Date(),
        },
      })
      .catch(() => null); // Ignore if table doesn't exist
  } catch (error) {
    // Environment-aware logging - only in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Failed to log export activity:', error);
    }
    // Don't throw - this is a non-critical operation
  }
}

// Get data for different table selections using Prisma
export async function getExportData(selection: ExportTables): Promise<{
  data: Record<string, unknown[]>;
  recordCount: number;
}> {
  let data: Record<string, unknown[]> = {};
  let recordCount = 0;

  switch (selection) {
    case 'all': {
      // Export all main tables
      const [
        members,
        households,
        householdMembers,
        censusYears,
        censusControls,
        sacraments,
        sacramentTypes,
        uniqueCodes,
        censusFormStatuses,
        suburbs,
      ] = await Promise.all([
        prisma.member.findMany(),
        prisma.household.findMany(),
        prisma.householdMember.findMany(),
        prisma.censusYear.findMany(),
        prisma.censusControl?.findMany().catch(() => []) || [],
        prisma.sacrament.findMany(),
        prisma.sacramentType.findMany(),
        prisma.uniqueCode.findMany(),
        prisma.censusForm?.findMany().catch(() => []) || [],
        prisma.suburb?.findMany().catch(() => []) || [],
      ]);

      data = {
        members,
        households,
        household_members: householdMembers,
        census_years: censusYears,
        census_controls: censusControls,
        sacraments,
        sacrament_types: sacramentTypes,
        unique_codes: uniqueCodes,
        census_forms: censusFormStatuses,
        suburbs,
      };

      recordCount = Object.values(data).reduce(
        (sum, arr) => sum + arr.length,
        0
      );
      break;
    }

    case 'members': {
      const memberData = await Promise.all([
        prisma.member.findMany(),
        prisma.sacrament.findMany(),
        prisma.sacramentType.findMany(),
      ]);

      data = {
        members: memberData[0],
        sacraments: memberData[1],
        sacrament_types: memberData[2],
      };

      recordCount = Object.values(data).reduce(
        (sum, arr) => sum + arr.length,
        0
      );
      break;
    }

    case 'households': {
      const householdData = await Promise.all([
        prisma.household.findMany(),
        prisma.householdMember.findMany(),
        prisma.censusForm?.findMany().catch(() => []) || [],
      ]);

      data = {
        households: householdData[0],
        household_members: householdData[1],
        census_forms: householdData[2],
      };

      recordCount = Object.values(data).reduce(
        (sum, arr) => sum + arr.length,
        0
      );
      break;
    }

    case 'settings': {
      const settingsData = await Promise.all([
        prisma.censusControl?.findMany().catch(() => []) || [],
        prisma.censusYear.findMany(),
        prisma.suburb?.findMany().catch(() => []) || [],
      ]);

      data = {
        census_controls: settingsData[0],
        census_years: settingsData[1],
        suburbs: settingsData[2],
      };

      recordCount = Object.values(data).reduce(
        (sum, arr) => sum + arr.length,
        0
      );
      break;
    }

    default:
      throw new Error(`Invalid table selection: ${selection}`);
  }

  return { data, recordCount };
}

// Generate SQL export from Prisma data
export async function generateSQLExport(
  data: Record<string, unknown[]>
): Promise<{ sql: string; recordCount: number }> {
  let sql = '';
  let totalRecords = 0;

  // Add header with timestamp
  sql += '-- WSCCC Census System Database Export (Prisma)\n';
  sql += `-- Date: ${formatForDatabase(new Date())}\n\n`;

  // Process each table
  for (const [tableName, rows] of Object.entries(data)) {
    totalRecords += rows.length;

    if (rows.length > 0) {
      // Generate INSERT statements
      const columns = Object.keys(rows[0] as Record<string, unknown>);
      sql += `-- Table: ${tableName}\n`;
      sql += `INSERT INTO \`${tableName}\` (\`${columns.join('`, `')}\`) VALUES\n`;

      rows.forEach((row, index) => {
        const values = columns.map((col) => {
          const value = (row as Record<string, unknown>)[col];
          if (value === null || value === undefined) return 'NULL';
          if (typeof value === 'number') return value;
          if (typeof value === 'boolean') return value ? 1 : 0;
          if (value instanceof Date)
            return `'${value.toISOString().slice(0, 19).replace('T', ' ')}'`;
          return `'${String(value).replace(/'/g, "''")}'`;
        });

        sql += `(${values.join(', ')})${index < rows.length - 1 ? ',' : ';'}\n`;
      });

      sql += '\n';
    }
  }

  return { sql, recordCount: totalRecords };
}

// Generate CSV export from Prisma data
export async function generateCSVExport(
  data: Record<string, unknown[]>
): Promise<{ csv: Record<string, string>; recordCount: number }> {
  const result: Record<string, string> = {};
  let totalRecords = 0;

  for (const [tableName, rows] of Object.entries(data)) {
    totalRecords += rows.length;

    if (rows.length > 0) {
      // Generate CSV header
      const columns = Object.keys(rows[0] as Record<string, unknown>);
      let csv = columns.map((col) => formatSafeCSVCell(col)).join(',') + '\n';

      // Generate CSV rows
      rows.forEach((row) => {
        const values = columns.map((col) => {
          const value = (row as Record<string, unknown>)[col];
          if (value === null || value === undefined) return '';
          if (typeof value === 'object' && !(value instanceof Date))
            return formatSafeCSVCell(JSON.stringify(value));
          if (value instanceof Date)
            return formatSafeCSVCell(value.toISOString());
          return formatSafeCSVCell(String(value));
        });

        csv += values.join(',') + '\n';
      });

      result[tableName] = csv;
    } else {
      // Empty table - just add header
      result[tableName] = 'No data available\n';
    }
  }

  return { csv: result, recordCount: totalRecords };
}

// Generate JSON export from Prisma data
export async function generateJSONExport(
  data: Record<string, unknown[]>
): Promise<{ json: Record<string, unknown[]>; recordCount: number }> {
  let totalRecords = 0;

  for (const rows of Object.values(data)) {
    totalRecords += rows.length;
  }

  return { json: data, recordCount: totalRecords };
}

// Main export function using Prisma
export async function exportDatabase(options: ExportOptions): Promise<{
  data: string | Record<string, string> | Record<string, unknown[]>;
  filename: string;
  contentType: string;
  recordCount: number;
}> {
  const { format, tables: tableSelection } = options;

  // Get data to export using Prisma
  const { data: exportData, recordCount: totalRecords } =
    await getExportData(tableSelection);

  // Generate filename
  const timestamp = formatForFilename(new Date());
  let filename = `wsccc_census_${tableSelection}_${timestamp}`;
  let contentType = '';
  let data: string | Record<string, string> | Record<string, unknown[]>;
  let recordCount = totalRecords;

  // Generate export based on format
  switch (format) {
    case 'sql': {
      const sqlResult = await generateSQLExport(exportData);
      data = sqlResult.sql;
      recordCount = sqlResult.recordCount;
      filename += '.sql';
      contentType = 'application/sql';
      break;
    }

    case 'csv': {
      const csvResult = await generateCSVExport(exportData);
      data = csvResult.csv;
      recordCount = csvResult.recordCount;
      filename += '.zip';
      contentType = 'application/zip';
      break;
    }

    case 'json': {
      const jsonResult = await generateJSONExport(exportData);
      data = jsonResult.json;
      recordCount = jsonResult.recordCount;
      filename += '.json';
      contentType = 'application/json';
      break;
    }

    default:
      throw new Error(`Unsupported export format: ${format}`);
  }

  return {
    data,
    filename,
    contentType,
    recordCount,
  };
}

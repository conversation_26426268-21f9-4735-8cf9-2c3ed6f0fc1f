/**
 * Chatbot Configuration
 *
 * Contains configuration constants and settings for the chatbot system.
 * This module centralizes all configuration values to improve maintainability
 * and make it easier to adjust settings across the application.
 */

// Google Gemini AI Configuration
export const GEMINI_API_KEY = process.env.GOOGLE_GEMINI_API_KEY;
export const GEMINI_MODEL_NAME =
  process.env.GEMINI_MODEL_NAME || 'gemini-2.5-flash-preview-05-20';

// Request timeout configuration (in milliseconds)
export const REQUEST_TIMEOUT = 45_000; // 45 seconds - industry standard for production AI applications

// Query validation limits
export const MAX_QUERY_LENGTH = 500; // Maximum characters allowed in user queries
export const MIN_QUERY_LENGTH = 2; // Minimum characters required for valid queries

// Response configuration
export const MAX_RESPONSE_LENGTH = 2000; // Maximum response length for analytics data
export const STANDARD_RESPONSE_LENGTH = 500; // Standard response length for non-chart data

// Development configuration
export const ENABLE_DEBUG_LOGGING = process.env.NODE_ENV === 'development';
export const ENABLE_PERFORMANCE_LOGGING =
  process.env.NODE_ENV === 'development';

// Security configuration
export const ENABLE_PROMPT_INJECTION_DETECTION = true;
export const ENABLE_SQL_INJECTION_DETECTION = true;
export const ENABLE_INPUT_SANITIZATION = true;

// AI model configuration
export const DEFAULT_AI_TEMPERATURE = 0.5; // 2025 standard for general conversation
export const LOW_CONFIDENCE_THRESHOLD = 0.3; // Threshold for low confidence responses
export const HIGH_CONFIDENCE_THRESHOLD = 0.8; // Threshold for high confidence responses

// Chart and analytics configuration
export const ENABLE_CHART_GENERATION = true;
export const MAX_CHART_DATA_POINTS = 100; // Maximum data points in charts
export const DEFAULT_CHART_TYPE = 'bar'; // Default chart type for analytics

// Error handling configuration
export const ENABLE_DETAILED_ERRORS = process.env.NODE_ENV === 'development';
export const ENABLE_ERROR_TRACKING = true;

// Validation configuration
export const ALLOWED_DATA_TYPES = [
  'member_demographics',
  'household_info',
  'sacrament_records',
  'census_participation',
  'temporal_analysis',
  'general',
] as const;

export const ALLOWED_ANALYSIS_TYPES = [
  'count',
  'distribution',
  'list',
  'overview',
  'trend',
  'comparison',
] as const;

// Type definitions for configuration
export type AllowedDataType = (typeof ALLOWED_DATA_TYPES)[number];
export type AllowedAnalysisType = (typeof ALLOWED_ANALYSIS_TYPES)[number];

// Configuration validation
export function validateConfiguration(): boolean {
  if (!GEMINI_API_KEY) {
    console.error('GOOGLE_GEMINI_API_KEY environment variable is required');
    return false;
  }

  if (REQUEST_TIMEOUT < 1000) {
    console.error('REQUEST_TIMEOUT must be at least 1000ms');
    return false;
  }

  if (MAX_QUERY_LENGTH < MIN_QUERY_LENGTH) {
    console.error('MAX_QUERY_LENGTH must be greater than MIN_QUERY_LENGTH');
    return false;
  }

  return true;
}

// Get configuration summary for debugging
export function getConfigurationSummary() {
  return {
    model: GEMINI_MODEL_NAME,
    timeout: REQUEST_TIMEOUT,
    maxQueryLength: MAX_QUERY_LENGTH,
    debugEnabled: ENABLE_DEBUG_LOGGING,
    securityEnabled: {
      promptInjection: ENABLE_PROMPT_INJECTION_DETECTION,
      sqlInjection: ENABLE_SQL_INJECTION_DETECTION,
      inputSanitization: ENABLE_INPUT_SANITIZATION,
    },
    aiConfig: {
      temperature: DEFAULT_AI_TEMPERATURE,
      lowConfidenceThreshold: LOW_CONFIDENCE_THRESHOLD,
      highConfidenceThreshold: HIGH_CONFIDENCE_THRESHOLD,
    },
  };
}

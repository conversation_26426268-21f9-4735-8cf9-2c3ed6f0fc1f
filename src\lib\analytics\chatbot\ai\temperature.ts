/**
 * Temperature Logic Module
 *
 * Contains dynamic temperature calculation logic for AI model interactions.
 * Temperature controls the randomness/creativity of AI responses.
 *
 * This module provides:
 * - Optimal temperature calculation based on message characteristics
 * - Heuristic analysis of user input patterns
 * - Dynamic temperature adjustment for different query types
 * - 2025 AI-native temperature optimization
 */

// 2025 AI-Native: Pure AI-driven temperature calculation without hardcoded patterns
export function determineOptimalTemperature(userMessage: string): number {
  const messageLength = userMessage.trim().length;

  // Simple heuristics based on message characteristics (not pattern matching)
  // Short messages (likely conversational) benefit from higher temperature
  if (messageLength <= 10) {
    return 0.7; // Higher for short, likely casual inputs
  }

  // Medium messages could be either casual or formal
  if (messageLength <= 50) {
    return 0.6; // Balanced for medium-length inputs
  }

  // Longer messages are typically more structured/analytical
  if (messageLength > 100) {
    return 0.3; // Lower for complex analytical queries
  }

  // Default modern temperature for general conversation
  return 0.5; // 2025 standard (not the old 0.1)
}

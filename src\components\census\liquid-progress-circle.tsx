'use client';

import { useTranslations } from 'next-intl';
import { Button } from '@/components/ui/button';

interface LiquidProgressCircleProps {
  progress: number; // Progress percentage (0-100)
  onClick?: () => void; // Optional - when provided, makes component clickable
  className?: string;
  showPercentage?: boolean; // Whether to show percentage text
}

/**
 * Enhanced LiquidProgressCircle component - More visible and fluid progress indicator
 *
 * Improvements:
 * - Increased size and contrast for better visibility
 * - More pronounced liquid wave animation
 * - Enhanced color saturation and gradients
 * - Smoother, more organic flowing animations
 * - Better surface tension and liquid physics simulation
 */
export function LiquidProgressCircle({
  progress,
  onClick,
  className = '',
  showPercentage = true,
}: LiquidProgressCircleProps) {
  const t = useTranslations('onboarding');

  // Ensure progress is within valid range
  const clampedProgress = Math.max(0, Math.min(100, progress));

  // Calculate fill height with slight wave offset for more natural look
  const fillHeight = clampedProgress;
  const waveOffset = clampedProgress > 0 ? 2 : 0; // Small wave at surface

  // Determine if component should be clickable based on onClick prop
  const isClickable = Boolean(onClick);

  // Enhanced liquid circle content with improved visibility
  const liquidCircleContent = (
    <div className="relative h-6 w-6 flex-shrink-0 overflow-hidden rounded-full">
      {/* Enhanced empty circle outline at 0% */}
      {clampedProgress === 0 && (
        <div className="absolute inset-0 rounded-full border-2 border-muted-foreground/70 bg-muted/20">
          <div className="absolute inset-1 rounded-full border border-muted-foreground/30" />
        </div>
      )}

      {/* Liquid fill container - enhanced visibility */}
      {clampedProgress > 0 && (
        <>
          {/* Enhanced outer glow with more intensity */}
          <div className="-inset-1 absolute rounded-full bg-gradient-to-br from-[#FF6308]/25 via-[#97A4FF]/20 to-[#BDC9E6]/15 blur-sm" />

          {/* Stronger foundation layer for better contrast */}
          <div className="absolute inset-0 rounded-full bg-gradient-to-br from-[#FF6308] via-[#97A4FF] to-[#BDC9E6] opacity-85" />

          {/* Enhanced base gradient with higher saturation */}
          <div className="absolute inset-0 rounded-full bg-gradient-to-br from-[#FF6308]/90 via-[#97A4FF]/90 to-[#BDC9E6]/90" />

          {/* Stronger inner shadow for more depth */}
          <div
            className="absolute inset-0 rounded-full"
            style={{
              boxShadow:
                'inset 0 2px 4px rgba(0,0,0,0.2), inset 0 -2px 3px rgba(255,255,255,0.3)',
            }}
          />

          {/* Liquid fill mask with wave animation */}
          <div
            className="absolute inset-0 overflow-hidden rounded-full"
            style={{
              clipPath: `inset(${100 - fillHeight - waveOffset}% 0 0 0)`,
            }}
          >
            {/* Enhanced wave surface animation */}
            <div
              className="absolute inset-x-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
              style={{
                height: '2px',
                top: `${100 - fillHeight}%`,
                animation: 'liquidWave 3s ease-in-out infinite',
                transform: 'translateY(-1px)',
              }}
            />

            {/* Enhanced flowing color blob 1 - More visible orange */}
            <div
              className="-top-2 -left-2 absolute h-4 w-4 rounded-full"
              style={{
                background:
                  'radial-gradient(circle, #FF6308 0%, #FF6308 30%, rgba(255,99,8,0.8) 60%, transparent 80%)',
                animation: 'liquidBlob1 6s ease-in-out infinite',
                filter: 'blur(0.8px)',
              }}
            />

            {/* Enhanced flowing color blob 2 - More visible purple */}
            <div
              className="-top-2 -right-2 absolute h-5 w-5 rounded-full"
              style={{
                background:
                  'radial-gradient(circle, #97A4FF 0%, #97A4FF 25%, rgba(151,164,255,0.9) 55%, transparent 75%)',
                animation: 'liquidBlob2 8s ease-in-out infinite',
                filter: 'blur(0.8px)',
              }}
            />

            {/* Enhanced flowing color blob 3 - More visible light blue */}
            <div
              className="-bottom-1 -left-1 absolute h-3 w-3 rounded-full"
              style={{
                background:
                  'radial-gradient(circle, #BDC9E6 0%, #BDC9E6 35%, rgba(189,201,230,0.9) 70%, transparent 85%)',
                animation: 'liquidBlob3 7s ease-in-out infinite',
                filter: 'blur(0.6px)',
              }}
            />

            {/* Additional organic flowing elements */}
            <div
              className="absolute top-1/2 right-0 h-3 w-3 rounded-full"
              style={{
                background:
                  'radial-gradient(circle, #FF6308 0%, rgba(255,99,8,0.9) 30%, transparent 60%)',
                animation: 'liquidBlob4 5s ease-in-out infinite',
                filter: 'blur(0.5px)',
              }}
            />

            <div
              className="absolute top-1/4 left-1/4 h-2 w-2 rounded-full"
              style={{
                background:
                  'radial-gradient(circle, #97A4FF 0%, rgba(151,164,255,0.8) 40%, transparent 70%)',
                animation: 'liquidBlob5 9s ease-in-out infinite',
                filter: 'blur(0.4px)',
              }}
            />

            {/* Enhanced breathing overlay with more intensity */}
            <div
              className="absolute inset-0 rounded-full bg-gradient-to-tr from-[#FF6308]/30 via-transparent to-[#97A4FF]/25"
              style={{
                animation: 'liquidBreathe 3s ease-in-out infinite',
              }}
            />

            {/* Enhanced shimmer effect */}
            <div
              className="absolute inset-0 rounded-full bg-gradient-to-t from-transparent via-white/15 to-white/25"
              style={{
                animation: 'liquidShimmer 2.5s ease-in-out infinite',
              }}
            />
          </div>

          {/* Enhanced surface tension effect */}
          <div
            className="absolute inset-0 rounded-full transition-all duration-700 ease-out"
            style={{
              background: `linear-gradient(to top,
                rgba(255,255,255,0.2) 0%,
                rgba(255,255,255,0.4) 3px,
                transparent 6px
              )`,
              transform: `translateY(${100 - fillHeight}%)`,
              opacity: fillHeight > 0 ? 0.9 : 0,
            }}
          />

          {/* Enhanced surface reflection */}
          <div
            className="absolute inset-0 rounded-full bg-gradient-to-b from-white/30 via-white/10 to-transparent"
            style={{
              height: '50%',
              opacity: fillHeight > 15 ? 0.8 : 0,
              transition: 'opacity 700ms ease-out',
            }}
          />
        </>
      )}
    </div>
  );

  // Conditional rendering based on onClick prop presence
  if (isClickable) {
    return (
      <Button
        aria-label={t('progressBadgeTooltip', {
          progress: clampedProgress.toString(),
        })}
        className={`flex h-8 items-center gap-2 rounded-full px-2 py-1 transition-colors hover:bg-accent ${className}`}
        onClick={onClick}
        title={t('progressBadgeTooltip', {
          progress: clampedProgress.toString(),
        })}
        variant="ghost"
      >
        {liquidCircleContent}
        {/* Enhanced percentage text */}
        {showPercentage && (
          <span className="font-medium text-primary text-xs leading-none">
            {clampedProgress}%
          </span>
        )}

        {/* Enhanced scoped CSS animations */}
        <style jsx>{`
          @keyframes liquidWave {
            0%, 100% {
              transform: translateY(-1px) scaleX(1);
              opacity: 0.6;
            }
            50% {
              transform: translateY(-2px) scaleX(1.1);
              opacity: 0.8;
            }
          }

          @keyframes liquidBlob1 {
            0% {
              transform: translate(0px, 0px) scale(1) rotate(0deg);
              opacity: 0.9;
            }
            25% {
              transform: translate(20px, 10px) scale(1.3) rotate(90deg);
              opacity: 1;
            }
            50% {
              transform: translate(25px, 22px) scale(0.8) rotate(180deg);
              opacity: 0.8;
            }
            75% {
              transform: translate(10px, 25px) scale(1.2) rotate(270deg);
              opacity: 0.95;
            }
            100% {
              transform: translate(0px, 0px) scale(1) rotate(360deg);
              opacity: 0.9;
            }
          }

          @keyframes liquidBlob2 {
            0% {
              transform: translate(0px, 0px) scale(1) rotate(0deg);
              opacity: 0.8;
            }
            30% {
              transform: translate(-15px, 12px) scale(0.9) rotate(108deg);
              opacity: 0.9;
            }
            60% {
              transform: translate(-22px, -10px) scale(1.4) rotate(216deg);
              opacity: 0.7;
            }
            90% {
              transform: translate(-5px, -18px) scale(1.1) rotate(324deg);
              opacity: 0.85;
            }
            100% {
              transform: translate(0px, 0px) scale(1) rotate(360deg);
              opacity: 0.8;
            }
          }

          @keyframes liquidBlob3 {
            0% {
              transform: translate(0px, 0px) scale(1) rotate(0deg);
              opacity: 0.7;
            }
            40% {
              transform: translate(15px, -8px) scale(1.5) rotate(144deg);
              opacity: 0.9;
            }
            80% {
              transform: translate(-10px, -15px) scale(0.7) rotate(288deg);
              opacity: 0.6;
            }
            100% {
              transform: translate(0px, 0px) scale(1) rotate(360deg);
              opacity: 0.7;
            }
          }

          @keyframes liquidBlob4 {
            0% {
              transform: translate(0px, 0px) scale(1) rotate(0deg);
              opacity: 0.6;
            }
            50% {
              transform: translate(-10px, 8px) scale(1.6) rotate(180deg);
              opacity: 0.8;
            }
            100% {
              transform: translate(0px, 0px) scale(1) rotate(360deg);
              opacity: 0.6;
            }
          }

          @keyframes liquidBlob5 {
            0% {
              transform: translate(0px, 0px) scale(1) rotate(0deg);
              opacity: 0.5;
            }
            33% {
              transform: translate(8px, -5px) scale(1.2) rotate(120deg);
              opacity: 0.7;
            }
            66% {
              transform: translate(-6px, 10px) scale(0.8) rotate(240deg);
              opacity: 0.6;
            }
            100% {
              transform: translate(0px, 0px) scale(1) rotate(360deg);
              opacity: 0.5;
            }
          }

          @keyframes liquidBreathe {
            0%, 100% {
              opacity: 0.4;
              transform: scale(1);
            }
            50% {
              opacity: 0.6;
              transform: scale(1.03);
            }
          }

          @keyframes liquidShimmer {
            0%, 100% {
              opacity: 0.7;
              transform: translateY(0px) scale(1);
            }
            50% {
              opacity: 0.9;
              transform: translateY(-1px) scale(1.01);
            }
          }
        `}</style>
      </Button>
    );
  }

  // Non-clickable version with enhanced visibility
  return (
    <div className={`flex items-center justify-center ${className}`}>
      {liquidCircleContent}
      {/* Enhanced percentage text for non-clickable version */}
      {showPercentage && (
        <span className="ml-2 font-medium text-primary text-xs leading-none">
          {clampedProgress}%
        </span>
      )}

      {/* Enhanced scoped CSS animations for non-clickable version */}
      <style jsx>{`
        @keyframes liquidWave {
          0%, 100% {
            transform: translateY(-1px) scaleX(1);
            opacity: 0.6;
          }
          50% {
            transform: translateY(-2px) scaleX(1.1);
            opacity: 0.8;
          }
        }

        @keyframes liquidBlob1 {
          0% {
            transform: translate(0px, 0px) scale(1) rotate(0deg);
            opacity: 0.9;
          }
          25% {
            transform: translate(20px, 10px) scale(1.3) rotate(90deg);
            opacity: 1;
          }
          50% {
            transform: translate(25px, 22px) scale(0.8) rotate(180deg);
            opacity: 0.8;
          }
          75% {
            transform: translate(10px, 25px) scale(1.2) rotate(270deg);
            opacity: 0.95;
          }
          100% {
            transform: translate(0px, 0px) scale(1) rotate(360deg);
            opacity: 0.9;
          }
        }

        @keyframes liquidBlob2 {
          0% {
            transform: translate(0px, 0px) scale(1) rotate(0deg);
            opacity: 0.8;
          }
          30% {
            transform: translate(-15px, 12px) scale(0.9) rotate(108deg);
            opacity: 0.9;
          }
          60% {
            transform: translate(-22px, -10px) scale(1.4) rotate(216deg);
            opacity: 0.7;
          }
          90% {
            transform: translate(-5px, -18px) scale(1.1) rotate(324deg);
            opacity: 0.85;
          }
          100% {
            transform: translate(0px, 0px) scale(1) rotate(360deg);
            opacity: 0.8;
          }
        }

        @keyframes liquidBlob3 {
          0% {
            transform: translate(0px, 0px) scale(1) rotate(0deg);
            opacity: 0.7;
          }
          40% {
            transform: translate(15px, -8px) scale(1.5) rotate(144deg);
            opacity: 0.9;
          }
          80% {
            transform: translate(-10px, -15px) scale(0.7) rotate(288deg);
            opacity: 0.6;
          }
          100% {
            transform: translate(0px, 0px) scale(1) rotate(360deg);
            opacity: 0.7;
          }
        }

        @keyframes liquidBlob4 {
          0% {
            transform: translate(0px, 0px) scale(1) rotate(0deg);
            opacity: 0.6;
          }
          50% {
            transform: translate(-10px, 8px) scale(1.6) rotate(180deg);
            opacity: 0.8;
          }
          100% {
            transform: translate(0px, 0px) scale(1) rotate(360deg);
            opacity: 0.6;
          }
        }

        @keyframes liquidBlob5 {
          0% {
            transform: translate(0px, 0px) scale(1) rotate(0deg);
            opacity: 0.5;
          }
          33% {
            transform: translate(8px, -5px) scale(1.2) rotate(120deg);
            opacity: 0.7;
          }
          66% {
            transform: translate(-6px, 10px) scale(0.8) rotate(240deg);
            opacity: 0.6;
          }
          100% {
            transform: translate(0px, 0px) scale(1) rotate(360deg);
            opacity: 0.5;
          }
        }

        @keyframes liquidBreathe {
          0%, 100% {
            opacity: 0.4;
            transform: scale(1);
          }
          50% {
            opacity: 0.6;
            transform: scale(1.03);
          }
        }

        @keyframes liquidShimmer {
          0%, 100% {
            opacity: 0.7;
            transform: translateY(0px) scale(1);
          }
          50% {
            opacity: 0.9;
            transform: translateY(-1px) scale(1.01);
          }
        }
      `}</style>
    </div>
  );
}

import type { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import { SettingsPageClient } from '@/components/admin/settings/SettingsPageClient';
import { requireAdmin } from '@/lib/auth/auth-utils';

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations('metadata');

  return {
    title: t('systemSettingsTitle'),
    description: t('systemSettingsDescription'),
  };
}

export default async function SettingsPage() {
  // Server-side authentication check
  await requireAdmin();

  const t = await getTranslations('admin');

  return (
    <div className="space-y-8">
      <div>
        <h1 className="mb-2 font-bold text-2xl">{t('systemSettings')}</h1>
        <p className="mb-6 text-muted-foreground">
          {t('systemSettingsDescription')}
        </p>
      </div>

      <SettingsPageClient />
    </div>
  );
}

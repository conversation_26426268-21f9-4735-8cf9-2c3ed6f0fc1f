'use client';

import { <PERSON><PERSON><PERSON><PERSON>gle, <PERSON>O<PERSON>, Home, RefreshCw } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  const [isVisible, setIsVisible] = useState(false);
  const isDev = process.env.NODE_ENV === 'development';
  const t = useTranslations('errors');

  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
    }

    const timer = setTimeout(() => setIsVisible(true), 100);
    return () => clearTimeout(timer);
  }, []);

  return (
    <html lang="en">
      <body className="bg-background text-foreground antialiased">
        <div className="relative flex min-h-screen flex-col items-center justify-center overflow-hidden p-4">

          <div className="-z-10 absolute inset-0 overflow-hidden">
            <div
              aria-hidden="true"
              className="-z-10 -translate-x-1/2 xl:-top-6 absolute top-0 left-1/2 blur-3xl"
            >
              <div
                className="aspect-[1155/678] w-[72.1875rem] bg-gradient-to-tr from-destructive/20 to-destructive-foreground/20 opacity-30"
                style={{
                  clipPath:
                    'polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)',
                }}
              />
            </div>
          </div>

          <div
            className={`relative z-10 w-full max-w-md transition-all duration-700 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}
          >
            <div className="text-center">
              <div className="mb-4 inline-flex items-center justify-center rounded-full bg-destructive/10 p-3 text-destructive">
                <AlertTriangle className="h-8 w-8" />
              </div>

              <h1 className="mb-2 bg-gradient-to-r from-destructive to-destructive/70 bg-clip-text font-bold text-5xl text-transparent tracking-tight">
                {t('criticalError')}
              </h1>
              <h2 className="mb-4 font-semibold text-2xl">
                {t('somethingWentWrong')}
              </h2>


              <div className="mx-auto mb-4 max-w-md">
                <div className="flex items-start gap-3">
                  <BookOpen className="mt-1 h-5 w-5 flex-shrink-0 text-destructive" />
                  <div>
                    <p className="mb-2 text-lg italic">
                      &quot;The Lord is my strength and my shield; my heart
                      trusts in him, and he helps me.&quot;
                    </p>
                    <p className="text-right text-muted-foreground text-sm">
                      — Psalm 28:7
                    </p>
                  </div>
                </div>
              </div>

              <p className="mb-8 text-muted-foreground">
                {t('criticalErrorDescription')}
              </p>

              <div className="mb-8 flex flex-wrap justify-center gap-4">
                <Button
                  className="gap-2 rounded-md px-6 shadow-lg transition-all hover:shadow-xl"
                  onClick={reset}
                  size="lg"
                >
                  <RefreshCw className="h-5 w-5" />
                  {t('tryAgain')}
                </Button>
                <Button
                  className="gap-2 rounded-md border-destructive/20 px-6 hover:bg-destructive/5"
                  onClick={() => (window.location.href = '/')}
                  size="lg"
                  variant="outline"
                >
                  <Home className="h-5 w-5" />
                  {t('backToHome')}
                </Button>
              </div>

              {isDev && (
                <div className="mt-8 rounded-md border border-destructive/10 bg-muted p-4 text-left">
                  <p className="mb-2 font-mono text-sm">{error.message}</p>
                  {error.stack && (
                    <pre className="max-h-40 overflow-auto whitespace-pre-wrap font-mono text-xs">
                      {error.stack}
                    </pre>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </body>
    </html>
  );
}

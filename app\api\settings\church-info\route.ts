import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { getTranslations } from 'next-intl/server';
import { authOptions } from '@/lib/auth/auth-options';
import { getChurchInfo, updateChurchInfo } from '@/lib/db/settings';
import {
  getErrorMessage,
  getZodErrorDetails,
  isZodError,
} from '@/lib/utils/error-handling';
import { getLocaleFromCookies } from '@/lib/utils/server-messages';
import { createChurchInfoSchema } from '@/lib/validation/settings';

export async function GET(_request: Request) {
  // Get locale from centralized utility at the beginning
  const locale = await getLocaleFromCookies();
  const tAdmin = await getTranslations({ locale, namespace: 'admin' });

  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Require authentication for accessing settings
    if (!session || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: tAdmin('unauthorized') },
        { status: 401 }
      );
    }

    try {
      // Add a log to help debug connection issues
      if (process.env.NODE_ENV === 'development') {
      }

      // Try to get church info from database (now with caching)
      const churchInfo = await getChurchInfo();
      return NextResponse.json(churchInfo);
    } catch (_dbError) {
      // If database connection fails, return default values
      if (process.env.NODE_ENV === 'development') {
      }

      // Return default values for development
      // Note: For full internationalization, fallback data should be handled client-side
      // with proper locale context. These are English fallbacks for API consistency.
      return NextResponse.json({
        churchName: 'WSCCC Church',
        email: '<EMAIL>',
        contactNumber: '0412345678',
        address: '123 Main Street, Sydney NSW 2000',
      });
    }
  } catch (error) {
    return NextResponse.json(
      {
        error: tAdmin('failedToFetchChurchInformation'),
        details: getErrorMessage(error),
      },
      { status: 500 }
    );
  }
}

export async function PUT(request: Request) {
  // Get locale from centralized utility at the beginning
  const locale = await getLocaleFromCookies();
  const tAdmin = await getTranslations({ locale, namespace: 'admin' });
  const tErrors = await getTranslations({ locale, namespace: 'errors' });

  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Require authentication for updating settings
    if (!session || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: tAdmin('unauthorized') },
        { status: 401 }
      );
    }

    // Parse and validate request body with translations
    const data = await request.json();
    const churchInfoSchema = await createChurchInfoSchema(locale);
    const validatedData = churchInfoSchema.parse(data);

    try {
      // Try to update church information
      await updateChurchInfo(validatedData);

      return NextResponse.json({
        message: tErrors('churchInformationUpdatedSuccessfully'),
      });
    } catch (_dbError) {
      return NextResponse.json({
        message: tAdmin('settingsWouldBeSavedInProduction'),
        data: validatedData,
      });
    }
  } catch (error) {
    // Handle validation errors
    if (isZodError(error)) {
      return NextResponse.json(
        {
          error: tAdmin('validationFailed'),
          details: getZodErrorDetails(error),
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        error: tAdmin('failedToUpdateChurchInformation'),
        details: getErrorMessage(error),
      },
      { status: 500 }
    );
  }
}

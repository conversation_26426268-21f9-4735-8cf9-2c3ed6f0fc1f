import { type NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { getTranslations } from 'next-intl/server';
import { z } from 'zod/v4';
import { authOptions } from '@/lib/auth/auth-options';
import { prisma } from '@/lib/db/prisma';
import { getLocaleFromCookies } from '@/lib/utils/server-messages';
import { createHomepageAnnouncementSchema } from '@/lib/validation/settings';

/**
 * GET /api/settings/homepage-announcement
 * Retrieve current homepage announcement settings
 */
export async function GET() {
  const locale = await getLocaleFromCookies();
  const tAdmin = await getTranslations({ locale, namespace: 'admin' });

  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    if (!session || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: tAdmin('unauthorized') },
        { status: 401 }
      );
    }

    // Fetch homepage announcement settings
    const settings = await prisma.systemSettings.findMany({
      where: {
        settingKey: {
          in: [
            'homepage_announcement_enabled',
            'homepage_announcement_text',
            'homepage_announcement_type',
          ],
        },
      },
    });

    // Convert to object format
    const settingsMap = settings.reduce(
      (acc, setting) => {
        acc[setting.settingKey] = setting.settingValue;
        return acc;
      },
      {} as Record<string, string | null>
    );

    return NextResponse.json({
      enabled: settingsMap.homepage_announcement_enabled === 'true',
      text:
        settingsMap.homepage_announcement_text ||
        'Welcome to [CHURCH_NAME]! [CENSUS_STATUS]',
      type: settingsMap.homepage_announcement_type || 'info',
    });
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
    }
    return NextResponse.json(
      {
        error: tAdmin('failedToFetchData'),
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/settings/homepage-announcement
 * Update homepage announcement settings
 */
export async function POST(request: NextRequest) {
  const locale = await getLocaleFromCookies();
  const tAdmin = await getTranslations({ locale, namespace: 'admin' });

  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    if (!session || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: tAdmin('unauthorized') },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();

    // Check if this is a preview request
    if (body.preview === true) {
      return handlePreview(body.text, locale, tAdmin);
    }

    // Validate data for settings update using centralized schema
    const homepageAnnouncementSchema =
      await createHomepageAnnouncementSchema(locale);
    const validatedData = homepageAnnouncementSchema.parse(body);

    // Update settings in database (records already exist from initialization)
    const updatePromises = [
      prisma.systemSettings.update({
        where: { settingKey: 'homepage_announcement_enabled' },
        data: { settingValue: validatedData.enabled.toString() },
      }),
      prisma.systemSettings.update({
        where: { settingKey: 'homepage_announcement_text' },
        data: { settingValue: validatedData.text },
      }),
      prisma.systemSettings.update({
        where: { settingKey: 'homepage_announcement_type' },
        data: { settingValue: validatedData.type },
      }),
    ];

    await Promise.all(updatePromises);

    return NextResponse.json({
      success: true,
      message: tAdmin('homepageAnnouncementSettingsSaved'),
    });
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
    }

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: tAdmin('validationError'),
          details: error.issues,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        error: tAdmin('failedToUpdateData'),
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

/**
 * Handle preview requests via POST body (secure)
 */
async function handlePreview(text: string, locale: string, tAdmin: any) {
  try {
    // Get current census data for preview
    const censusSettings = await prisma.systemSettings.findMany({
      where: {
        settingKey: {
          in: [
            'system_open',
            'census_start_date',
            'census_end_date',
            'church_name',
          ],
        },
      },
    });

    const settingsMap = censusSettings.reduce(
      (acc, setting) => {
        acc[setting.settingKey] = setting.settingValue;
        return acc;
      },
      {} as Record<string, string | null>
    );

    // Import placeholder processor dynamically
    const { processPlaceholders, parseDate } = await import(
      '@/lib/homepage/placeholder-processor'
    );

    const censusData = {
      isOpen: settingsMap.system_open === 'true',
      startDate: settingsMap.census_start_date
        ? parseDate(settingsMap.census_start_date) || undefined
        : undefined,
      endDate: settingsMap.census_end_date
        ? parseDate(settingsMap.census_end_date) || undefined
        : undefined,
      churchName: settingsMap.church_name || '',
    };

    const processed = processPlaceholders(text, { censusData, locale });

    return NextResponse.json({ processed });
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
    }
    return NextResponse.json(
      {
        error: tAdmin('failedToGeneratePreview'),
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

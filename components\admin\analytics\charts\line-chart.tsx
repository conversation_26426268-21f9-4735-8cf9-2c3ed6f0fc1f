'use client';

import {
  Cartes<PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from 'recharts';
import { type ChartConfig, ChartContainer } from '@/components/ui/chart';
import { type BaseChartProps, registerChart } from './chart-registry';
import {
  CHART_CONFIGS,
  CHART_DEFAULTS,
  MOBILE_CHART_CONFIGS,
} from './constants';
import { formatChartValue } from './utils';

interface LineDataPoint {
  name: string;
  value: number;
  [key: string]: unknown;
}

function LineChartComponent({
  data,
  isAnimationActive = true,
  className,
}: BaseChartProps) {
  const config = data.config || {};
  const { showGrid = true } = config;

  // Check for responsive configuration
  const responsiveConfig = (data as any)._responsive;
  const isMobile = responsiveConfig?.isMobile;
  const chartHeight = responsiveConfig?.height || CHART_DEFAULTS.HEIGHT;
  const chartMargins =
    responsiveConfig?.margins || CHART_DEFAULTS.MARGINS.DEFAULT;
  const fontSize =
    responsiveConfig?.fontSize || MOBILE_CHART_CONFIGS.FONT_SIZES.DESKTOP;

  // Process data for line chart
  const processedData = data.data.map((item) => {
    const lineItem = item as LineDataPoint;
    return {
      ...lineItem,
      name: lineItem.name,
      value: lineItem.value,
    };
  });

  // Create chart config for shadcn using theme approach
  const chartConfig: ChartConfig = {
    [data.yKey || 'value']: {
      label: data.yKey || 'Value',
      theme: {
        light: 'oklch(0.646 0.222 41.116)',
        dark: 'oklch(0.488 0.243 264.376)',
      },
    },
  };

  // Custom tooltip
  const CustomTooltip = ({
    active,
    payload,
    label,
  }: {
    active?: boolean;
    payload?: Array<{ color: string; dataKey: string; value: number | string }>;
    label?: string;
  }) => {
    if (active && payload && payload.length) {
      const data = payload[0];
      if (!data || data.value === undefined || data.value === null) {
        return null;
      }

      return (
        <div className="rounded-lg border border-slate-200 bg-white p-3 shadow-lg dark:border-slate-600 dark:bg-slate-800">
          <p className="mb-2 font-medium text-slate-900 dark:text-slate-100">
            {label}
          </p>
          <div className="flex items-center gap-2 text-sm">
            <div
              className="h-3 w-3 rounded-full"
              style={{ backgroundColor: data.color }}
            />
            <span className="text-slate-600 dark:text-slate-400">Value:</span>
            <span className="font-medium text-slate-900 dark:text-slate-100">
              {formatChartValue(data.value)}
            </span>
          </div>
        </div>
      );
    }
    return null;
  };

  return (
    <div className={`w-full ${className || ''}`}>
      <ChartContainer
        className="w-full"
        config={chartConfig}
        style={{ height: `${chartHeight}px` }}
      >
        <LineChart data={processedData} margin={chartMargins}>
          {showGrid && (
            <CartesianGrid className="opacity-30" strokeDasharray="3 3" />
          )}
          <XAxis
            angle={
              processedData.length > CHART_CONFIGS.LINE.LABEL_ANGLE_THRESHOLD
                ? -45
                : 0
            }
            className="text-xs"
            dataKey={data.xKey || 'name'}
            height={
              processedData.length > CHART_CONFIGS.LINE.LABEL_ANGLE_THRESHOLD
                ? isMobile
                  ? 50
                  : 60
                : isMobile
                  ? 25
                  : 30
            }
            interval={0}
            textAnchor={
              processedData.length > CHART_CONFIGS.LINE.LABEL_ANGLE_THRESHOLD
                ? 'end'
                : 'middle'
            }
            tick={{ fontSize: fontSize.tick }}
          />
          <YAxis
            className="text-xs"
            tick={{ fontSize: fontSize.tick }}
            width={isMobile ? 35 : 45}
          />
          <Tooltip content={<CustomTooltip />} />
          <Line
            activeDot={{
              r: CHART_CONFIGS.LINE.ACTIVE_DOT_RADIUS,
              stroke: `var(--color-${data.yKey || 'value'})`,
              strokeWidth: 2,
              fill: '#ffffff',
              className: 'drop-shadow-md',
            }}
            animationBegin={0}
            animationDuration={CHART_DEFAULTS.ANIMATION_DURATION}
            dataKey={data.yKey || 'value'}
            dot={{
              fill: `var(--color-${data.yKey || 'value'})`,
              strokeWidth: 2,
              r: CHART_CONFIGS.LINE.DOT_RADIUS,
              className: 'drop-shadow-sm',
            }}
            isAnimationActive={isAnimationActive}
            stroke={`var(--color-${data.yKey || 'value'})`}
            strokeWidth={CHART_CONFIGS.LINE.STROKE_WIDTH}
            type="monotone"
          />
        </LineChart>
      </ChartContainer>
    </div>
  );
}

// Register the component
registerChart('line', LineChartComponent);

export default LineChartComponent;

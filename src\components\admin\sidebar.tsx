'use client';

import {
  Bar<PERSON>hart3,
  ChevronLeft,
  ChevronRight,
  HelpCircle,
  Home,
  LayoutDashboard,
  LogOut,
  Scroll,
  Settings,
  Users,
} from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { useState } from 'react';
import { LanguageSelector } from '@/components/language/language-selector';
import { ThemeToggle } from '@/components/theme/theme-toggle';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { useAdminAuth } from '@/hooks/useAdminAuth';
import { cn } from '@/lib/utils';

interface SidebarProps {
  className?: string;
}

export function Sidebar({ className }: SidebarProps) {
  const [collapsed, setCollapsed] = useState(false);
  const pathname = usePathname();
  const { signOutFromAdmin } = useAdminAuth();
  const t = useTranslations('navigation');
  const tCommon = useTranslations('common');
  const tBrand = useTranslations('brand');

  const navItems = [
    {
      name: t('dashboard'),
      href: '/admin/dashboard',
      icon: <LayoutDashboard className="h-5 w-5" />,
    },
    {
      name: t('household'),
      href: '/admin/household',
      icon: <Home className="h-5 w-5" />,
    },
    {
      name: t('members'),
      href: '/admin/members',
      icon: <Users className="h-5 w-5" />,
    },
    {
      name: t('uniqueCode'),
      href: '/admin/unique-code',
      icon: <Scroll className="h-5 w-5" />,
    },
    {
      name: t('analytics'),
      href: '/admin/analytics',
      icon: <BarChart3 className="h-5 w-5" />,
    },
    {
      name: t('settings'),
      href: '/admin/settings',
      icon: <Settings className="h-5 w-5" />,
    },
  ];

  return (
    <div
      className={cn(
        'flex h-screen flex-col border-sidebar-border border-r bg-sidebar text-sidebar-foreground transition-all duration-300',
        collapsed ? 'w-0 overflow-hidden' : 'w-[250px]',
        className
      )}
    >
      <div className="absolute top-4 left-4 z-10">
        {collapsed && (
          <Button
            aria-label={tCommon('expandSidebar')}
            className="bg-sidebar-primary text-sidebar-primary-foreground"
            onClick={() => setCollapsed(false)}
            size="icon"
            variant="outline"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        )}
      </div>
      {/* Logo and Title */}
      <div className="flex items-center p-4">
        <Link
          className={cn(
            'flex flex-1 items-center gap-3 rounded-md px-2 py-1 transition-colors',
            'text-sidebar-foreground hover:bg-sidebar-accent/50 hover:text-sidebar-accent-foreground'
          )}
          href="/admin/dashboard"
        >
          <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
            <svg
              fill="none"
              height="16"
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              viewBox="0 0 24 24"
              width="16"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path d="M3 9h18v10a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V9Z" />
              <path d="M3 9V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v4" />
              <path d="M13 13h4" />
              <path d="M13 17h4" />
              <path d="M7 13h2v4H7z" />
            </svg>
          </div>
          <div className="grid flex-1 text-left text-sm leading-tight">
            <span className="truncate font-semibold">{tBrand('name')}</span>
            <span className="truncate text-sidebar-foreground/70 text-xs">
              {tCommon('adminPortal')}
            </span>
          </div>
        </Link>
        <Button
          aria-label={tCommon('collapseSidebar')}
          className="ml-auto"
          onClick={() => setCollapsed(true)}
          size="icon"
          variant="ghost"
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>
      </div>

      <Separator className="bg-sidebar-border" />

      {/* Navigation Links */}
      <nav className="flex-1 overflow-y-auto py-4">
        <ul className="space-y-1 px-2">
          {navItems.map((item) => (
            <li key={item.href}>
              <Link
                className={cn(
                  'flex cursor-pointer items-center rounded-md px-3 py-2 font-medium text-sm transition-colors',
                  pathname === item.href
                    ? 'bg-sidebar-accent text-sidebar-accent-foreground'
                    : 'text-sidebar-foreground hover:bg-sidebar-accent/50 hover:text-sidebar-accent-foreground'
                )}
                href={item.href}
              >
                {item.icon}
                {!collapsed && <span className="ml-3">{item.name}</span>}
              </Link>
            </li>
          ))}
        </ul>
      </nav>

      {/* Bottom Section */}
      <div className="mt-auto p-4">
        <Separator className="mb-4 bg-sidebar-border" />
        <div className="space-y-3">
          {/* Controls Row */}
          <div
            className={cn(
              'mb-3 flex items-center gap-2 px-2',
              collapsed ? 'justify-center' : 'justify-start'
            )}
          >
            {/* Language Selector */}
            <LanguageSelector variant="sidebar" />

            {/* Theme Toggle */}
            <ThemeToggle />
          </div>

          {/* FAQ Link */}
          <Link
            className={cn(
              'flex cursor-pointer items-center rounded-md px-3 py-2 font-medium text-sm transition-colors',
              'text-sidebar-foreground hover:bg-sidebar-accent/50 hover:text-sidebar-accent-foreground'
            )}
            href="/help"
          >
            <HelpCircle className="h-5 w-5" />
            {!collapsed && <span className="ml-3">{t('faq')}</span>}
          </Link>

          {/* Logout Link */}
          <button
            className={cn(
              'flex w-full cursor-pointer items-center rounded-md px-3 py-2 font-medium text-sm transition-colors',
              'text-sidebar-foreground hover:bg-sidebar-accent/50 hover:text-sidebar-accent-foreground'
            )}
            onClick={() => signOutFromAdmin()}
          >
            <LogOut className="h-5 w-5" />
            {!collapsed && <span className="ml-3">{t('logout')}</span>}
          </button>
        </div>
      </div>
    </div>
  );
}

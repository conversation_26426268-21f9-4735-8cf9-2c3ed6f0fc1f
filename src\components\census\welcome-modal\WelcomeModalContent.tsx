'use client';

import { useTranslations } from 'next-intl';
import { LiquidProgressCircle } from '@/components/census/liquid-progress-circle';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { useCensusProgress } from '@/hooks/use-census-progress';
import type { IWelcomeModalContentProps } from '@/types/welcome-modal';
import { NextStepsSection } from './NextStepsSection';

/**
 * WelcomeModalContent component provides the main content for the welcome modal
 * Implements progressive disclosure and clear call-to-action buttons
 */
export function WelcomeModalContent({
  onGetStarted,
}: IWelcomeModalContentProps) {
  const t = useTranslations('onboarding');
  const { progress } = useCensusProgress();

  const handleGetStarted = () => {
    onGetStarted();
  };

  return (
    <div className="space-y-6">
      {/* Welcome Header with Liquid Progress Circle and Percentage */}
      <div className="text-center">
        {/*
          Professional container for scaled content with integrated percentage:
          - Base size: 24px (w-6 h-6)
          - Scale factor: 2.5x = 60px visual size
          - Container size: 60px to accommodate scaled content
          - Minimal padding for tighter spacing with percentage
        */}
        <div className="flex flex-col items-center space-y-2">
          <div
            className="flex items-center justify-center"
            style={{
              width: '96px', // 60px scaled + 36px padding (18px each side)
              height: '88px', // Reduced height: 60px scaled + 28px padding (18px top, 10px bottom)
              paddingTop: '18px',
              paddingLeft: '18px',
              paddingRight: '18px',
              paddingBottom: '10px', // Reduced from 8px to 10px for minimal visual breathing room
            }}
          >
            <LiquidProgressCircle
              className="origin-center scale-[2.5]"
              progress={progress}
              showPercentage={false}
            />
          </div>

          {/* Progress Summary - Moved directly under progress circle */}
          {progress > 0 && (
            <p className="-mt-1 text-muted-foreground text-sm">
              <span
                className={progress === 100 ? 'font-medium text-green-600' : ''}
              >
                {progress}%
              </span>
            </p>
          )}
        </div>
      </div>

      <Separator className="bg-gradient-to-r from-transparent via-border to-transparent" />

      {/* Next Steps Section */}
      <NextStepsSection onGetStarted={handleGetStarted} />
    </div>
  );
}

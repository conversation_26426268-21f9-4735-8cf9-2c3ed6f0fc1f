import type { Metada<PERSON> } from 'next';
import { getTranslations } from 'next-intl/server';
import { requireAdmin } from '@/lib/auth/auth-utils';
import { AnalyticsChatbotAISDKClient } from './analytics-chatbot-ai-sdk-client';

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations('metadata');

  return {
    title: t('analyticsTitle'),
    description: t('analyticsDescription'),
  };
}

export default async function AnalyticsPage() {
  // Server-side authentication check
  await requireAdmin();

  return <AnalyticsChatbotAISDKClient />;
}

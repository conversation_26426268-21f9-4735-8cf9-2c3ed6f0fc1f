/**
 * Unique Codes Database Operations with Prisma
 * Replaces MySQL-based implementation with Prisma ORM
 */

import type { UniqueCode } from '@prisma/client';
import * as crypto from 'crypto';
import { prisma } from './prisma';

export async function getUniqueCodes(): Promise<UniqueCode[]> {
  return await prisma.uniqueCode.findMany({
    orderBy: { createdAt: 'desc' },
  });
}

export async function getUniqueCodeById(
  id: number
): Promise<UniqueCode | null> {
  return await prisma.uniqueCode.findUnique({
    where: { id },
  });
}

export async function getUniqueCodeByCode(
  code: string
): Promise<UniqueCode | null> {
  return await prisma.uniqueCode.findUnique({
    where: { code },
  });
}

export async function getUniqueCodesByHouseholdId(
  householdId: number
): Promise<UniqueCode[]> {
  return await prisma.uniqueCode.findMany({
    where: { householdId },
  });
}

export async function getUniqueCodesByCensusYear(
  censusYearId: number
): Promise<UniqueCode[]> {
  return await prisma.uniqueCode.findMany({
    where: { censusYearId },
  });
}

export async function getUnassignedUniqueCodesByCensusYear(
  censusYearId: number
): Promise<UniqueCode[]> {
  return await prisma.uniqueCode.findMany({
    where: {
      censusYearId,
      isAssigned: false,
    },
  });
}

export async function createUniqueCode(data: {
  code: string;
  isAssigned?: boolean;
  assignedAt?: Date | null;
  householdId?: number | null;
  censusYearId: number;
  validationStart: number;
  validationEnd: number;
  validationHash: string;
}): Promise<UniqueCode> {
  return await prisma.uniqueCode.create({
    data: {
      code: data.code,
      isAssigned: data.isAssigned ?? false,
      assignedAt: data.assignedAt,
      householdId: data.householdId,
      censusYearId: data.censusYearId,
      validationStart: data.validationStart,
      validationEnd: data.validationEnd,
      validationHash: data.validationHash,
    },
  });
}

export async function updateUniqueCode(
  id: number,
  data: Partial<{
    code: string;
    isAssigned: boolean;
    assignedAt: Date | null;
    householdId: number | null;
    censusYearId: number;
    validationStart: number;
    validationEnd: number;
    validationHash: string;
  }>
): Promise<UniqueCode | null> {
  try {
    // If code is being updated, validation fields must be provided
    if (
      data.code &&
      !(data.validationStart && data.validationEnd && data.validationHash)
    ) {
      throw new Error(
        'Validation fields (validationStart, validationEnd, validationHash) are required when updating code'
      );
    }

    return await prisma.uniqueCode.update({
      where: { id },
      data,
    });
  } catch (error) {
    console.error('Error updating unique code:', error);
    return null;
  }
}

export async function deleteUniqueCode(id: number): Promise<boolean> {
  try {
    await prisma.uniqueCode.delete({
      where: { id },
    });
    return true;
  } catch (error) {
    console.error('Error deleting unique code:', error);
    return false;
  }
}

export async function assignUniqueCodeToHousehold(
  codeId: number,
  householdId: number
): Promise<void> {
  await prisma.uniqueCode.update({
    where: { id: codeId },
    data: {
      isAssigned: true,
      assignedAt: new Date(),
      householdId,
    },
  });
}

/**
 * Generates a variable validation range for secure unique code authentication
 *
 * SECURITY PROPERTIES (0-based indexing):
 * - Variable start position: 8-15 (ensures minimum validation length)
 * - Variable end position: 15-22 (ensures minimum validation length)
 * - Minimum validation length: 8 characters
 * - Maximum validation length: 14 characters
 * - Validation range covers only the random portion (positions 8-22, 0-based indexing)
 * - Each code gets a unique validation range for maximum security
 */
function generateValidationRange(): { start: number; end: number } {
  try {
    // Validation parameters
    const minLength = 8;
    const maxLength = 14;
    const randomPartStart = 8; // Start of random part: "cc-2025-" = 8 chars, so random starts at position 8 (0-based indexing)
    const randomPartEnd = 22; // End of random part: position 22 (inclusive)

    // Calculate valid start positions (8 to 15 to ensure minimum length fits)
    const maxStartPosition = randomPartEnd - minLength + 1; // 22 - 8 + 1 = 15
    const start = crypto.randomInt(randomPartStart, maxStartPosition + 1); // 8 to 15

    // Calculate valid end positions based on start
    const minEndPosition = start + minLength - 1; // Ensure minimum length
    const maxEndPosition = Math.min(randomPartEnd, start + maxLength - 1); // Ensure max length and bounds
    const end = crypto.randomInt(minEndPosition, maxEndPosition + 1);

    // Validate the generated range
    const validationLength = end - start + 1;
    if (validationLength < minLength || validationLength > maxLength) {
      throw new Error(
        `Invalid validation range: ${start}-${end} (length: ${validationLength})`
      );
    }

    if (start < randomPartStart || end > randomPartEnd) {
      throw new Error(
        `Validation range ${start}-${end} outside random part bounds ${randomPartStart}-${randomPartEnd}`
      );
    }

    return { start, end };
  } catch (error) {
    console.error('Error generating validation range:', error);
    // Fallback to safe default range (8 characters from position 8-15)
    return { start: 8, end: 15 };
  }
}

/**
 * Extracts validation characters from a unique code based on the specified range
 *
 * @param code The full unique code (e.g., "cc-2025-abcdefghijklmno")
 * @param start Start position (inclusive)
 * @param end End position (inclusive)
 * @returns Validation string for hashing
 */
function extractValidationChars(
  code: string,
  start: number,
  end: number
): string {
  try {
    if (code.length !== 23) {
      throw new Error(`Invalid code length: ${code.length}, expected 23`);
    }

    if (start < 0 || end >= code.length || start > end) {
      throw new Error(
        `Invalid validation range: ${start}-${end} for code length ${code.length}`
      );
    }

    return code.substring(start, end + 1);
  } catch (error) {
    console.error('Error extracting validation characters:', error);
    throw new Error('Failed to extract validation characters');
  }
}

/**
 * Generates a cryptographically secure unique code with variable validation range
 *
 * FORMAT: cc-yyyy-xxxxxxxxxxxxxxx (23 characters)
 * Where:
 * - cc: Fixed prefix for consistency
 * - yyyy: 4-digit census year (organisational benefit)
 * - xxxxxxxxxxxxxxx: 15 characters of cryptographically secure random data (77.5 bits entropy)
 *
 * SECURITY ENHANCEMENT:
 * - Variable validation ranges (8-14 characters from positions 8-22)
 * - 40-60% of visible characters are decoys
 * - Each code has unique validation logic
 * - SHA-256 hashing of validation portion
 * - Systematic enumeration attacks become computationally infeasible
 */
function generateUniqueCode(censusYear: number): {
  code: string;
  validationStart: number;
  validationEnd: number;
  validationHash: string;
} {
  try {
    // Generate 15 characters of cryptographically secure random data
    // Using crypto.randomInt() to avoid modulo bias and ensure uniform distribution
    let randomString = '';
    for (let i = 0; i < 15; i++) {
      // Generate random integer from 0 to 35 (inclusive) for base36
      const randomValue = crypto.randomInt(0, 36);
      randomString += randomValue.toString(36);
    }

    // Format: cc-yyyy-xxxxxxxxxxxxxxx (23 characters total)
    const fullCode = `cc-${censusYear.toString().padStart(4, '0')}-${randomString}`;

    // Validate the generated code meets our requirements
    if (fullCode.length !== 23) {
      throw new Error(
        `Generated code has invalid length: ${fullCode.length}, expected 23`
      );
    }

    // Validate format matches expected pattern
    if (!/^cc-\d{4}-[0-9a-z]{15}$/.test(fullCode)) {
      throw new Error(`Generated code has invalid format: ${fullCode}`);
    }

    // Generate variable validation range
    const { start, end } = generateValidationRange();

    // Extract validation characters
    const validationChars = extractValidationChars(fullCode, start, end);

    // Hash the validation characters using SHA-256
    const validationHash = crypto
      .createHash('sha256')
      .update(validationChars)
      .digest('hex');

    return {
      code: fullCode,
      validationStart: start,
      validationEnd: end,
      validationHash,
    };
  } catch (error) {
    // Log the error for debugging but don't expose internal details
    console.error('Error generating secure unique code:', error);
    throw new Error('Failed to generate secure unique code');
  }
}

/**
 * Generates the specified number of unique codes for the given census year ID
 * and inserts them into the database - Prisma version
 * @returns Array of IDs for the newly generated codes
 */
export async function generateUniqueCodes(
  count: number,
  censusYearId: number
): Promise<number[]> {
  // First, get the census year from the ID
  const censusYear = await prisma.censusYear.findUnique({
    where: { id: censusYearId },
    select: { year: true },
  });

  if (!censusYear) {
    throw new Error(`Census year with ID ${censusYearId} not found`);
  }

  // Generate all the codes with secure validation ranges
  const allSecureCodes = [];
  for (let i = 0; i < count; i++) {
    allSecureCodes.push(generateUniqueCode(censusYear.year));
  }

  try {
    // Use Prisma transaction to create all codes with validation data
    const results = await prisma.$transaction(
      allSecureCodes.map((secureCode) =>
        prisma.uniqueCode.create({
          data: {
            code: secureCode.code,
            censusYearId,
            isAssigned: false,
            validationStart: secureCode.validationStart,
            validationEnd: secureCode.validationEnd,
            validationHash: secureCode.validationHash,
          },
        })
      )
    );

    return results.map((result) => result.id);
  } catch (error) {
    // Check if it's a duplicate key error
    if (error instanceof Error && error.message.includes('Unique constraint')) {
      throw new Error('duplicateUniqueCodeGeneratedPl');
    }

    throw error;
  }
}

/**
 * Validates a unique code using secure variable validation range system
 *
 * SECURITY ENHANCEMENT:
 * - Uses variable validation ranges with decoy characters
 * - Only validates specific character positions (not the entire code)
 * - Each code has unique validation logic
 * - SHA-256 cryptographic verification
 * - Systematic enumeration attacks become computationally infeasible
 *
 * This function allows both:
 * - First-time users with unassigned codes (isAssigned = false)
 * - Returning users with previously assigned codes (isAssigned = true)
 *
 * It also allows codes from any census year, not just the active one.
 * The only restriction on login is whether the census system is open,
 * which is checked separately in the census-auth-options.ts file.
 */
export async function validateUniqueCode(
  code: string
): Promise<UniqueCode | null> {
  try {
    // First, look up the code in the database to get validation parameters
    const storedCode = await prisma.uniqueCode.findUnique({
      where: { code },
    });

    if (!storedCode) {
      return null;
    }

    // Extract validation characters using stored range
    const validationChars = extractValidationChars(
      code,
      storedCode.validationStart,
      storedCode.validationEnd
    );

    // Hash the extracted validation characters
    const inputHash = crypto
      .createHash('sha256')
      .update(validationChars)
      .digest('hex');

    // Compare with stored validation hash
    if (inputHash === storedCode.validationHash) {
      return storedCode;
    }

    // Validation failed - hash mismatch means invalid code
    return null;
  } catch (error) {
    // Log error for debugging but don't expose details
    console.error('Error validating unique code:', error);
    return null;
  }
}

/**
 * Get count of unique codes with filters - Prisma version
 * Optimized for performance with proper index usage
 */
export async function getUniqueCodesCount(
  searchTerm?: string,
  isAssigned?: boolean,
  censusYearId?: number
): Promise<number> {
  // Build the where clause based on filters
  const where: Record<string, unknown> = {};

  // Add search term filter
  if (searchTerm) {
    where.code = {
      contains: searchTerm,
      mode: 'insensitive',
    };
  }

  // Add assignment status filter
  if (isAssigned !== undefined) {
    where.isAssigned = isAssigned;
  }

  // Add census year filter
  if (censusYearId) {
    where.censusYearId = censusYearId;
  }

  // Execute the query
  return await prisma.uniqueCode.count({ where });
}

/**
 * Get available unique codes
 */
export async function getAvailableUniqueCodes(): Promise<UniqueCode[]> {
  return await prisma.uniqueCode.findMany({
    where: { isAssigned: false },
    orderBy: { code: 'asc' },
  });
}

/**
 * Get assigned unique codes
 */
export async function getAssignedUniqueCodes(): Promise<UniqueCode[]> {
  return await prisma.uniqueCode.findMany({
    where: { isAssigned: true },
    orderBy: { assignedAt: 'desc' },
  });
}

/**
 * Unassign a unique code
 */
export async function unassignUniqueCode(codeId: number): Promise<void> {
  await prisma.uniqueCode.update({
    where: { id: codeId },
    data: {
      isAssigned: false,
      assignedAt: null,
      householdId: null,
    },
  });
}

/**
 * Get unique code by household ID
 */
export async function getUniqueCodeByHouseholdId(
  householdId: number
): Promise<UniqueCode | null> {
  return await prisma.uniqueCode.findFirst({
    where: {
      householdId,
      isAssigned: true,
    },
  });
}

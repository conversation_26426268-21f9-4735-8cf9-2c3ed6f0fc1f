import { type NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { getTranslations } from 'next-intl/server';
import { z } from 'zod/v4';
import { censusAuthOptions } from '@/lib/census-auth/census-auth-options';
import { prisma } from '@/lib/db/prisma';
import { getLocaleFromCookies } from '@/lib/utils/server-messages';

/**
 * DELETE /api/census/delete-account
 *
 * Deletes a census participant's account and all associated data
 * - Requires census participant authentication
 * - Deletes the household (if it exists), which cascades to delete household_members
 * - Deletes the unique code
 * - Logs the action in the audit_logs table
 *
 * @param request The incoming request
 * @returns A JSON response indicating success or failure
 */
// Validation schema for the request body - will be created dynamically with translations
const createDeleteAccountSchema = async (locale: 'en' | 'zh-CN') => {
  const t = await getTranslations({ locale, namespace: 'admin' });
  const tValidation = await getTranslations({
    locale,
    namespace: 'validation',
  });
  return z.object({
    confirmationPhrase: z
      .string()
      .min(1, tValidation('confirmationPhraseRequired'))
      .refine((value) => value === 'DELETE NOW', {
        message: t('invalidConfirmationPhrase'),
      }),
  });
};

export async function DELETE(request: NextRequest) {
  // Get locale from centralized utility at the beginning
  const locale = await getLocaleFromCookies();
  const tAdmin = await getTranslations({ locale, namespace: 'admin' });

  try {
    // Check if user is authenticated
    const session = await getServerSession(censusAuthOptions);

    if (!session) {
      return NextResponse.json(
        {
          success: false,
          error: 'Unauthorized',
        },
        { status: 401 }
      );
    }

    // Check if user has the household role
    if (session.user.role !== 'household') {
      return NextResponse.json(
        {
          success: false,
          error: 'Forbidden',
        },
        { status: 403 }
      );
    }

    // Get the unique code ID and household ID from the session
    const uniqueCodeId = Number.parseInt(session.user.id, 10);
    const householdId = session.user.householdId
      ? Number.parseInt(session.user.householdId, 10)
      : null;

    // Parse and validate request body
    let requestBody;
    try {
      requestBody = await request.json();
      const deleteAccountSchema = await createDeleteAccountSchema(locale);
      deleteAccountSchema.parse(requestBody);
    } catch (error) {
      // Log the validation failure
      await prisma.auditLog.create({
        data: {
          userType: 'household',
          userId: uniqueCodeId,
          action: 'delete-account-validation-failed',
          entityType: 'unique_codes',
          recordId: uniqueCodeId,
          ipAddress:
            request.headers.get('x-forwarded-for') ||
            request.headers.get('x-real-ip') ||
            'unknown',
          userAgent: request.headers.get('user-agent') || 'unknown',
          newValues: JSON.stringify({
            error: error instanceof Error ? error.message : 'Unknown error',
          }),
        },
      });

      return NextResponse.json(
        {
          success: false,
          error: tAdmin('invalidConfirmationPhrase'),
        },
        { status: 400 }
      );
    }

    // Get client IP address for audit logging
    const ipAddress =
      request.headers.get('x-forwarded-for') ||
      request.headers.get('x-real-ip') ||
      'unknown';

    // Get user agent for audit logging
    const userAgent = request.headers.get('user-agent') || 'unknown';

    // Prepare for transaction operations

    // Execute all operations in a single transaction
    await prisma.$transaction(async (tx) => {
      if (householdId) {
        // CRITICAL FIX: Collect member IDs BEFORE deleting relationships
        // This fixes the bug where member records were not being deleted
        const memberIds = await tx.householdMember.findMany({
          where: { householdId },
          select: { memberId: true },
        });

        // Delete all sacraments for this household's members
        await tx.sacrament.deleteMany({
          where: {
            member: {
              householdMembers: {
                some: {
                  householdId,
                  isCurrent: true,
                },
              },
            },
          },
        });

        // Delete household member relationships
        await tx.householdMember.deleteMany({
          where: { householdId },
        });

        // Delete all members in this household (now using pre-collected IDs)
        if (memberIds.length > 0) {
          await tx.member.deleteMany({
            where: {
              id: {
                in: memberIds.map((hm) => hm.memberId),
              },
            },
          });
        }

        // Delete the household
        await tx.household.delete({
          where: { id: householdId },
        });
      }

      // Update unique code to unassigned
      await tx.uniqueCode.update({
        where: { id: uniqueCodeId },
        data: {
          isAssigned: false,
          assignedAt: null,
          householdId: null,
          updatedAt: new Date(),
        },
      });

      // Log the deletion
      await tx.auditLog.create({
        data: {
          userType: 'household',
          userId: uniqueCodeId,
          action: 'delete-account',
          entityType: 'unique_codes',
          recordId: uniqueCodeId,
          ipAddress,
          userAgent,
        },
      });
    });

    // Set success toast cookie for homepage display
    const tNotifications = await getTranslations({
      locale,
      namespace: 'notifications',
    });
    const successMessage = tNotifications('accountDeleted');

    const response = NextResponse.json({
      success: true,
      message: successMessage,
    });

    // Set census_toast cookie so the message appears on homepage after redirect
    const toastData = {
      type: 'success',
      message: successMessage,
    };

    response.cookies.set({
      name: 'census_toast',
      value: JSON.stringify(toastData),
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      maxAge: 10, // 10 seconds - short expiry prevents persistence issues
      path: '/',
      sameSite: 'lax',
    });

    return response;
  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        error: tAdmin('accountDeleted'),
        details:
          error instanceof Error ? error.message : tAdmin('unknownError'),
      },
      { status: 500 }
    );
  }
}

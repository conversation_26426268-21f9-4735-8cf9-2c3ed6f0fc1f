/**
 * Household Distribution Queries Module
 *
 * Contains all distribution query functions related to household data:
 * - Relationship distributions
 * - Suburb distributions
 */

import { prisma } from '@/lib/db/prisma';
import type { DistributionTableData } from '@/types/analytics';

// Relationship distribution table generator - PostgreSQL optimized
export async function getRelationshipDistributionTable(): Promise<DistributionTableData> {
  // 2025 Best Practice: Single PostgreSQL query for aggregation and total
  const relationshipStats = await prisma.$queryRaw<
    Array<{
      relationship: string;
      count: bigint;
      total_count: bigint;
    }>
  >`
    SELECT
      relationship,
      COUNT(*) as count,
      SUM(COUNT(*)) OVER() as total_count
    FROM household_members
    WHERE is_current = true
    GROUP BY relationship
    ORDER BY count DESC
  `;

  if (relationshipStats.length === 0) {
    return {
      data: [],
      queryType: 'relationship_distribution',
      title: 'Household Relationship Distribution',
      totalRecords: 0,
    };
  }

  const totalCount = Number(relationshipStats[0].total_count);

  return {
    data: relationshipStats.map((stat) => ({
      relationship: stat.relationship,
      count: Number(stat.count),
      percentage:
        totalCount > 0
          ? `${((Number(stat.count) / totalCount) * 100).toFixed(1)}%`
          : '0.0%',
    })),
    queryType: 'relationship_distribution',
    title: 'Household Relationship Distribution',
    totalRecords: relationshipStats.length,
  };
}

// Suburb distribution table generator - PostgreSQL optimized
export async function getSuburbDistributionTable(): Promise<DistributionTableData> {
  // 2025 Best Practice: Single PostgreSQL query for aggregation and total
  const suburbStats = await prisma.$queryRaw<
    Array<{
      suburb: string;
      households: bigint;
      total_count: bigint;
    }>
  >`
    SELECT
      suburb,
      COUNT(*) as households,
      SUM(COUNT(*)) OVER() as total_count
    FROM households
    GROUP BY suburb
    ORDER BY households DESC
  `;

  if (suburbStats.length === 0) {
    return {
      data: [],
      queryType: 'suburb_distribution',
      title: 'Household Suburb Distribution',
      totalRecords: 0,
    };
  }

  const totalCount = Number(suburbStats[0].total_count);

  return {
    data: suburbStats.map((stat) => ({
      suburb: stat.suburb,
      households: Number(stat.households),
      percentage:
        totalCount > 0
          ? `${((Number(stat.households) / totalCount) * 100).toFixed(1)}%`
          : '0.0%',
    })),
    queryType: 'suburb_distribution',
    title: 'Household Suburb Distribution',
    totalRecords: suburbStats.length,
  };
}

import 'next-auth';
import { DefaultSession } from 'next-auth';

declare module 'next-auth' {
  interface User {
    id: string;
    username?: string;
    role: string;
    name: string;
    code?: string;
    householdId?: string | null;
    censusYearId?: string;
  }

  interface Session {
    user: {
      id: string;
      username?: string;
      role: string;
      name: string;
      code?: string;
      householdId?: string | null;
      censusYearId?: string;
    } & DefaultSession['user'];
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    id: string;
    username?: string;
    role: string;
    name: string;
    code?: string;
    householdId?: string | null;
    censusYearId?: string;
  }
}

# WSCCC Census System Security Guide

## Overview

The WSCCC Census System implements enterprise-grade security measures to protect sensitive community data. This guide outlines the comprehensive security architecture, authentication systems, and protection mechanisms.

## Authentication Systems

### Dual Authentication Architecture

The system maintains two completely independent authentication systems to ensure security isolation:

#### Admin Authentication
- **Endpoint**: `/api/auth/[...nextauth]`
- **Method**: Username/password with optional 2FA
- **Session Management**: JWT with HTTP-only cookies
- **<PERSON>ie Names**: `admin-session-token`, `admin-callback-url`, `admin-csrf-token`
- **Secret**: `NEXTAUTH_SECRET_ADMIN`
- **Session Duration**: 8 hours
- **Password Hashing**: Argon2id (OWASP recommendation)
- **Middleware Protection**: Enterprise-grade API and page authentication
- **Session Isolation**: Prevents cross-auth contamination

#### Census Authentication
- **Endpoint**: `/api/census/auth/[...nextauth]`
- **Method**: Unique code validation with progressive rate limiting
- **Session Management**: JWT with HTTP-only cookies
- **<PERSON>ie Names**: `census-session-token`, `census-callback-url`, `census-csrf-token`
- **Secret**: `NEXTAUTH_SECRET_CENSUS`
- **Session Duration**: 8 hours
- **Code Generation**: Cryptographically secure random codes
- **Rate Limiting**: IP-based progressive timeout system
- **Middleware Protection**: Account deletion checks and API protection
- **Session Isolation**: Prevents admin session interference

### Hybrid Defense-in-Depth Authentication Security

#### Multi-Layer Protection System
The system implements a sophisticated defense-in-depth approach to prevent authentication cross-contamination:

##### Layer 1: SessionIsolationProvider (Primary Defense)
- **Purpose**: Prevents NextAuth.js session requests when wrong cookies present
- **Implementation**: Cookie validation before session context creation
- **Behavior**: Provides null session when expected cookies missing
- **Performance**: Zero network requests for mismatched contexts
- **Coverage**: All authentication contexts (admin, census, public)

##### Layer 2: Middleware Guard (Secondary Defense)
- **Purpose**: Edge-level protection against cross-auth API calls
- **Implementation**: Request interception with cookie validation
- **Response**: Structured JSON errors for blocked requests
- **Logging**: Development-only logging for debugging
- **Endpoints**: Surgical blocking of NextAuth.js session endpoints only

#### Authentication Endpoint Classification
```typescript
// Public endpoints (always accessible)
PUBLIC_CENSUS_ENDPOINTS = [
  '/api/census/auth/rate-limit-status',
  '/api/census/status'
];

// Protected NextAuth.js endpoints (cross-auth sensitive)
NEXTAUTH_SESSION_ENDPOINTS = [
  '/api/census/auth/session',
  '/api/census/auth/signin',
  '/api/census/auth/signout'
];

// Admin authentication endpoints
ADMIN_AUTH_ENDPOINTS = [
  '/api/auth/session',
  '/api/auth/signin',
  '/api/auth/signout'
];
```

#### Cross-Contamination Prevention
- **Cookie Namespace Separation**: `admin-*` vs `census-*` prefixes
- **Endpoint Isolation**: Middleware blocks wrong cookies from wrong endpoints
- **Session Context Isolation**: SessionIsolationProvider prevents wrong session requests
- **Error Handling**: Graceful degradation without user experience disruption

### Account Deletion Security

The system implements secure account deletion with proper detection and user feedback:

#### Admin-Initiated Deletion
- **Detection**: Professional cookie-based logic in `requireCensusAuth()`
- **Flow**: Database flag → Session invalidation → Toast redirect → Homepage message
- **Message**: Warning toast with administrator contact information
- **Security**: Prevents false positives from expired tokens

#### Self-Initiated Deletion
- **Validation**: Requires exact confirmation phrase "DELETE NOW"
- **Flow**: API validation → Data deletion → Toast cookie → Homepage message
- **Message**: Success toast confirming account deletion
- **Cleanup**: Comprehensive localStorage and database cleanup

#### Security Features
- **Dual Authentication Independence**: Separate `auth_toast` vs `census_toast` cookies
- **Professional Detection Logic**: Avoids heuristic-based assumptions
- **Audit Logging**: All deletion operations logged with IP and user agent
- **Data Integrity**: Proper cascade deletion of related data
- **Session Invalidation**: Immediate session termination on deletion

### Password Security

#### Argon2id Implementation
```typescript
// Password hashing configuration
export async function hashPassword(password: string): Promise<string> {
  return await hash(password, {
    memoryCost: 65536, // 64 MB
    timeCost: 3,       // 3 iterations
    outputLen: 32,     // 32 bytes
    parallelism: 1,    // 1 thread
  });
}
```

#### Legacy Support
- **bcrypt**: Supported for existing passwords
- **Migration**: Automatic upgrade to Argon2id on login
- **Validation**: Constant-time comparison to prevent timing attacks

### Two-Factor Authentication (2FA)

#### TOTP Implementation
- **Library**: otplib for TOTP generation/validation
- **QR Code Setup**: Professional styling with caching
- **Backup Codes**: 6 alphanumeric characters without hyphens
- **Validation**: Both TOTP and backup code support
- **Audit Logging**: Complete 2FA usage tracking

#### Backup Code System
- **Format**: 6 alphanumeric characters (e.g., "A1B2C3")
- **Generation**: Cryptographically secure random generation
- **Storage**: Hashed in database
- **Usage**: One-time use with automatic invalidation
- **Recovery**: Admin-managed regeneration

## Hybrid Rate Limiting System

### Elegant Client-Server Architecture

The system implements a sophisticated hybrid approach combining smooth user experience with bulletproof security:

> **Core Philosophy**: "Client provides smooth UX, Server ensures security"

#### Configuration
```typescript
export const RATE_LIMIT_CONFIG = {
  MAX_ATTEMPTS: 5,                    // Failed attempts before lockout
  BASE_LOCKOUT_MINUTES: 1,           // Initial lockout duration (reduced)
  ESCALATION_INCREMENT_MINUTES: 5,    // Progressive escalation
  MAX_LOCKOUT_HOURS: 1,              // Maximum lockout (60 minutes)
} as const;
```

#### Escalation Pattern
- **Attempt 1-5**: ✅ Allowed
- **Attempt 6**: 🔒 1 minute lockout
- **Attempt 7**: 🔒 5 minute lockout
- **Attempt 8**: 🔒 15 minute lockout
- **Attempt 9**: 🔒 30 minute lockout
- **Attempt 10+**: 🔒 60 minute lockout (maximum)

#### Hybrid Architecture
- **Database Authority**: PostgreSQL stores authoritative rate limit state
- **Strategic Server Checks**: Only when needed (auth attempts, countdown expiry)
- **Client-Side UX**: Smooth real-time countdown without polling
- **Multi-Tab Sync**: localStorage synchronization across browser tabs
- **Graceful Degradation**: Works even when client features fail

#### Security Guarantees
- **Server Validates Every Auth Attempt**: Client cannot bypass security
- **Database Persistence**: Survives server restarts and deployments
- **No Client Authority**: Client never makes security decisions
- **Progressive Escalation**: Increasing timeout periods deter attacks
- **Automatic Cleanup**: Records older than 30 days removed

#### How It Works

```
┌─────────────────┐    Strategic     ┌──────────────────┐
│   Client Timer  │ ────Validation──▶│  Server (Auth)   │
│  (Smooth UX)    │                  │  (Authoritative) │
└─────────────────┘                  └──────────────────┘
         │                                     │
         ▼                                     ▼
   ┌─────────────┐                    ┌──────────────┐
   │ localStorage │                    │   Database   │
   │ (Multi-tab)  │                    │ (Persistent) │
   └─────────────┘                    └──────────────┘
```

**The Flow:**
1. **Authentication Attempt**: Server checks database before allowing auth
2. **Failed Attempt**: Server records failure and calculates lockout
3. **Client Countdown**: Client displays smooth countdown using server lockout time
4. **Strategic Validation**: Client asks server when countdown expires
5. **Server Authority**: Server decides when to unlock, client follows

**Key Benefits:**
- **95% Network Reduction**: From 60+ requests/minute to 1-3 strategic checks
- **Smooth UX**: Real-time countdown without server polling
- **Bulletproof Security**: Server validates every authentication attempt
- **Multi-Tab Sync**: Consistent state across browser tabs

#### Strategic Server Checks

We **don't** constantly poll the server. Instead, we check strategically:

**When We Check Server:**
1. **🔐 Before every authentication attempt** (security critical)
2. **⏰ When client countdown expires** (unlock validation)
3. **📱 On page load** (sync state)
4. **🔄 On tab focus** (multi-tab sync)

**When We DON'T Check:**
- ❌ Every second during countdown
- ❌ On mouse movement or clicks
- ❌ On form input changes
- ❌ On timer intervals

#### Multi-Tab Synchronization

```typescript
// Tab A: User gets locked out
localStorage.setItem('rate-limit-state', JSON.stringify({
  isLocked: true,
  lockoutExpiry: 1640995200000,
  remainingTime: 300000
}));

// Tab B: Automatically syncs via storage event
window.addEventListener('storage', (e) => {
  if (e.key === 'rate-limit-event') {
    const newState = loadStateFromStorage();
    setState(newState);
    startCountdown(newState.lockoutExpiry);
  }
});
```

#### Security Guarantees

**What's Impossible to Bypass:**
- **Client can't unlock early** - Server validates every auth attempt
- **Client can't reset attempts** - Database is authoritative
- **Client can't fake countdown** - Server checks lockout expiry
- **Multi-device attacks** - Same IP gets same rate limit
- **Browser manipulation** - Server validates regardless of client state

**What Happens if Client Fails:**
- **localStorage disabled** → Single tab still works
- **JavaScript disabled** → Server still blocks attempts
- **Timer fails** → Server validates on auth attempt
- **Network issues** → Fail-safe blocks authentication
- **Clock drift** → Server time is authoritative

#### Performance Benefits

**Network Efficiency:**
- **95% reduction** in server requests (from 60/min to 1-3 strategic checks)
- **No polling** - Only check when needed
- **Cached responses** - Client state persists across page loads

**User Experience:**
- **Instant feedback** - Smooth countdown without delays
- **Multi-tab sync** - Consistent state across browser tabs
- **Graceful degradation** - Works even when features fail
- **Professional UI** - Clean, responsive rate limit display

#### The Key Insight

> **Perfect client-side accuracy doesn't matter because server validates every authentication attempt anyway!**

**Example Scenario:**
```
1. User gets locked for 5 minutes
2. Client countdown shows 2:30 remaining
3. User tries to authenticate anyway
4. Server checks database: "Still 2:45 remaining"
5. Server blocks authentication
6. Client gets updated state from server
7. Client corrects countdown to 2:45
```

**Result**: Security is never compromised, UX stays smooth!

### Database Schema
```sql
CREATE TABLE IF NOT EXISTS auth_rate_limits (
    id INT PRIMARY KEY AUTO_INCREMENT,
    session_token VARCHAR(64) NOT NULL UNIQUE COMMENT 'SHA256 hash of session token',
    failed_attempts INT DEFAULT 0,
    lockout_until DATETIME NULL,
    escalation_level INT DEFAULT 0,
    last_failed_attempt DATETIME NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_lockout_until (lockout_until),
    INDEX idx_cleanup (created_at),
    INDEX idx_active_lockouts (session_token, lockout_until)
);
```

#### Implementation Files

**Core Files:**
- `src/hooks/use-census-rate-limit.ts` - Client-side hybrid logic
- `src/lib/auth/rate-limiting-service.ts` - Server-side authority
- `app/api/census/auth/rate-limit-status/route.ts` - Status API
- `src/components/home/<USER>

**Key Functions:**
- `useCensusRateLimit()` - React hook for client state management
- `getEnhancedRateLimitStatus()` - Server status check with lockout expiry
- `recordFailedAttempt()` - Server failure recording and lockout calculation
- `checkStatus()` - Strategic server validation (returns fresh state)

#### Why This Approach is Superior

**vs. Pure Server-Side:**
- ✅ **Better UX**: Real-time countdown vs. page refreshes
- ✅ **Less Load**: 95% fewer server requests
- ✅ **Faster Response**: Instant feedback vs. network delays

**vs. Pure Client-Side:**
- ✅ **Bulletproof Security**: Server validates everything
- ✅ **Persistent**: Survives browser restarts
- ✅ **Multi-Device**: Works across different browsers/devices

**vs. Constant Polling:**
- ✅ **Efficient**: Strategic checks vs. every second
- ✅ **Scalable**: Handles thousands of users
- ✅ **Battery Friendly**: No constant network requests

## Input Validation & Sanitization

### Zod Schema Validation

All user inputs are validated using comprehensive Zod v4 schemas:

#### Authentication Schemas
```typescript
export const loginSchema = z.object({
  username: z.string().min(1, { error: 'Username is required' }),
  password: z.string().min(1, { error: 'Password is required' }),
});

export const totpSchema = z.object({
  token: z.string()
    .min(6, { error: 'Code must be at least 6 characters' })
    .max(6, { error: 'Code must be at most 6 characters' })
    .refine(val => /^[A-Z0-9]{6}$/i.test(val), {
      error: "Code must be 6 characters (letters or numbers)"
    }),
});
```

#### Form Validation
- **Client-side**: Real-time validation with React Hook Form
- **Server-side**: API endpoint validation before processing
- **Database**: Prisma schema constraints
- **Sanitization**: Input cleaning and normalization

### SQL Injection Prevention

#### Parameterized Queries
All database operations use parameterized queries:

```typescript
// Safe database query example
const user = await prisma.admin.findUnique({
  where: { username: credentials.username }
});
```

#### Query Validation
- **Prisma ORM**: Type-safe database operations
- **Input Validation**: Zod schemas before database calls
- **Parameter Binding**: No string concatenation in queries
- **Escape Handling**: Automatic parameter escaping

## Security Headers

### Content Security Policy (CSP)
```typescript
{
  key: 'Content-Security-Policy',
  value: "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https://generativelanguage.googleapis.com; frame-ancestors 'none'; object-src 'none'; base-uri 'self';"
}
```

### Additional Security Headers
- **X-Frame-Options**: `DENY` (prevents clickjacking)
- **X-Content-Type-Options**: `nosniff` (prevents MIME sniffing)
- **X-XSS-Protection**: `1; mode=block` (XSS protection)
- **Referrer-Policy**: `strict-origin-when-cross-origin`
- **Permissions-Policy**: Disables unnecessary browser features

## Session Management

### HTTP-Only Cookies
- **Security**: Prevents XSS access to session tokens
- **SameSite**: `lax` for CSRF protection
- **Secure**: HTTPS-only in production
- **Path**: Root path for application-wide access

### Session Validation
- **JWT Verification**: Cryptographic signature validation
- **Expiration**: Automatic session timeout after 8 hours
- **Refresh**: Token refresh on activity
- **Invalidation**: Secure logout with token cleanup

## Audit Logging

### Comprehensive Tracking
The system logs all security-relevant events:

#### Admin Actions
- Login/logout events
- Failed authentication attempts
- 2FA setup and usage
- Data export operations
- System configuration changes
- User management actions

#### Census Actions
- Code validation attempts
- Form submissions
- Account deletions
- Rate limiting triggers

#### Log Structure
```typescript
interface AuditLog {
  id: number;
  userId?: number;
  action: string;
  details?: string;
  ipAddress?: string;
  userAgent?: string;
  timestamp: Date;
}
```

## Data Protection

### Encryption at Rest
- **Database**: PostgreSQL with encryption support
- **Passwords**: Argon2id hashing
- **Session Tokens**: JWT with cryptographic signatures
- **Backup Codes**: Hashed storage

### Encryption in Transit
- **HTTPS**: TLS 1.3 for all communications
- **API Calls**: Secure HTTPS endpoints
- **Database Connections**: Encrypted connections
- **External APIs**: HTTPS-only integrations

## Unique Code Security

### Advanced Variable Validation Range System

The system implements a revolutionary security enhancement that makes systematic enumeration attacks computationally infeasible:

#### Code Generation
```typescript
// Cryptographically secure code generation with variable validation
function generateUniqueCode(censusYear: number): {
  code: string;
  validationStart: number;
  validationEnd: number;
  validationHash: string;
} {
  // Generate 15 characters of cryptographically secure random data
  let randomString = '';
  for (let i = 0; i < 15; i++) {
    const randomValue = crypto.randomInt(0, 36);
    randomString += randomValue.toString(36);
  }

  const fullCode = `cc-${censusYear.toString().padStart(4, '0')}-${randomString}`;

  // Generate variable validation range (8-14 characters from positions 8-22, 0-based indexing)
  const { start, end } = generateValidationRange();
  const validationChars = extractValidationChars(fullCode, start, end);
  const validationHash = crypto.createHash('sha256').update(validationChars).digest('hex');

  return { code: fullCode, validationStart: start, validationEnd: end, validationHash };
}
```

#### Revolutionary Security Features

**1. Variable Validation Ranges**
- **Range**: 8-14 characters validated per code
- **Positions**: Selected from positions 8-22 (0-based indexing, random portion only)
- **Uniqueness**: Each code has different validation pattern
- **Decoy Characters**: 40-60% of visible characters are meaningless

**2. Cryptographic Validation**
- **Algorithm**: SHA-256 hashing of validation portion
- **Storage**: Only hash stored in database (not validation pattern)
- **Verification**: Cryptographic comparison prevents tampering
- **Security**: Validation logic hidden from attackers

**3. Attack Resistance**
- **Systematic Enumeration**: Computationally infeasible
- **Pattern Discovery**: Prevented by variable ranges
- **Lucky Guessing**: Even correct full code fails without validation pattern
- **Reverse Engineering**: SHA-256 prevents validation logic discovery

#### Code Format & Security Properties
- **Format**: `cc-yyyy-xxxxxxxxxxxxxxx` (23 characters total)
- **Prefix**: `cc-` (2 characters) - Fixed identifier
- **Year**: `yyyy-` (5 characters) - Census year for organization
- **Random Data**: `xxxxxxxxxxxxxxx` (15 characters) - 77.5 bits entropy
- **Validation**: Variable 8-14 character subset with SHA-256 hash
- **Database Constraints**: Unique code constraint prevents duplicates

#### Position Indexing System (0-Based)
```
Code: cc-2025-abcdefghijklmno
      0123456789012345678901234  ← Position index (0-based)
      │       │              │
      0       8              22
      c       a              o

Fixed Part:   Positions 0-7   = "cc-2025-" (8 characters)
Random Part:  Positions 8-22  = "abcdefghijklmno" (15 characters)
Validation:   Variable subset from positions 8-22 (8-14 characters)
```

**Important**: All position references in this system use **0-based indexing**, which is standard in programming. Position 8 refers to the 9th character in natural counting.

#### Example Security Scenario
```
Generated Code: cc-2025-abcdefghijklmno
                0123456789012345678901234  (0-based position index)
Validation Range: Positions 12-19 (8 characters: "defghijk")
Stored Hash: SHA-256("defghijk") = a1b2c3d4e5f6...
Decoy Characters: "cc-2025-abc" + "lmno" (9 characters ignored)

Position Breakdown (0-based indexing):
- Positions 0-7: "cc-2025-" (fixed prefix, 8 characters)
- Positions 8-22: "abcdefghijklmno" (random data, 15 characters)
- Validation subset: Positions 12-19 = "defghijk" (8 characters)

Attacker Challenge:
- Must guess correct 23-character code: 1 in 36^15 chance
- Must guess correct validation positions: 1 in thousands of combinations
- Must know validation algorithm: Hidden server-side logic
- Result: Attack becomes computationally infeasible
```

### QR Code Security
- **Generation**: Server-side with secure validation data
- **Distribution**: Print cards with cut marks for security
- **Validation**: Cryptographic hash verification required
- **Tracking**: Assignment and usage monitoring with audit logs
- **Protection**: Variable validation prevents code prediction

## API Security

### Authentication Middleware
```typescript
// Admin route protection
export async function requireAdmin() {
  const session = await getServerSession(authOptions);
  
  if (!session || session.user.role !== 'admin') {
    redirect('/api/auth/toast-redirect?reason=unauthorized');
  }
  
  return session;
}
```

### Rate Limiting
- **API Endpoints**: Request rate limiting
- **Authentication**: Progressive lockout system
- **Resource Protection**: Admin-only endpoint restrictions
- **Error Handling**: Secure failure modes

## Compliance & Best Practises

### Security Standards
- **OWASP Guidelines**: Authentication and session management
- **NIST SP 800-63B**: Digital identity guidelines
- **Enterprise Security**: Industry best practises
- **Privacy-First Design**: Data minimization principles

### Regular Security Maintenance
- **Dependency Updates**: Regular security patches
- **Vulnerability Scanning**: Automated security checks
- **Audit Reviews**: Periodic security assessments
- **Incident Response**: Security event procedures

This comprehensive security implementation ensures the WSCCC Census System meets enterprise-grade security requirements while maintaining usability for both administrators and census participants.

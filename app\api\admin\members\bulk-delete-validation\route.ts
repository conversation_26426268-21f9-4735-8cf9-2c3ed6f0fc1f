import { type NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { getTranslations } from 'next-intl/server';
import { z } from 'zod/v4';
import { authOptions } from '@/lib/auth/auth-options';
import { getMemberDeletionInfo } from '@/lib/db/members';
import { getErrorMessage } from '@/lib/utils/error-handling';
import { getLocaleFromCookies } from '@/lib/utils/server-messages';
import { createBulkMemberValidationSchema } from '@/lib/validation/members';

/**
 * POST /api/admin/members/bulk-delete-validation
 *
 * Validates multiple members for deletion without actually deleting them
 * Only accessible to admin users
 */
export async function POST(request: NextRequest) {
  // Get locale from centralized utility at the beginning
  const locale = await getLocaleFromCookies();
  const tAdmin = await getTranslations({ locale, namespace: 'admin' });
  const tErrors = await getTranslations({ locale, namespace: 'errors' });

  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Require admin authentication
    if (!session || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: tErrors('unauthorized') },
        { status: 401 }
      );
    }

    // Parse and validate request body with translations
    const body = await request.json();
    const bulkValidationSchema = await createBulkMemberValidationSchema(locale);
    const validatedData = bulkValidationSchema.parse(body);
    const { memberIds } = validatedData;

    // Get translations for warning messages
    const tDialogs = await getTranslations({ locale, namespace: 'dialogs' });

    // Validate each member
    const validationResults = [];
    const deletableMembers = [];
    const undeletableMembers = [];
    const householdDeletions = [];

    for (const memberId of memberIds) {
      try {
        const deletionInfo = await getMemberDeletionInfo(memberId);

        validationResults.push({
          memberId,
          canDelete: deletionInfo.canDelete,
          deleteType: deletionInfo.deleteType,
          isHouseholdHead: deletionInfo.isHouseholdHead,
          memberCount: deletionInfo.memberCount,
          warningMessage: deletionInfo.warningMessage,
        });

        if (deletionInfo.canDelete) {
          deletableMembers.push(memberId);

          if (deletionInfo.deleteType === 'head_last_member') {
            householdDeletions.push({
              memberId,
              householdId: deletionInfo.householdId,
            });
          }
        } else {
          undeletableMembers.push({
            memberId,
            reason:
              deletionInfo.warningMessage || tDialogs('cannotDeleteMember'),
            deleteType: deletionInfo.deleteType,
          });
        }
      } catch (_error) {
        undeletableMembers.push({
          memberId,
          reason: tErrors('memberNotFound'),
          deleteType: 'unknown',
        });
      }
    }

    // Calculate summary statistics
    const summary = {
      total: memberIds.length,
      deletable: deletableMembers.length,
      undeletable: undeletableMembers.length,
      householdsToDelete: householdDeletions.length,
      regularMembers: validationResults.filter(
        (r) => r.deleteType === 'regular'
      ).length,
      householdHeads: validationResults.filter((r) => r.isHouseholdHead).length,
    };

    // Determine if bulk delete can proceed
    const canProceed = undeletableMembers.length === 0;

    return NextResponse.json({
      canProceed,
      summary,
      validationResults,
      deletableMembers,
      undeletableMembers,
      householdDeletions,
      warnings:
        householdDeletions.length > 0
          ? [
              tDialogs('householdsWillBeCompletelyDeleted', {
                count: householdDeletions.length.toString(),
                households:
                  householdDeletions.length === 1
                    ? tAdmin('household')
                    : tAdmin('households'),
              }),
            ]
          : [],
    });
  } catch (error) {
    // Environment-aware logging - only in development
    if (process.env.NODE_ENV === 'development') {
    }

    // Handle validation errors (reuse locale variables from function scope)
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: tAdmin('invalidRequestData'),
          details: error.issues,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        error: tErrors('failedToValidateBulkDelete'),
        details: getErrorMessage(error),
      },
      { status: 500 }
    );
  }
}

/**
 * Temporal Intent Handlers Module
 *
 * Contains temporal analysis and general intent handler functions that process
 * time-based queries and general overview requests.
 *
 * These functions handle:
 * - Temporal analysis intent processing (year-over-year trends)
 * - General intent processing (overview queries)
 */

import { logSecureError } from '@/lib/analytics/chatbot/security';
import { prisma } from '@/lib/db/prisma';
import type { QueryIntent } from '@/types/analytics';

// Handle temporal analysis intent - Professional year-over-year analysis
export async function handleTemporalAnalysisIntent(
  intent: QueryIntent,
  results: string[]
): Promise<void> {
  try {
    const censusYears = await prisma.censusYear.findMany({
      orderBy: { year: 'desc' },
    });

    if (censusYears.length === 0) {
      results.push('No census years found in the database.');
      return;
    }

    const activeCensus = censusYears.find((cy) => cy.isActive);
    if (activeCensus) {
      results.push(`Active census year: ${activeCensus.year}`);
    }

    results.push(`Total census years: ${censusYears.length}`);

    // Enhanced temporal analysis for distribution and comparison queries
    if (
      intent.analysisType === 'distribution' ||
      intent.analysisType === 'overview'
    ) {
      // Year-over-year participation analysis
      const participationStats = await Promise.all(
        censusYears.slice(0, 5).map(async (year) => {
          const [memberCount, householdCount, codeCount] = await Promise.all([
            prisma.member.count({
              where: {
                householdMembers: {
                  some: { censusYearId: year.id, isCurrent: true },
                },
              },
            }),
            prisma.household.count({
              where: {
                OR: [
                  { firstCensusYearId: year.id },
                  { lastCensusYearId: year.id },
                ],
              },
            }),
            prisma.uniqueCode.count({
              where: { censusYearId: year.id, isAssigned: true },
            }),
          ]);

          return {
            year: year.year,
            isActive: year.isActive,
            members: memberCount,
            households: householdCount,
            participation: codeCount,
          };
        })
      );

      // Format year-over-year summary
      const yearSummary = participationStats
        .map((stat) => {
          const activeFlag = stat.isActive ? ' (ACTIVE)' : '';
          return `${stat.year}${activeFlag}: ${stat.members} members, ${stat.households} households, ${stat.participation} participating`;
        })
        .join('\n');

      results.push(`Year-over-year analysis:\n${yearSummary}`);

      // Calculate growth trends if we have multiple years
      if (participationStats.length >= 2) {
        const latest = participationStats[0];
        const previous = participationStats[1];

        const memberGrowth = latest.members - previous.members;
        const householdGrowth = latest.households - previous.households;
        const participationGrowth =
          latest.participation - previous.participation;

        const growthSummary = [
          `Member growth: ${memberGrowth >= 0 ? '+' : ''}${memberGrowth}`,
          `Household growth: ${householdGrowth >= 0 ? '+' : ''}${householdGrowth}`,
          `Participation growth: ${participationGrowth >= 0 ? '+' : ''}${participationGrowth}`,
        ].join(', ');

        results.push(
          `Growth trends (${latest.year} vs ${previous.year}): ${growthSummary}`
        );
      }

      // Chart data for temporal analysis
      if (intent.chartRequested && participationStats.length > 1) {
        const chartData = {
          type: 'line',
          title: 'Census Participation Trends',
          data: participationStats.reverse().map((stat) => ({
            name: stat.year.toString(),
            members: stat.members,
            households: stat.households,
            participation: stat.participation,
          })),
        };
        results.push(`CHART_DATA: ${JSON.stringify(chartData)}`);
      }
    }
  } catch (error) {
    logSecureError('temporal_analysis_intent', error);
    results.push('Unable to process temporal analysis query at this time.');
  }
}

// 2025 Enhancement: Context-aware response generation based on communication style
export async function handleGeneralIntent(
  intent: QueryIntent,
  results: string[],
  userMessage?: string
): Promise<void> {
  try {
    const [memberCount, householdCount, codeCount, sacramentCount] =
      await Promise.all([
        prisma.member.count(),
        prisma.household.count(),
        prisma.uniqueCode.count(),
        prisma.sacrament.count(),
      ]);

    // Context-aware response based on user communication style
    const isDetailedRequest =
      userMessage &&
      (userMessage.toLowerCase().includes('detailed') ||
        userMessage.toLowerCase().includes('comprehensive') ||
        userMessage.toLowerCase().includes('full') ||
        userMessage.length > 50);

    if (isDetailedRequest) {
      // Provide comprehensive overview for detailed requests
      results.push('Comprehensive Census Overview:');
      results.push(`• Total Members: ${memberCount}`);
      results.push(`• Total Households: ${householdCount}`);
      results.push(`• Total Unique Codes: ${codeCount}`);
      results.push(`• Total Sacrament Records: ${sacramentCount}`);

      // Add participation rate if codes exist
      if (codeCount > 0) {
        const usedCodes = await prisma.uniqueCode.count({
          where: { isAssigned: true },
        });
        const participationRate = ((usedCodes / codeCount) * 100).toFixed(1);
        results.push(
          `• Census Participation Rate: ${participationRate}% (${usedCodes}/${codeCount})`
        );
      }

      // Add recent activity summary
      const recentMembers = await prisma.member.count({
        where: {
          createdAt: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
          },
        },
      });
      if (recentMembers > 0) {
        results.push(`• New Members (Last 30 days): ${recentMembers}`);
      }

      // Add community feedback summary
      const feedbackCount = await prisma.censusForm.count({
        where: { householdComment: { not: null } },
      });
      if (feedbackCount > 0) {
        results.push(`• Households with Community Feedback: ${feedbackCount}`);
      }
    } else {
      // Provide concise overview for brief requests
      results.push(
        `Census Summary: ${memberCount} members, ${householdCount} households, ${codeCount} codes, ${sacramentCount} sacraments.`
      );

      if (codeCount > 0) {
        const usedCodes = await prisma.uniqueCode.count({
          where: { isAssigned: true },
        });
        const participationRate = ((usedCodes / codeCount) * 100).toFixed(1);
        results.push(
          `Participation: ${participationRate}% (${usedCodes}/${codeCount} codes used).`
        );
      }
    }

    // Add helpful suggestions based on data availability
    if (memberCount === 0) {
      results.push(
        '💡 Tip: Start by adding member data to begin census tracking.'
      );
    } else if (householdCount === 0) {
      results.push(
        '💡 Tip: Consider organizing members into households for better census management.'
      );
    } else if (codeCount === 0) {
      results.push(
        '💡 Tip: Generate unique codes to enable census participation tracking.'
      );
    }
  } catch (error) {
    logSecureError('general_intent', error);
    results.push('Unable to process general query at this time.');
  }
}

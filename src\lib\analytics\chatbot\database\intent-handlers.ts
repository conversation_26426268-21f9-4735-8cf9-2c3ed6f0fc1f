/**
 * Intent Handlers Module
 *
 * Contains all intent-based query handler functions that process specific
 * user intents and generate appropriate database queries and responses.
 *
 * These functions handle:
 * - Member demographics intent processing
 * - Household information intent processing
 * - Sacrament records intent processing
 * - Census participation intent processing
 * - Temporal analysis intent processing
 * - General intent processing
 */

import { logSecureError } from '@/lib/analytics/chatbot/security';
import { prisma } from '@/lib/db/prisma';
import type { QueryIntent } from '@/types/analytics';

// Handle member demographics intent - Professional member data analysis
export async function handleMemberDemographicsIntent(
  intent: QueryIntent,
  results: string[]
): Promise<void> {
  try {
    if (intent.analysisType === 'count') {
      const memberCount = await prisma.member.count();
      results.push(`Total members: ${memberCount}`);
    } else if (intent.analysisType === 'distribution') {
      // Distribution queries are handled by generateDistributionTable
      results.push('Distribution analysis is handled by the table system.');
    } else if (intent.analysisType === 'list') {
      // NEW: Handle member list requests
      const whereCondition: any = {};

      // Apply filters if provided
      if (intent.filters?.gender) {
        whereCondition.gender = intent.filters.gender;
      }

      if (intent.filters?.ageRange) {
        const currentYear = new Date().getFullYear();
        const { min, max } = intent.filters.ageRange;

        if (min !== undefined || max !== undefined) {
          whereCondition.dateOfBirth = {};
          if (max !== undefined) {
            // For max age, calculate the earliest birth year
            whereCondition.dateOfBirth.gte = new Date(
              `${currentYear - max}-01-01`
            );
          }
          if (min !== undefined) {
            // For min age, calculate the latest birth year
            whereCondition.dateOfBirth.lte = new Date(
              `${currentYear - min}-12-31`
            );
          }
        }
      }

      // Add hobby filter
      if (intent.filters?.hobby) {
        whereCondition.hobby = {
          contains: intent.filters.hobby,
          mode: 'insensitive',
        };
      }

      // Add occupation filter
      if (intent.filters?.occupation) {
        whereCondition.occupation = {
          contains: intent.filters.occupation,
          mode: 'insensitive',
        };
      }

      const members = await prisma.member.findMany({
        where:
          Object.keys(whereCondition).length > 0 ? whereCondition : undefined,
        take: 20, // Reasonable limit for administrative purposes
        select: {
          firstName: true,
          lastName: true,
          gender: true,
          dateOfBirth: true,
          mobilePhone: true,
          hobby: true,
          occupation: true,
        },
        orderBy: [{ lastName: 'asc' }, { firstName: 'asc' }],
      });

      if (members.length === 0) {
        results.push('No members found matching the criteria.');
      } else {
        const currentYear = new Date().getFullYear();
        results.push(`Found ${members.length} member(s):`);

        const memberList = members
          .map((member) => {
            const age = member.dateOfBirth
              ? currentYear - member.dateOfBirth.getFullYear()
              : 'Unknown';
            const phone = member.mobilePhone ? ` | ${member.mobilePhone}` : '';
            const hobby = member.hobby ? ` | Hobby: ${member.hobby}` : '';
            const occupation = member.occupation
              ? ` | Occupation: ${member.occupation}`
              : '';
            return `${member.firstName} ${member.lastName} (${member.gender}, age ${age}${phone}${hobby}${occupation})`;
          })
          .join('\n');

        results.push(memberList);

        // Add summary if filtered
        if (Object.keys(whereCondition).length > 0) {
          const totalMembers = await prisma.member.count();
          results.push(
            `\nShowing ${members.length} of ${totalMembers} total members.`
          );
        }
      }
    } else {
      // General member overview
      const memberCount = await prisma.member.count();
      results.push(`Total members: ${memberCount}`);

      // 2025 Performance: Use optimized PostgreSQL queries
      const genderStats = await prisma.$queryRaw<
        Array<{ gender: string; count: bigint }>
      >`
        SELECT
          COALESCE(gender::text, 'Unknown') as gender,
          COUNT(*) as count
        FROM members
        GROUP BY gender
        ORDER BY count DESC
      `;
      const genderSummary = genderStats
        .map((stat) => `${stat.gender}: ${Number(stat.count)}`)
        .join(', ');
      results.push(`Gender distribution: ${genderSummary}`);

      // Add hobby distribution analysis - PostgreSQL optimized
      const hobbyStats = await prisma.$queryRaw<
        Array<{ hobby: string; count: bigint }>
      >`
        SELECT
          hobby,
          COUNT(*) as count
        FROM members
        WHERE hobby IS NOT NULL
        GROUP BY hobby
        ORDER BY count DESC
        LIMIT 10
      `;
      if (hobbyStats.length > 0) {
        const hobbySummary = hobbyStats
          .map((stat) => `${stat.hobby}: ${Number(stat.count)}`)
          .join(', ');
        results.push(`Top hobbies: ${hobbySummary}`);
      }

      // Add occupation distribution analysis - PostgreSQL optimized
      const occupationStats = await prisma.$queryRaw<
        Array<{ occupation: string; count: bigint }>
      >`
        SELECT
          occupation,
          COUNT(*) as count
        FROM members
        WHERE occupation IS NOT NULL
        GROUP BY occupation
        ORDER BY count DESC
        LIMIT 10
      `;
      if (occupationStats.length > 0) {
        const occupationSummary = occupationStats
          .map((stat) => `${stat.occupation}: ${Number(stat.count)}`)
          .join(', ');
        results.push(`Top occupations: ${occupationSummary}`);
      }
    }
  } catch (error) {
    logSecureError('member_demographics_intent', error);
    results.push('Unable to process member demographics query at this time.');
  }
}

// Handle household information intent - Professional household data analysis
export async function handleHouseholdInfoIntent(
  intent: QueryIntent,
  results: string[]
): Promise<void> {
  try {
    if (intent.analysisType === 'count') {
      const householdCount = await prisma.household.count();
      results.push(`Total households: ${householdCount}`);
    } else if (intent.analysisType === 'distribution') {
      // Distribution queries are handled by generateDistributionTable
      results.push('Distribution analysis is handled by the table system.');
    } else if (intent.analysisType === 'list') {
      // NEW: Handle household list requests
      const whereCondition: any = {};

      // Apply location filter if provided
      if (intent.filters?.location) {
        whereCondition.suburb = {
          contains: intent.filters.location,
          mode: 'insensitive',
        };
      }

      const households = await prisma.household.findMany({
        where:
          Object.keys(whereCondition).length > 0 ? whereCondition : undefined,
        take: 15, // Reasonable limit for administrative purposes
        include: {
          householdMembers: {
            where: { isCurrent: true },
            include: {
              member: {
                select: {
                  firstName: true,
                  lastName: true,
                  gender: true,
                  dateOfBirth: true,
                  hobby: true,
                  occupation: true,
                },
              },
            },
            orderBy: { relationship: 'asc' },
          },
          uniqueCodes: {
            take: 1,
            orderBy: { createdAt: 'desc' },
            select: {
              code: true,
              isAssigned: true,
            },
          },
          censusForms: {
            take: 1,
            orderBy: { createdAt: 'desc' },
            select: {
              householdComment: true,
              status: true,
              lastUpdated: true,
            },
          },
        },
        orderBy: [{ suburb: 'asc' }, { id: 'asc' }],
      });

      if (households.length === 0) {
        results.push('No households found matching the criteria.');
      } else {
        results.push(`Found ${households.length} household(s):`);

        const householdList = households
          .map((household, index) => {
            const members = household.householdMembers
              .map((hm) => {
                const member = hm.member;
                const currentYear = new Date().getFullYear();
                const age = member.dateOfBirth
                  ? currentYear - member.dateOfBirth.getFullYear()
                  : 'Unknown';
                const hobby = member.hobby ? `, hobby: ${member.hobby}` : '';
                const occupation = member.occupation
                  ? `, occupation: ${member.occupation}`
                  : '';
                return `${member.firstName} ${member.lastName} (${hm.relationship}, ${member.gender}, age ${age}${hobby}${occupation})`;
              })
              .join(', ');

            const uniqueCode = household.uniqueCodes[0];
            const codeInfo = uniqueCode
              ? ` | Code: ${uniqueCode.code} (${uniqueCode.isAssigned ? 'assigned' : 'available'})`
              : ' | No code assigned';

            const censusForm = household.censusForms[0];
            const communityFeedback = censusForm?.householdComment
              ? `\n   Community Feedback: "${censusForm.householdComment}"`
              : '';

            return `${index + 1}. ${household.suburb}${codeInfo}\n   Members: ${members || 'No current members'}${communityFeedback}`;
          })
          .join('\n\n');

        results.push(householdList);

        // Add summary if filtered
        if (Object.keys(whereCondition).length > 0) {
          const totalHouseholds = await prisma.household.count();
          results.push(
            `\nShowing ${households.length} of ${totalHouseholds} total households.`
          );
        }
      }
    } else {
      // General household overview
      const householdCount = await prisma.household.count();
      results.push(`Total households: ${householdCount}`);

      // Add community feedback summary
      const feedbackCount = await prisma.censusForm.count({
        where: { householdComment: { not: null } },
      });
      if (feedbackCount > 0) {
        results.push(`Households with community feedback: ${feedbackCount}`);

        // Get recent community feedback samples
        const recentFeedback = await prisma.censusForm.findMany({
          where: { householdComment: { not: null } },
          take: 3,
          orderBy: { lastUpdated: 'desc' },
          select: {
            householdComment: true,
            household: { select: { suburb: true } },
          },
        });

        if (recentFeedback.length > 0) {
          const feedbackSamples = recentFeedback
            .map(
              (form, index) =>
                `${index + 1}. ${form.household.suburb}: "${form.householdComment}"`
            )
            .join('\n');
          results.push(`Recent community feedback:\n${feedbackSamples}`);
        }
      }
    }
  } catch (error) {
    logSecureError('household_info_intent', error);
    results.push('Unable to process household info query at this time.');
  }
}

// Handle sacrament records intent - Professional sacrament data analysis
export async function handleSacramentRecordsIntent(
  intent: QueryIntent,
  results: string[]
): Promise<void> {
  try {
    if (intent.analysisType === 'list') {
      // NEW: Handle sacrament list requests (e.g., "who received first holy communion")
      const whereCondition: any = {};

      // Apply sacrament type filter if provided (AI-first approach)
      if (intent.filters?.sacramentType) {
        // AI has already validated and provided the correct sacrament code
        const sacramentType = await prisma.sacramentType.findFirst({
          where: {
            code: {
              equals: intent.filters.sacramentType,
              mode: 'insensitive',
            },
          },
        });

        if (sacramentType) {
          whereCondition.sacramentTypeId = sacramentType.id;
        } else {
          // This should rarely happen due to validation, but handle gracefully
          results.push(
            `No sacrament type found matching "${intent.filters.sacramentType}".`
          );
          return;
        }
      }

      // Apply census year filter if provided
      if (intent.filters?.censusYear) {
        whereCondition.censusYearId = intent.filters.censusYear;
      }

      const sacraments = await prisma.sacrament.findMany({
        where:
          Object.keys(whereCondition).length > 0 ? whereCondition : undefined,
        take: 25, // Reasonable limit for administrative purposes
        include: {
          member: {
            select: {
              firstName: true,
              lastName: true,
              gender: true,
              dateOfBirth: true,
            },
          },
          sacramentType: {
            select: {
              name: true,
              code: true,
            },
          },
          censusYear: {
            select: {
              year: true,
            },
          },
        },
        orderBy: [
          { date: 'desc' },
          { member: { lastName: 'asc' } },
          { member: { firstName: 'asc' } },
        ],
      });

      if (sacraments.length === 0) {
        const filterDesc = intent.filters?.sacramentType
          ? ` for ${intent.filters.sacramentType}`
          : '';
        results.push(`No sacrament records found${filterDesc}.`);
      } else {
        const sacramentTypeDesc = intent.filters?.sacramentType
          ? ` who received ${intent.filters.sacramentType}`
          : '';
        results.push(
          `Found ${sacraments.length} member(s)${sacramentTypeDesc}:`
        );

        const sacramentList = sacraments
          .map((sacrament, index) => {
            const member = sacrament.member;
            const currentYear = new Date().getFullYear();
            const age = member.dateOfBirth
              ? currentYear - member.dateOfBirth.getFullYear()
              : 'Unknown';
            const dateStr = sacrament.date
              ? sacrament.date.toDateString()
              : 'No date recorded';
            const place = sacrament.place ? ` at ${sacrament.place}` : '';

            return `${index + 1}. ${member.firstName} ${member.lastName} (${member.gender}, age ${age}) - ${sacrament.sacramentType.name} on ${dateStr}${place}`;
          })
          .join('\n');

        results.push(sacramentList);

        // Add summary if filtered
        if (Object.keys(whereCondition).length > 0) {
          const totalSacraments = await prisma.sacrament.count();
          results.push(
            `\nShowing ${sacraments.length} of ${totalSacraments} total sacrament records.`
          );
        }
      }
    } else if (intent.analysisType === 'count') {
      // Professional sacrament count analysis
      const sacramentCount = await prisma.sacrament.count();
      results.push(`Total sacrament records: ${sacramentCount}`);

      // Add breakdown by sacrament type for count queries
      if (sacramentCount > 0) {
        const sacramentStats = await prisma.sacrament.groupBy({
          by: ['sacramentTypeId'],
          _count: { sacramentTypeId: true },
          orderBy: { _count: { sacramentTypeId: 'desc' } },
        });

        const sacramentTypes = await prisma.sacramentType.findMany({
          where: { id: { in: sacramentStats.map((s) => s.sacramentTypeId) } },
        });

        const typeMap = Object.fromEntries(
          sacramentTypes.map((t) => [t.id, t.name])
        );

        const breakdown = sacramentStats
          .map(
            (stat) =>
              `${typeMap[stat.sacramentTypeId]}: ${stat._count?.sacramentTypeId || 0}`
          )
          .join(', ');
        results.push(`Sacrament breakdown: ${breakdown}`);
      }
    } else if (intent.analysisType === 'distribution') {
      // Distribution queries are handled by generateDistributionTable
      results.push('Distribution analysis is handled by the table system.');
    } else {
      // Default: Show general statistics
      const sacramentCount = await prisma.sacrament.count();
      results.push(`Total sacrament records: ${sacramentCount}`);

      if (sacramentCount > 0) {
        const sacramentStats = await prisma.sacrament.groupBy({
          by: ['sacramentTypeId'],
          _count: { sacramentTypeId: true },
          orderBy: { _count: { sacramentTypeId: 'desc' } },
        });

        const sacramentTypes = await prisma.sacramentType.findMany({
          where: { id: { in: sacramentStats.map((s) => s.sacramentTypeId) } },
        });

        const typeMap = Object.fromEntries(
          sacramentTypes.map((t) => [t.id, t.name])
        );
        const summary = sacramentStats
          .map(
            (stat) =>
              `${typeMap[stat.sacramentTypeId]}: ${stat._count.sacramentTypeId}`
          )
          .join(', ');
        results.push(`Sacrament distribution: ${summary}`);
      }
    }
  } catch (error) {
    logSecureError('sacrament_records_intent', error);
    results.push('Unable to process sacrament records query at this time.');
  }
}

// Handle census participation intent - Professional census participation analysis
export async function handleCensusParticipationIntent(
  intent: QueryIntent,
  results: string[]
): Promise<void> {
  try {
    if (intent.analysisType === 'list') {
      // NEW: Handle unique code list requests
      const whereCondition: any = {};

      // Apply filters if provided
      if (intent.filters?.censusYear) {
        whereCondition.censusYearId = intent.filters.censusYear;
      }

      const codes = await prisma.uniqueCode.findMany({
        where:
          Object.keys(whereCondition).length > 0 ? whereCondition : undefined,
        take: 20, // Reasonable limit for administrative purposes
        include: {
          household: {
            select: {
              suburb: true,
              householdMembers: {
                where: { isCurrent: true, relationship: 'head' },
                include: {
                  member: {
                    select: {
                      firstName: true,
                      lastName: true,
                    },
                  },
                },
                take: 1,
              },
            },
          },
          censusYear: {
            select: {
              year: true,
            },
          },
        },
        orderBy: [
          { isAssigned: 'asc' }, // Show available codes first
          { createdAt: 'desc' },
        ],
      });

      if (codes.length === 0) {
        results.push('No unique codes found matching the criteria.');
      } else {
        results.push(`Found ${codes.length} unique code(s):`);

        const codeList = codes
          .map((code, index) => {
            const status = code.isAssigned ? 'ASSIGNED' : 'AVAILABLE';
            const year = code.censusYear?.year || 'Unknown';

            let householdInfo = '';
            if (code.household) {
              const headMember = code.household.householdMembers[0]?.member;
              const headName = headMember
                ? `${headMember.firstName} ${headMember.lastName}`
                : 'Unknown';
              householdInfo = ` | ${code.household.suburb} | Head: ${headName}`;
            } else {
              householdInfo = ' | No household assigned';
            }

            return `${index + 1}. ${code.code} (${status}, ${year}${householdInfo})`;
          })
          .join('\n');

        results.push(codeList);

        // Add summary statistics
        const totalCodes = await prisma.uniqueCode.count();
        const assignedCount = codes.filter((c) => c.isAssigned).length;
        const availableCount = codes.filter((c) => !c.isAssigned).length;

        results.push(
          `\nSummary: ${assignedCount} assigned, ${availableCount} available (showing ${codes.length} of ${totalCodes} total codes).`
        );
      }
    } else {
      // Default: Show statistics
      const codeCount = await prisma.uniqueCode.count();
      results.push(`Total unique codes: ${codeCount}`);

      if (codeCount > 0) {
        const usedCodes = await prisma.uniqueCode.count({
          where: { isAssigned: true },
        });
        const availableCodes = codeCount - usedCodes;
        results.push(
          `Used codes: ${usedCodes}, Available codes: ${availableCodes}`
        );
      }
    }
  } catch (error) {
    logSecureError('census_participation_intent', error);
    results.push('Unable to process census participation query at this time.');
  }
}

// Re-export temporal and general intent handlers from separate module
export {
  handleGeneralIntent,
  handleTemporalAnalysisIntent,
} from './temporal-intent-handlers';

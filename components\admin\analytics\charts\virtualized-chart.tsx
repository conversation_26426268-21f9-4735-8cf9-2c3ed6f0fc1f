'use client';

import { Chevron<PERSON>eft, ChevronRight, MoreHorizontal } from 'lucide-react';
import { useTranslations } from 'next-intl';
import React, { useCallback, useMemo, useState } from 'react';
import { FixedSizeList as List } from 'react-window';
import { Button } from '@/components/ui/button';
import type { ChartData } from '@/lib/utils/chart-data-formatter';
import { type BaseChartProps, ChartFactory } from './chart-registry';

// Configuration for virtualization thresholds
const VIRTUALIZATION_CONFIG = {
  LARGE_DATASET_THRESHOLD: 1000,
  PAGINATION_THRESHOLD: 100,
  ITEMS_PER_PAGE: 50,
  VIRTUAL_ITEM_HEIGHT: 400,
  VIRTUAL_CONTAINER_HEIGHT: 600,
};

interface VirtualizedChartProps extends BaseChartProps {
  enableVirtualization?: boolean;
  itemsPerPage?: number;
}

// Paginated chart component for medium datasets
function PaginatedChart({
  data,
  isAnimationActive,
  className,
  itemsPerPage = VIRTUALIZATION_CONFIG.ITEMS_PER_PAGE,
}: VirtualizedChartProps) {
  const t = useTranslations('common');
  const _tPagination = useTranslations('pagination');
  const [currentPage, setCurrentPage] = useState(0);

  const totalPages = Math.ceil(data.data.length / itemsPerPage);
  const startIndex = currentPage * itemsPerPage;
  const endIndex = Math.min(startIndex + itemsPerPage, data.data.length);

  const paginatedData: ChartData = {
    ...data,
    data: data.data.slice(startIndex, endIndex),
  };

  const handlePrevPage = useCallback(() => {
    setCurrentPage((prev) => Math.max(0, prev - 1));
  }, []);

  const handleNextPage = useCallback(() => {
    setCurrentPage((prev) => Math.min(totalPages - 1, prev + 1));
  }, [totalPages]);

  const handlePageClick = useCallback((page: number) => {
    setCurrentPage(page);
  }, []);

  // Generate page numbers for pagination
  const visiblePages = useMemo(() => {
    const maxVisible = 5;
    const half = Math.floor(maxVisible / 2);
    let start = Math.max(0, currentPage - half);
    const end = Math.min(totalPages - 1, start + maxVisible - 1);

    if (end - start < maxVisible - 1) {
      start = Math.max(0, end - maxVisible + 1);
    }

    return Array.from({ length: end - start + 1 }, (_, i) => start + i);
  }, [currentPage, totalPages]);

  return (
    <div className={`w-full ${className || ''}`}>
      {/* Chart */}
      <ChartFactory
        className="w-full"
        data={paginatedData}
        isAnimationActive={isAnimationActive}
      />

      {/* Pagination Controls */}
      <div className="mt-4 flex items-center justify-between rounded-lg bg-slate-50 px-4 py-2 dark:bg-slate-800">
        <div className="text-slate-600 text-sm dark:text-slate-400">
          Showing {startIndex + 1}-{endIndex} of {data.data.length} items
        </div>

        <div className="flex items-center gap-2">
          <Button
            aria-label={t('goToPreviousPage')}
            className="h-8 w-8 p-0"
            disabled={currentPage === 0}
            onClick={handlePrevPage}
            size="sm"
            variant="outline"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>

          {/* Page Numbers */}
          <div className="flex items-center gap-1">
            {visiblePages.map((page) => (
              <Button
                aria-current={page === currentPage ? 'page' : undefined}
                aria-label={`Go to page ${page + 1}`}
                className="h-8 w-8 p-0 text-xs"
                key={`page-${page}`}
                onClick={() => handlePageClick(page)}
                size="sm"
                variant={page === currentPage ? 'default' : 'outline'}
              >
                {page + 1}
              </Button>
            ))}

            {totalPages > 5 && currentPage < totalPages - 3 && (
              <>
                <MoreHorizontal
                  aria-hidden="true"
                  className="h-4 w-4 text-slate-400"
                />
                <Button
                  aria-label={`Go to page ${totalPages}`}
                  className="h-8 w-8 p-0 text-xs"
                  onClick={() => handlePageClick(totalPages - 1)}
                  size="sm"
                  variant="outline"
                >
                  {totalPages}
                </Button>
              </>
            )}
          </div>

          <Button
            aria-label={t('goToNextPage')}
            className="h-8 w-8 p-0"
            disabled={currentPage === totalPages - 1}
            onClick={handleNextPage}
            size="sm"
            variant="outline"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}

// Memoized ChartItem component for better virtualization performance
const ChartItem = React.memo(
  ({
    index,
    style,
    chunkData,
    itemsPerPage,
    totalLength,
  }: {
    index: number;
    style: React.CSSProperties;
    chunkData: ChartData;
    itemsPerPage: number;
    totalLength: number;
  }) => {
    return (
      <div className="px-4 py-2" style={style}>
        <div className="overflow-hidden rounded-lg border border-slate-200 bg-white dark:border-slate-700 dark:bg-slate-900">
          <div className="border-slate-200 border-b bg-slate-50 p-4 dark:border-slate-700 dark:bg-slate-800">
            <h3 className="font-medium text-slate-900 text-sm dark:text-slate-100">
              {chunkData.title}
            </h3>
            <p className="mt-1 text-slate-500 text-xs dark:text-slate-400">
              Items {index * itemsPerPage + 1} -{' '}
              {Math.min((index + 1) * itemsPerPage, totalLength)}
            </p>
          </div>
          <div className="p-4">
            <ChartFactory
              className="w-full"
              data={chunkData} // Disable animations for performance
              isAnimationActive={false}
            />
          </div>
        </div>
      </div>
    );
  }
);
ChartItem.displayName = 'ChartItem';

// Virtualized chart component for very large datasets
function VirtualizedChartList({
  data,
  className,
  itemsPerPage = VIRTUALIZATION_CONFIG.ITEMS_PER_PAGE,
}: VirtualizedChartProps) {
  const t = useTranslations('common');
  const tPagination = useTranslations('pagination');
  const chunkedData = useMemo(() => {
    const chunkSize = itemsPerPage;
    const chunks: ChartData[] = [];

    for (let i = 0; i < data.data.length; i += chunkSize) {
      chunks.push({
        ...data,
        data: data.data.slice(i, i + chunkSize),
        title: `${data.title || 'Chart'} - Part ${Math.floor(i / chunkSize) + 1}`,
      });
    }

    return chunks;
  }, [data, itemsPerPage]);

  const renderItem = useCallback(
    ({ index, style }: { index: number; style: React.CSSProperties }) => (
      <ChartItem
        chunkData={chunkedData[index]}
        index={index}
        itemsPerPage={itemsPerPage}
        style={style}
        totalLength={data.data.length}
      />
    ),
    [chunkedData, itemsPerPage, data.data.length]
  );

  return (
    <div className={`w-full ${className || ''}`}>
      <div className="mb-4 rounded-lg border border-blue-200 bg-blue-50 p-4 dark:border-blue-800 dark:bg-blue-900/20">
        <p className="text-blue-800 text-sm dark:text-blue-200">
          <strong>{t('largeDatasetDetected')}:</strong>{' '}
          {tPagination('showingItemsInVirtualizedView', {
            count: data.data.length.toString(),
          })}
        </p>
      </div>

      <List
        className="thin-scrollbar"
        height={VIRTUALIZATION_CONFIG.VIRTUAL_CONTAINER_HEIGHT}
        itemCount={chunkedData.length}
        itemSize={VIRTUALIZATION_CONFIG.VIRTUAL_ITEM_HEIGHT}
        width="100%"
      >
        {renderItem}
      </List>
    </div>
  );
}

// Main virtualized chart component with automatic strategy selection
export function VirtualizedChart({
  data,
  isAnimationActive = true,
  className,
  enableVirtualization = true,
  itemsPerPage,
}: VirtualizedChartProps) {
  // Determine rendering strategy based on data size
  const dataSize = data.data.length;

  // Small datasets - render normally
  if (
    !enableVirtualization ||
    dataSize <= VIRTUALIZATION_CONFIG.PAGINATION_THRESHOLD
  ) {
    return (
      <ChartFactory
        className={className}
        data={data}
        isAnimationActive={isAnimationActive}
      />
    );
  }

  // Medium datasets - use pagination
  if (dataSize <= VIRTUALIZATION_CONFIG.LARGE_DATASET_THRESHOLD) {
    return (
      <PaginatedChart
        className={className}
        data={data}
        isAnimationActive={isAnimationActive}
        itemsPerPage={itemsPerPage}
      />
    );
  }

  // Large datasets - use virtualization
  return (
    <VirtualizedChartList
      className={className}
      data={data}
      itemsPerPage={itemsPerPage}
    />
  );
}

// Utility function to determine if virtualization is recommended
export function shouldUseVirtualization(dataLength: number): {
  recommended: boolean;
  strategy: 'normal' | 'pagination' | 'virtualization';
  reason: string;
} {
  if (dataLength <= VIRTUALIZATION_CONFIG.PAGINATION_THRESHOLD) {
    return {
      recommended: false,
      strategy: 'normal',
      reason: 'Dataset is small enough for normal rendering',
    };
  }

  if (dataLength <= VIRTUALIZATION_CONFIG.LARGE_DATASET_THRESHOLD) {
    return {
      recommended: true,
      strategy: 'pagination',
      reason: 'Medium dataset - pagination recommended for better UX',
    };
  }

  return {
    recommended: true,
    strategy: 'virtualization',
    reason: 'Large dataset - virtualization required for performance',
  };
}

export default VirtualizedChart;

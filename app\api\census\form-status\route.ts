import { type NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { getTranslations } from 'next-intl/server';
import { censusAuthOptions } from '@/lib/census-auth/census-auth-options';
import { prisma } from '@/lib/db/prisma';
import { getLocaleFromCookies } from '@/lib/utils/server-messages';
import { createCensusFormStatusSchema } from '@/lib/validation/census-form';

/**
 * GET /api/census/form-status
 *
 * Fetches the current status of the census form for the household
 */
export async function GET(_request: NextRequest) {
  // Get locale from centralized utility at the beginning
  const locale = await getLocaleFromCookies();
  const tErrors = await getTranslations({ locale, namespace: 'errors' });

  try {
    // Check if user is authenticated
    const session = await getServerSession(censusAuthOptions);

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has the household role
    if (session.user.role !== 'household') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Check if household ID exists in session
    if (!session.user.householdId) {
      return NextResponse.json(
        {
          error: tErrors('householdNotRegistered'),
          message: tErrors('pleaseCompleteHouseholdRegistrationFirst'),
        },
        { status: 400 }
      );
    }

    const householdId = Number.parseInt(session.user.householdId, 10);
    const censusYearId = Number.parseInt(session.user.censusYearId, 10);

    // Get the current form status
    const formStatus = await prisma.censusForm.findFirst({
      where: {
        householdId,
        censusYearId,
      },
    });

    // If no form status exists yet, return not_started
    if (!formStatus) {
      return NextResponse.json({
        status: 'not_started',
        lastUpdated: null,
        completionDate: null,
        householdComment: '',
      });
    }

    // Transform form status to match expected interface
    const transformedFormStatus = {
      id: formStatus.id,
      householdId: formStatus.householdId,
      censusYearId: formStatus.censusYearId,
      status: formStatus.status,
      lastUpdated: formStatus.lastUpdated
        ? formStatus.lastUpdated.toISOString()
        : null,
      completionDate: formStatus.completionDate
        ? formStatus.completionDate.toISOString()
        : null,
      householdComment: formStatus.householdComment || '',
      createdAt: formStatus.createdAt.toISOString(),
      updatedAt: formStatus.updatedAt.toISOString(),
    };

    return NextResponse.json(transformedFormStatus);
  } catch (error) {
    return NextResponse.json(
      {
        error: 'Failed to fetch form status',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/census/form-status
 *
 * Updates the status of the census form
 */
export async function PUT(request: NextRequest) {
  // Get locale from centralized utility at the beginning
  const locale = await getLocaleFromCookies();
  const _tErrors = await getTranslations({ locale, namespace: 'errors' });

  try {
    // Check if user is authenticated
    const session = await getServerSession(censusAuthOptions);

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has the household role
    if (session.user.role !== 'household') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Check if household ID exists in session
    if (!session.user.householdId) {
      return NextResponse.json(
        {
          error: 'Household not registered',
          message: 'Please complete household registration first',
        },
        { status: 400 }
      );
    }

    // Parse request body
    const body = await request.json();

    // Validate request body with translations
    const censusFormStatusSchema = await createCensusFormStatusSchema(locale);
    const validationResult = censusFormStatusSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Invalid request data',
          details: validationResult.error.issues,
        },
        { status: 400 }
      );
    }

    const data = validationResult.data;
    const householdId = Number.parseInt(session.user.householdId, 10);
    const censusYearId = Number.parseInt(session.user.censusYearId, 10);

    // Verify the household ID matches the session
    if (data.householdId !== householdId) {
      return NextResponse.json(
        {
          error: 'Household ID mismatch',
        },
        { status: 400 }
      );
    }

    // Verify the census year ID matches the session
    if (data.censusYearId !== censusYearId) {
      return NextResponse.json(
        {
          error: 'Census year ID mismatch',
        },
        { status: 400 }
      );
    }

    // Update the form status
    const updateData: Record<string, unknown> = {
      status: data.status,
      lastUpdated: new Date(),
    };

    // Include household comment if provided
    if (data.householdComment !== undefined) {
      updateData.householdComment = data.householdComment || null;
    }

    // If marking as completed, set the completion date
    if (data.status === 'completed') {
      updateData.completionDate = new Date();
    }

    const updatedFormStatus = await prisma.censusForm.upsert({
      where: {
        householdId_censusYearId: {
          householdId,
          censusYearId,
        },
      },
      update: updateData,
      create: {
        householdId,
        censusYearId,
        ...updateData,
      },
    });

    // Transform updated form status to match expected interface
    const transformedUpdatedFormStatus = {
      id: updatedFormStatus.id,
      householdId: updatedFormStatus.householdId,
      censusYearId: updatedFormStatus.censusYearId,
      status: updatedFormStatus.status,
      lastUpdated: updatedFormStatus.lastUpdated
        ? updatedFormStatus.lastUpdated.toISOString()
        : null,
      completionDate: updatedFormStatus.completionDate
        ? updatedFormStatus.completionDate.toISOString()
        : null,
      householdComment: updatedFormStatus.householdComment || '',
      createdAt: updatedFormStatus.createdAt.toISOString(),
      updatedAt: updatedFormStatus.updatedAt.toISOString(),
    };

    return NextResponse.json({
      success: true,
      message: 'Form status updated successfully',
      formStatus: transformedUpdatedFormStatus,
    });
  } catch (error) {
    return NextResponse.json(
      {
        error: 'Failed to update form status',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

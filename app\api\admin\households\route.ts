import { type NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { getTranslations } from 'next-intl/server';
import { authOptions } from '@/lib/auth/auth-options';
import { createHousehold } from '@/lib/db/households';
import { validateRequestData } from '@/lib/utils/api-validation-helpers';
import { getErrorMessage } from '@/lib/utils/error-handling';
import { getLocaleFromCookies } from '@/lib/utils/server-messages';
import { createAdminHouseholdSchema } from '@/lib/validation/household';

/**
 * POST /api/admin/households
 *
 * Creates a new household
 * Only accessible to admin users
 */
export async function POST(request: NextRequest) {
  // Get locale from centralized utility at the beginning
  const locale = await getLocaleFromCookies();
  const tErrors = await getTranslations({ locale, namespace: 'errors' });

  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Require admin authentication
    if (!session || session.user.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse and validate request body with translations
    const body = await request.json();
    const householdSchema = await createAdminHouseholdSchema(locale);

    const validation = await validateRequestData(householdSchema, body, locale);
    if (!validation.success) {
      return validation.response;
    }

    const data = validation.data;

    // Create the household
    const household = await createHousehold({
      suburb: data.suburb,
      firstCensusYearId: data.firstCensusYearId,
      lastCensusYearId: data.lastCensusYearId,
    });

    return NextResponse.json(household, { status: 201 });
  } catch (error) {
    return NextResponse.json(
      {
        error: tErrors('householdCreateFailed'),
        details: getErrorMessage(error),
      },
      { status: 500 }
    );
  }
}

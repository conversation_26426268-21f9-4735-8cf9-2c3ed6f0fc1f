'use client';

import { Users } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useEffect, useState } from 'react';
import { Bar, Bar<PERSON>hart, CartesianGrid, XAxis, YAxis } from 'recharts';
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart';
import { devLog } from '@/lib/utils';

interface AgeDistributionData {
  ageGroups: {
    label: string;
    count: number;
    percentage: number;
    genderBreakdown: {
      male: number;
      female: number;
      other: number;
      malePercentage: number;
      femalePercentage: number;
      otherPercentage: number;
    };
  }[];
  totalMembers: number;
}

export function AgeDistributionChart() {
  const t = useTranslations();
  const tGenders = useTranslations('genders');
  const [data, setData] = useState<AgeDistributionData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchDemographics = async () => {
      try {
        const response = await fetch('/api/admin/dashboard/demographics');
        if (response.ok) {
          const demographicData = await response.json();
          setData({
            ageGroups: demographicData.ageGroups,
            totalMembers: demographicData.totalMembers,
          });
        }
      } catch (error) {
        devLog.error('Error fetching age distribution:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDemographics();
  }, []);

  if (loading || !data) {
    return null;
  }

  return (
    <div className="relative">
      {/* Background glow effect */}
      <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-blue-500/5 via-[#97A4FF]/5 to-[#FF6308]/5 blur-2xl dark:from-blue-500/10 dark:via-[#97A4FF]/10 dark:to-[#FF6308]/10" />

      {/* Main card */}
      <div className="relative rounded-2xl border border-white/30 bg-white/80 p-4 shadow-lg shadow-slate-200/30 backdrop-blur-xl sm:p-6 lg:p-8 dark:border-slate-700/30 dark:bg-slate-800/80 dark:shadow-slate-900/30">
        {/* Card header - Mobile Responsive */}
        <div className="mb-6 space-y-4 sm:mb-8">
          <div className="flex items-center gap-3 sm:gap-4">
            <div className="rounded-xl bg-gradient-to-r from-[#97A4FF]/10 to-[#FF6308]/10 p-2 sm:p-3 dark:from-[#97A4FF]/20 dark:to-[#FF6308]/20">
              <div className="rounded-lg bg-gradient-to-r from-[#97A4FF] to-[#FF6308] p-1.5 text-white sm:p-2">
                <Users className="h-5 w-5 sm:h-6 sm:w-6" />
              </div>
            </div>
            <div className="min-w-0 flex-1 space-y-1">
              <h2 className="font-light text-slate-800 text-xl tracking-tight sm:text-2xl dark:text-slate-100">
                {t('common.ageDistribution')}
              </h2>
              <p className="font-medium text-slate-600 text-sm sm:text-base dark:text-slate-300">
                {t('admin.ageGroupBreakdownByGender')}
              </p>
            </div>
          </div>
        </div>

        {/* Chart Container - Same Structure as Gender Distribution */}
        <div className="flex h-[300px] flex-col">
          {/* Chart Area */}
          <div className="min-h-0 flex-1 overflow-hidden">
            <ChartContainer
              className="h-full w-full"
              config={{
                male: {
                  label: tGenders('male'),
                  color: '#97A4FF',
                },
                female: {
                  label: tGenders('female'),
                  color: '#FF6308',
                },
                other: {
                  label: tGenders('other'),
                  color: '#BDC9E6',
                },
              }}
            >
              <BarChart
                data={data.ageGroups.map((group) => ({
                  ageGroup: group.label,
                  male: group.genderBreakdown.male,
                  female: group.genderBreakdown.female,
                  other: group.genderBreakdown.other,
                  total: group.count,
                }))}
                margin={{
                  top: 20,
                  right: 15,
                  left: 15,
                  bottom: 5,
                }}
              >
                <CartesianGrid className="stroke-muted" strokeDasharray="3 3" />
                <XAxis
                  className="text-xs"
                  dataKey="ageGroup"
                  interval={0}
                  tick={{ fontSize: 10 }}
                />
                <YAxis className="text-xs" tick={{ fontSize: 10 }} width={45} />
                <ChartTooltip
                  content={<ChartTooltipContent />}
                  cursor={{ fill: 'rgba(0, 0, 0, 0.1)' }}
                />
                <Bar
                  dataKey="male"
                  fill="var(--color-male)"
                  radius={[0, 0, 0, 0]}
                  stackId="a"
                />
                <Bar
                  dataKey="female"
                  fill="var(--color-female)"
                  radius={[0, 0, 0, 0]}
                  stackId="a"
                />
                {data.ageGroups.some(
                  (group) => group.genderBreakdown.other > 0
                ) && (
                  <Bar
                    dataKey="other"
                    fill="var(--color-other)"
                    radius={[4, 4, 0, 0]}
                    stackId="a"
                  />
                )}
              </BarChart>
            </ChartContainer>
          </div>

          {/* Legend - Inside Container Like Gender Distribution */}
          <div className="flex flex-wrap justify-center gap-4">
            <div className="flex items-center gap-2">
              <div className="h-3 w-3 rounded-sm bg-[#97A4FF]" />
              <span className="font-medium text-slate-600 text-xs dark:text-slate-400">
                {tGenders('male')} (
                {data.ageGroups.reduce(
                  (sum, group) => sum + group.genderBreakdown.male,
                  0
                )}
                )
              </span>
            </div>
            <div className="flex items-center gap-2">
              <div className="h-3 w-3 rounded-sm bg-[#FF6308]" />
              <span className="font-medium text-slate-600 text-xs dark:text-slate-400">
                {tGenders('female')} (
                {data.ageGroups.reduce(
                  (sum, group) => sum + group.genderBreakdown.female,
                  0
                )}
                )
              </span>
            </div>
            {data.ageGroups.some(
              (group) => group.genderBreakdown.other > 0
            ) && (
              <div className="flex items-center gap-2">
                <div className="h-3 w-3 rounded-sm bg-[#BDC9E6]" />
                <span className="font-medium text-slate-600 text-xs dark:text-slate-400">
                  {tGenders('other')} (
                  {data.ageGroups.reduce(
                    (sum, group) => sum + group.genderBreakdown.other,
                    0
                  )}
                  )
                </span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

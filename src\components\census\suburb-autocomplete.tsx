'use client';

import { Check, ChevronsUpDown, Loader2, MapPin } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useCallback, useEffect, useRef, useState } from 'react';
import {
  type Control,
  Controller,
  type FieldError,
  type FieldValues,
} from 'react-hook-form';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import { Label } from '@/components/ui/label';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/lib/utils';

// Interface for suburb data from API
interface SuburbOption {
  id: number;
  displayName: string;
  suburbName: string;
  stateCode: string;
}

// API response interface
interface SuburbSearchResponse {
  success: boolean;
  results: SuburbOption[];
  count: number;
  query: string;
}

interface SuburbAutocompleteProps<T extends FieldValues = FieldValues> {
  control: Control<T>;
  name: keyof T | string;
  label: string;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  error?: FieldError;
  className?: string;
}

export function SuburbAutocomplete<T extends FieldValues = FieldValues>({
  control,
  name,
  label,
  placeholder,
  required = false,
  disabled = false,
  error,
  className,
}: SuburbAutocompleteProps<T>) {
  const t = useTranslations();
  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [suburbs, setSuburbs] = useState<SuburbOption[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchError, setSearchError] = useState<string | null>(null);

  // Refs for cleanup
  const abortControllerRef = useRef<AbortController | null>(null);
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Debounced search function
  const searchSuburbs = useCallback(async (query: string) => {
    if (query.length < 2) {
      setSuburbs([]);
      setIsLoading(false);
      return;
    }

    try {
      // Cancel previous request
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      // Create new abort controller
      abortControllerRef.current = new AbortController();

      setIsLoading(true);
      setSearchError(null);

      const response = await fetch(
        `/api/census/suburbs?q=${encodeURIComponent(query)}`,
        {
          signal: abortControllerRef.current.signal,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        const errorMessage =
          errorData.details || errorData.error || `HTTP ${response.status}`;
        throw new Error(errorMessage);
      }

      const data: SuburbSearchResponse = await response.json();

      if (data.success) {
        setSuburbs(data.results);
      } else {
        throw new Error('Search failed');
      }
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        // Request was cancelled, ignore
        return;
      }

      console.error('Suburb search error:', error);
      setSearchError(error instanceof Error ? error.message : 'Search failed');
      setSuburbs([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Handle search input changes with debouncing
  useEffect(() => {
    // Clear previous timeout
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    // Set new timeout for debounced search
    debounceTimeoutRef.current = setTimeout(() => {
      searchSuburbs(searchQuery);
    }, 300);

    // Cleanup function
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, [searchQuery, searchSuburbs]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  return (
    <div className={cn('space-y-2', className)}>
      <Label className="font-medium text-sm">
        {label}
        {required && <span className="ml-1 text-destructive">*</span>}
      </Label>

      <Controller
        control={control}
        name={name as any}
        render={({ field }) => (
          <Popover onOpenChange={setOpen} open={open}>
            <PopoverTrigger asChild>
              <Button
                aria-expanded={open}
                aria-label={`Select ${label.toLowerCase()}`}
                className={cn(
                  'w-full cursor-pointer justify-between hover:bg-accent hover:text-accent-foreground',
                  !field.value && 'text-muted-foreground',
                  error && 'border-destructive',
                  disabled && 'cursor-not-allowed hover:bg-transparent'
                )}
                disabled={disabled}
                role="combobox"
                variant="outline"
              >
                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4 shrink-0" />
                  <span className="truncate">
                    {String(field.value || placeholder || '')}
                  </span>
                </div>
                <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
              </Button>
            </PopoverTrigger>
            <PopoverContent
              align="start"
              className="p-0"
              style={{ width: 'var(--radix-popover-trigger-width)' }}
            >
              <Command shouldFilter={false}>
                <CommandInput
                  className="h-9"
                  onValueChange={setSearchQuery}
                  placeholder={
                    placeholder || t('forms.typeAtLeast2CharactersToSearch')
                  }
                  value={searchQuery}
                />
                <CommandList className="max-h-[250px]">
                  {isLoading && (
                    <div className="flex items-center justify-center py-6">
                      <Loader2 className="h-4 w-4 animate-spin" />
                      <span className="ml-2 text-muted-foreground text-sm">
                        Searching suburbs...
                      </span>
                    </div>
                  )}

                  {searchError && (
                    <div className="py-6 text-center text-destructive text-sm">
                      {searchError}
                    </div>
                  )}

                  {!(isLoading || searchError) &&
                    searchQuery.length >= 2 &&
                    suburbs.length === 0 && (
                      <CommandEmpty>{t('common.noSuburbsFound')}</CommandEmpty>
                    )}

                  {!(isLoading || searchError) && searchQuery.length < 2 && (
                    <div className="py-6 text-center text-muted-foreground text-sm">
                      {t('forms.typeAtLeast2CharactersToSearch')}
                    </div>
                  )}

                  {!(isLoading || searchError) && suburbs.length > 0 && (
                    <CommandGroup>
                      {suburbs.map((suburb) => (
                        <CommandItem
                          className="flex cursor-pointer items-center gap-2"
                          key={suburb.id}
                          onSelect={() => {
                            field.onChange(suburb.displayName);
                            setOpen(false);
                            setSearchQuery('');
                          }}
                          value={suburb.displayName}
                        >
                          <Check
                            className={cn(
                              'h-4 w-4',
                              field.value === suburb.displayName
                                ? 'opacity-100'
                                : 'opacity-0'
                            )}
                          />
                          <MapPin className="h-4 w-4 text-muted-foreground" />
                          <span>{suburb.displayName}</span>
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  )}
                </CommandList>
              </Command>
            </PopoverContent>
          </Popover>
        )}
      />

      {error && <p className="text-destructive text-xs">{error.message}</p>}
    </div>
  );
}

'use client';

import { AlertCircle, RefreshCw } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { Button } from '@/components/ui/button';

interface WelcomeModalErrorFallbackProps {
  error?: Error;
  resetError?: () => void;
}

/**
 * Error fallback component for welcome modal
 * Provides graceful error handling with user-friendly messaging
 */
export function WelcomeModalErrorFallback({
  error,
  resetError,
}: WelcomeModalErrorFallbackProps) {
  const t = useTranslations('errors');

  return (
    <div className="flex flex-col items-center justify-center space-y-4 p-6 text-center">
      <div className="flex h-12 w-12 items-center justify-center rounded-full bg-destructive/10">
        <AlertCircle className="h-6 w-6 text-destructive" />
      </div>

      <div className="space-y-2">
        <h3 className="font-semibold text-foreground text-lg">
          {t('somethingWentWrong')}
        </h3>
        <p className="max-w-sm text-muted-foreground text-sm">
          {t('unableToLoadWelcomeModal')}
        </p>
      </div>

      {resetError && (
        <Button
          className="flex items-center gap-2"
          onClick={resetError}
          size="sm"
          variant="outline"
        >
          <RefreshCw className="h-4 w-4" />
          {t('tryAgain')}
        </Button>
      )}

      {process.env.NODE_ENV === 'development' && error && (
        <details className="mt-4 text-left text-xs">
          <summary className="cursor-pointer text-muted-foreground">
            Technical Details (Development Only)
          </summary>
          <pre className="mt-2 overflow-auto rounded bg-muted p-2 text-xs">
            {error.message}
          </pre>
        </details>
      )}
    </div>
  );
}

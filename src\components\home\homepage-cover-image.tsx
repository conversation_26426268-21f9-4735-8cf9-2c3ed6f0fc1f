'use client';

import Image from 'next/image';
import { useTranslations } from 'next-intl';

export function HomepageCoverImage() {
  const t = useTranslations('common');
  return (
    <div className="relative hidden h-full bg-muted lg:block">
      <div className="absolute inset-0 z-10 bg-gradient-to-r from-primary/10 via-transparent to-primary/5" />
      <Image
        alt={t('communityGathering')}
        className="absolute inset-0 h-full w-full object-cover dark:brightness-[0.2] dark:grayscale"
        fill
        onError={(e) => {
          const target = e.target as HTMLImageElement;
          target.src = '/images/community.svg';
        }}
        priority
        sizes="(max-width: 1024px) 0px, 50vw"
        src="/images/home-right.webp"
        style={{ objectFit: 'cover', objectPosition: 'center 20%' }}
      />
    </div>
  );
}

import { type NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { getTranslations } from 'next-intl/server';
import { z } from 'zod/v4';
import { authOptions } from '@/lib/auth/auth-options';
import { prisma } from '@/lib/db/prisma';
import { getErrorMessage } from '@/lib/utils/error-handling';
import { getLocaleFromCookies } from '@/lib/utils/server-messages';
import { createBulkHouseholdValidationSchema } from '@/lib/validation/household';

/**
 * POST /api/admin/households/bulk-delete
 *
 * Deletes multiple households
 * Only accessible to admin users
 * Cannot delete households with members
 */
export async function POST(request: NextRequest) {
  // Get locale from centralized utility at the beginning
  const locale = await getLocaleFromCookies();
  const t = await getTranslations({ locale, namespace: 'errors' });

  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Require admin authentication
    if (!session || session.user.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse and validate request body using factory function
    const body = await request.json();
    const bulkDeleteSchema = await createBulkHouseholdValidationSchema(locale);
    const validatedData = bulkDeleteSchema.parse(body);
    const { householdIds } = validatedData;

    // Process deletion for each household
    // Note: The validation is now handled by the bulk-delete-validation endpoint
    // This endpoint trusts that only valid household IDs are sent

    // Delete the households and update associated unique codes using Prisma transaction
    const results = await prisma.$transaction(async (tx) => {
      const deleteResults = [];

      for (const id of householdIds) {
        // Ensure id is a number
        const householdId =
          typeof id === 'number' ? id : Number.parseInt(String(id), 10);

        // Check if household exists first
        const existingHousehold = await tx.household.findUnique({
          where: { id: householdId },
        });

        if (!existingHousehold) {
          deleteResults.push({
            id: householdId,
            deleted: true,
            note: 'not_found',
          });
          continue;
        }

        // Get all member IDs for this household
        const householdMembers = await tx.householdMember.findMany({
          where: { householdId, isCurrent: true },
          select: { memberId: true },
        });

        const memberIds = householdMembers.map((hm) => hm.memberId);

        // Delete sacraments for all members in this household
        if (memberIds.length > 0) {
          await tx.sacrament.deleteMany({
            where: { memberId: { in: memberIds } },
          });
        }

        // Update any associated unique codes to unassigned
        await tx.uniqueCode.updateMany({
          where: { householdId },
          data: {
            isAssigned: false,
            assignedAt: null,
            householdId: null,
            updatedAt: new Date(),
          },
        });

        // Delete household member relationships
        await tx.householdMember.deleteMany({
          where: { householdId },
        });

        // Delete all members in this household
        if (memberIds.length > 0) {
          await tx.member.deleteMany({
            where: { id: { in: memberIds } },
          });
        }

        // Delete the household with error handling
        try {
          await tx.household.delete({
            where: { id: householdId },
          });
          deleteResults.push({ id: householdId, deleted: true });
        } catch (error: unknown) {
          // Handle P2025 error (record not found) - household already deleted
          if (
            error &&
            typeof error === 'object' &&
            'code' in error &&
            error.code === 'P2025'
          ) {
            deleteResults.push({
              id: householdId,
              deleted: true,
              note: 'already_deleted',
            });
          } else {
            // Re-throw other errors
            throw error;
          }
        }
      }

      return deleteResults;
    });

    // Success message handled client-side for consistency with other admin operations
    return NextResponse.json({
      success: true,
      results,
    });
  } catch (error) {
    // Handle validation errors (reuse locale and t from function scope)
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: t('invalidRequestData'),
          details: error.issues,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        error: t('failedToProcessBulkDelete'),
        details: getErrorMessage(error),
      },
      { status: 500 }
    );
  }
}

import { type NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { getTranslations } from 'next-intl/server';
import { z } from 'zod/v4';
import { authOptions } from '@/lib/auth/auth-options';
import { prisma } from '@/lib/db/prisma';
import { getLocaleFromCookies } from '@/lib/utils/server-messages';

// Validation schema for the request
const uniqueCodeIdsSchema = z.object({
  ids: z.string().transform((str) =>
    str
      .split(',')
      .map((id) => Number.parseInt(id.trim(), 10))
      .filter((id) => !Number.isNaN(id))
  ),
});

/**
 * GET endpoint to fetch unique codes by their IDs
 */
export async function GET(request: NextRequest) {
  // Get locale from centralized utility at the beginning
  const locale = await getLocaleFromCookies();
  const t = await getTranslations({ locale, namespace: 'errors' });

  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Require admin authentication
    if (!session || session.user.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const idsParam = searchParams.get('ids');

    if (!idsParam) {
      return NextResponse.json(
        { error: 'No code IDs provided' },
        { status: 400 }
      );
    }

    // Validate and parse the IDs
    let codeIds: number[];
    try {
      const validated = uniqueCodeIdsSchema.parse({ ids: idsParam });
      codeIds = validated.ids;
    } catch {
      return NextResponse.json(
        { error: 'Invalid code IDs format' },
        { status: 400 }
      );
    }

    if (codeIds.length === 0) {
      return NextResponse.json(
        { error: 'No valid code IDs provided' },
        { status: 400 }
      );
    }

    // Fetch the codes using Prisma - SECURITY: Only select non-sensitive fields
    const uniqueCodes = await prisma.uniqueCode.findMany({
      where: {
        id: { in: codeIds },
      },
      select: {
        id: true,
        code: true,
        isAssigned: true,
        assignedAt: true,
        householdId: true,
        censusYearId: true,
        createdAt: true,
        updatedAt: true,
        // SECURITY: Explicitly exclude validation fields (validationStart, validationEnd, validationHash)
        censusYear: {
          select: {
            year: true,
            isActive: true,
          },
        },
      },
      orderBy: { id: 'asc' },
    });

    // Transform to match expected format
    const transformedCodes = uniqueCodes.map((uc) => ({
      id: uc.id,
      code: uc.code,
      isAssigned: uc.isAssigned,
      assignedAt: uc.assignedAt,
      householdId: uc.householdId,
      censusYearId: uc.censusYearId,
      createdAt: uc.createdAt,
      updatedAt: uc.updatedAt,
      census_year: uc.censusYear?.year,
      is_active_year: uc.censusYear?.isActive,
    }));

    // Return the results
    return NextResponse.json({
      success: true,
      data: transformedCodes,
    });
  } catch (_error) {
    return NextResponse.json(
      { error: t('failedToFetchUniqueCodes') },
      { status: 500 }
    );
  }
}

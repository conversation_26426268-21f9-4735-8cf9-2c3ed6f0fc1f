# =============================================================================
# WSCCC Census System - Environment Configuration
# =============================================================================
# Copy this file to .env.local and update the values for your environment
# Generate secure secrets using: openssl rand -base64 32

# =============================================================================
# DATABASE CONFIGURATION (PostgreSQL with Prisma ORM)
# =============================================================================

# Primary database connection URL (required)
DATABASE_URL="postgresql://username:password@localhost:5432/wsccc_census_db_pg?schema=public"

# Optional: Direct database URL for connection pooling (used by some hosting providers)
# POSTGRES_URL_NON_POOLING="postgresql://username:password@localhost:5432/wsccc_census_db_pg?schema=public"

# =============================================================================
# AUTHENTICATION CONFIGURATION (TWO INDEPENDENT SYSTEMS)
# =============================================================================
# IMPORTANT: Each system uses a separate secret key for complete isolation
# Generate unique 32+ character secrets for each system

# Base URL for the application
NEXTAUTH_URL=http://localhost:3000
# Enter the full URL including http:// or https:// without a trailing slash for example https://wsccc-census.vercel.app

# Admin Portal Authentication (for admin users)
NEXTAUTH_SECRET_ADMIN=your_admin_secret_key_here_32_chars_minimum

# Census Portal Authentication (for census participants)
NEXTAUTH_SECRET_CENSUS=your_census_secret_key_here_32_chars_minimum

# =============================================================================
# AI ANALYTICS CONFIGURATION (Vercel AI SDK with Google Gemini)
# =============================================================================

# Google Gemini API Key for AI-powered analytics chatbot
# Get your API key from: https://aistudio.google.com/app/apikey
GOOGLE_GEMINI_API_KEY=your_google_gemini_api_key_here
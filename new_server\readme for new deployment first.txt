# WSCCC Census System - New Server Deployment

## Quick Setup

# 1. Create the database first
createdb wsccc_census_db_pg

# 2. Then run the setup script
cd new_server
node setup-new-server.mjs

## Important Notes

- The system uses PostgreSQL with Prisma ORM
- Database uses snake_case field names (member_id, household_id, etc.)
- Prisma maps these to camelCase in TypeScript (memberId, householdId, etc.)
- Application layer includes field mapping to ensure frontend compatibility
- All field mapping issues have been resolved in the codebase
import { AlertTriangle } from 'lucide-react';
import { useTranslations } from 'next-intl';
import React from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface ErrorBoundaryWrapperProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

interface ErrorBoundaryInternalProps extends ErrorBoundaryWrapperProps {
  translations: {
    somethingWentWrong: string;
    unableToRenderComponent: string;
    errorDetailsDevOnly: string;
  };
}

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

/**
 * Internal error boundary class component for catching and displaying errors
 * Provides graceful error handling for chart and table components
 */
class ErrorBoundaryInternal extends React.Component<
  ErrorBoundaryInternalProps,
  ErrorBoundaryState
> {
  constructor(props: ErrorBoundaryInternalProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    if (process.env.NODE_ENV === 'development') {
      console.error('ErrorBoundaryWrapper caught an error:', error, errorInfo);
    }
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="my-4 rounded-lg border border-red-200 bg-red-50 p-4 dark:border-red-800 dark:bg-red-900/20">
          <Alert className="border-0 bg-transparent">
            <AlertTriangle className="h-4 w-4 text-red-600 dark:text-red-400" />
            <AlertDescription className="text-red-700 dark:text-red-300">
              <div className="space-y-1">
                <p className="font-medium">
                  {this.props.translations.somethingWentWrong}
                </p>
                <p className="text-sm opacity-90">
                  {this.props.translations.unableToRenderComponent}
                </p>
                {process.env.NODE_ENV === 'development' && this.state.error && (
                  <details className="mt-2">
                    <summary className="cursor-pointer text-xs">
                      {this.props.translations.errorDetailsDevOnly}
                    </summary>
                    <pre className="mt-1 overflow-auto rounded bg-red-100 p-2 text-xs dark:bg-red-900/40">
                      {this.state.error.message}
                    </pre>
                  </details>
                )}
              </div>
            </AlertDescription>
          </Alert>
        </div>
      );
    }

    return this.props.children;
  }
}

/**
 * Public wrapper component that provides translations to the error boundary
 */
export function ErrorBoundaryWrapper({
  children,
  fallback,
}: ErrorBoundaryWrapperProps) {
  const t = useTranslations('errors');

  const translations = {
    somethingWentWrong: t('somethingWentWrong'),
    unableToRenderComponent: t('unableToRenderComponent'),
    errorDetailsDevOnly: t('errorDetailsDevOnly'),
  };

  return (
    <ErrorBoundaryInternal fallback={fallback} translations={translations}>
      {children}
    </ErrorBoundaryInternal>
  );
}

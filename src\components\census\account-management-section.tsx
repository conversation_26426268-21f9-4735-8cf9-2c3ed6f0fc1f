'use client';

import { useTranslations } from 'next-intl';
import { DeleteAccountButton } from '@/components/census/delete-account-button';

/**
 * AccountManagementSection component
 *
 * Displays account management options for census participants,
 * including the option to delete their account.
 */
export function AccountManagementSection() {
  const tCommon = useTranslations('common');

  return (
    <div>
      <div className="flex flex-col space-y-4">
        <div className="rounded-lg bg-destructive/5 p-4">
          <div className="mb-2 flex items-center gap-2">
            <svg
              className="h-4 w-4 text-destructive"
              fill="none"
              height="24"
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              viewBox="0 0 24 24"
              width="24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path d="M3 6h18" />
              <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6" />
              <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2" />
              <line x1="10" x2="10" y1="11" y2="17" />
              <line x1="14" x2="14" y1="11" y2="17" />
            </svg>
            <h3 className="font-medium text-base text-destructive">
              {tCommon('deleteAccount')}
            </h3>
          </div>
          <p className="mb-4 text-destructive text-sm">
            {tCommon('permanentlyDeleteYourAccountAnd')}
          </p>
          <DeleteAccountButton />
        </div>
      </div>
    </div>
  );
}

'use client';

import React, { useCallback, useEffect, useRef, useState } from 'react';

interface TourPopoverProps {
  targetElement: HTMLElement;
  content: { description: string; title?: string };
  onClose: () => void;
  preferredPosition?: 'top' | 'bottom' | 'left' | 'right';
}

/**
 * Elegant Tour Popover - Clean, minimal design
 */
export function TourPopover({
  targetElement,
  content,
  onClose,
  preferredPosition = 'bottom',
}: TourPopoverProps) {
  const popoverRef = useRef<HTMLDivElement>(null);
  const [position, setPosition] = useState<{
    x: number;
    y: number;
    position: string;
  } | null>(null);
  const [isVisible, setIsVisible] = useState(false);

  // Calculate position relative to target element
  const calculatePosition = useCallback(() => {
    if (!(targetElement && popoverRef.current)) return;

    const targetRect = targetElement.getBoundingClientRect();
    const popoverRect = popoverRef.current.getBoundingClientRect();
    const offset = 16;

    let x = targetRect.left + targetRect.width / 2 - popoverRect.width / 2;
    let y = targetRect.bottom + offset;
    let pos = 'bottom';

    // Adjust if popover goes off-screen
    if (x < 16) x = 16;
    if (x + popoverRect.width > window.innerWidth - 16) {
      x = window.innerWidth - popoverRect.width - 16;
    }
    if (y + popoverRect.height > window.innerHeight - 16) {
      y = targetRect.top - popoverRect.height - offset;
      pos = 'top';
    }

    setPosition({ x, y, position: pos });
  }, [targetElement]);

  // Initial positioning and event listeners
  useEffect(() => {
    const timer = setTimeout(() => {
      calculatePosition();
      setIsVisible(true);
    }, 50);

    const handleUpdate = () => calculatePosition();
    window.addEventListener('resize', handleUpdate);
    window.addEventListener('scroll', handleUpdate, { passive: true });

    return () => {
      clearTimeout(timer);
      window.removeEventListener('resize', handleUpdate);
      window.removeEventListener('scroll', handleUpdate);
    };
  }, [calculatePosition]);

  if (!position) {
    return (
      <div
        className="pointer-events-none absolute max-w-sm rounded-lg bg-white p-4 opacity-0 dark:bg-gray-800"
        ref={popoverRef}
        style={{ top: 0, left: 0 }}
      >
        <div className="text-center text-sm">{content.description}</div>
      </div>
    );
  }

  return (
    <div
      className={`pointer-events-auto absolute z-10 max-w-sm rounded-lg border border-gray-200 bg-white p-4 shadow-xl transition-all duration-300 ease-out dark:border-gray-700 dark:bg-gray-800 ${isVisible ? 'scale-100 opacity-100' : 'scale-95 opacity-0'} `}
      ref={popoverRef}
      style={{ left: position.x, top: position.y }}
    >
      {/* Content */}
      <div className="text-center text-gray-700 text-sm leading-relaxed dark:text-gray-300">
        {content.description}
      </div>
    </div>
  );
}

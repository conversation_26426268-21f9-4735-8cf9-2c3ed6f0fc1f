@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.92 0 0);
  --sidebar-accent-foreground: oklch(0.145 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
  --notebook-line: oklch(0.85 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
  --notebook-line: oklch(1 0 0 / 40%);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* INDUSTRY STANDARD 2025: Programmatic OKLCH conversion replaces CSS-based approach */
/* Export functionality now uses JavaScript-based color conversion for better accuracy */

/* Unified thin scrollbar styles */
.thin-scrollbar::-webkit-scrollbar,
.datetime-scrollarea::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

.thin-scrollbar::-webkit-scrollbar-track,
.datetime-scrollarea::-webkit-scrollbar-track {
  background: transparent;
}

.thin-scrollbar::-webkit-scrollbar-thumb,
.datetime-scrollarea::-webkit-scrollbar-thumb {
  background-color: #d1d5db;
  border-radius: 9999px;
}

.thin-scrollbar::-webkit-scrollbar-thumb:hover,
.datetime-scrollarea::-webkit-scrollbar-thumb:hover {
  background-color: #9ca3af;
}

/* For Firefox */
.thin-scrollbar,
.datetime-scrollarea {
  scrollbar-width: thin;
  scrollbar-color: #d1d5db transparent;
}

/* Radix ScrollArea component styling */
.datetime-scrollarea [data-slot="scroll-area-scrollbar"] {
  width: 4px;
}

/* Syntax highlighting styles for code blocks */
/* Light theme (default) */
.hljs {
  display: block;
  overflow-x: auto;
  padding: 1rem;
  background: #f8f9fa;
  color: #24292e;
  border-radius: 0 0 0.5rem 0.5rem;
}

.hljs-comment,
.hljs-quote {
  color: #6a737d;
  font-style: italic;
}

.hljs-keyword,
.hljs-selector-tag,
.hljs-subst {
  color: #d73a49;
  font-weight: bold;
}

.hljs-number,
.hljs-literal,
.hljs-variable,
.hljs-template-variable,
.hljs-tag .hljs-attr {
  color: #005cc5;
}

.hljs-string,
.hljs-doctag {
  color: #032f62;
}

.hljs-title,
.hljs-section,
.hljs-selector-id {
  color: #6f42c1;
  font-weight: bold;
}

.hljs-type,
.hljs-class .hljs-title {
  color: #d73a49;
  font-weight: bold;
}

.hljs-tag,
.hljs-name,
.hljs-attribute {
  color: #22863a;
  font-weight: normal;
}

.hljs-regexp,
.hljs-link {
  color: #032f62;
}

.hljs-symbol,
.hljs-bullet {
  color: #e36209;
}

.hljs-built_in,
.hljs-builtin-name {
  color: #005cc5;
}

.hljs-meta {
  color: #6a737d;
}

.hljs-deletion {
  background: #ffeef0;
}

.hljs-addition {
  background: #f0fff4;
}

.hljs-emphasis {
  font-style: italic;
}

.hljs-strong {
  font-weight: bold;
}

/* Dark theme syntax highlighting */
.dark .hljs {
  background: #1e1e1e;
  color: #d4d4d4;
}

.dark .hljs-comment,
.dark .hljs-quote {
  color: #6a9955;
  font-style: italic;
}

.dark .hljs-keyword,
.dark .hljs-selector-tag,
.dark .hljs-subst {
  color: #569cd6;
  font-weight: bold;
}

.dark .hljs-number,
.dark .hljs-literal,
.dark .hljs-variable,
.dark .hljs-template-variable,
.dark .hljs-tag .hljs-attr {
  color: #b5cea8;
}

.dark .hljs-string,
.dark .hljs-doctag {
  color: #ce9178;
}

.dark .hljs-title,
.dark .hljs-section,
.dark .hljs-selector-id {
  color: #dcdcaa;
  font-weight: bold;
}

.dark .hljs-type,
.dark .hljs-class .hljs-title {
  color: #4ec9b0;
  font-weight: bold;
}

.dark .hljs-tag,
.dark .hljs-name,
.dark .hljs-attribute {
  color: #92c5f8;
  font-weight: normal;
}

.dark .hljs-regexp,
.dark .hljs-link {
  color: #ce9178;
}

.dark .hljs-symbol,
.dark .hljs-bullet {
  color: #d7ba7d;
}

.dark .hljs-built_in,
.dark .hljs-builtin-name {
  color: #4fc1ff;
}

.dark .hljs-meta {
  color: #9cdcfe;
}

.dark .hljs-deletion {
  background: #4b1818;
}

.dark .hljs-addition {
  background: #1b4b1b;
}

.dark .hljs-emphasis {
  font-style: italic;
}

.dark .hljs-strong {
  font-weight: bold;
}

/* Custom scrollbar for code blocks */
.hljs::-webkit-scrollbar {
  height: 8px;
}

.hljs::-webkit-scrollbar-track {
  background: hsl(var(--muted) / 0.1);
  border-radius: 4px;
}

.hljs::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.3);
  border-radius: 4px;
}

.hljs::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.5);
}

.dark .hljs::-webkit-scrollbar-track {
  background: hsl(var(--muted) / 0.1);
}

.dark .hljs::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.3);
}

.dark .hljs::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.5);
}

/* Enhanced thin scrollbar for responsive content */
.thin-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

.thin-scrollbar::-webkit-scrollbar {
  height: 6px;
  width: 6px;
}

.thin-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.thin-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

.thin-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.7);
}

.dark .thin-scrollbar {
  scrollbar-color: rgba(107, 114, 128, 0.5) transparent;
}

.dark .thin-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(107, 114, 128, 0.5);
}

.dark .thin-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(107, 114, 128, 0.7);
}

/* Hidden scrollbar utility - maintains scroll functionality */
.scrollbar-hide {
  /* Hide scrollbar for Chrome, Safari and Opera */
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}

/* Responsive table improvements */
@media (max-width: 640px) {
  .thin-scrollbar {
    scrollbar-width: auto;
  }

  .thin-scrollbar::-webkit-scrollbar {
    height: 8px;
    width: 8px;
  }
}

/* Safe area support for modern mobile devices */
:root {
  --safe-area-inset-top: env(safe-area-inset-top);
  --safe-area-inset-right: env(safe-area-inset-right);
  --safe-area-inset-bottom: env(safe-area-inset-bottom);
  --safe-area-inset-left: env(safe-area-inset-left);
}

/* Mobile-specific chatbot improvements */
@media (max-width: 768px) {
  /* Improve touch targets for mobile */
  .touch-manipulation {
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  /* Safe area padding for mobile devices */
  body {
    padding-top: var(--safe-area-inset-top);
    padding-right: var(--safe-area-inset-right);
    padding-bottom: var(--safe-area-inset-bottom);
    padding-left: var(--safe-area-inset-left);
  }

  /* Better mobile table handling */
  .analytics-table {
    font-size: 0.875rem;
  }

  .analytics-table th,
  .analytics-table td {
    padding: 0.5rem 0.75rem;
    min-width: 80px;
  }

  /* Mobile-optimised prose content */
  .prose {
    font-size: 0.875rem;
    line-height: 1.5;
  }

  .prose h1 {
    font-size: 1.5rem;
  }

  .prose h2 {
    font-size: 1.25rem;
  }

  /* Mobile chart optimizations */
  .recharts-responsive-container {
    min-width: 0;
    width: 100%;
  }

  /* Prevent chart overflow on mobile */
  [data-slot="chart"] {
    overflow: hidden;
    width: 100%;
    min-width: 0;
  }

  /* Mobile-specific chart container */
  .mobile-chart-container {
    width: 100%;
    overflow: hidden;
    padding: 0 8px;
  }

  /* Improve touch targets for mobile charts */
  .recharts-tooltip-wrapper {
    pointer-events: auto;
  }

  .prose h3 {
    font-size: 1.125rem;
  }

  /* Better mobile code block handling */
  .prose pre {
    font-size: 0.75rem;
    padding: 0.75rem;
    margin: 0.75rem 0;
  }

  /* Mobile-friendly list spacing */
  .prose ul,
  .prose ol {
    margin: 0.75rem 0;
  }

  .prose li {
    margin: 0.25rem 0;
  }

  /* Prevent zoom on input focus (iOS Safari) */
  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="number"],
  input[type="tel"],
  input[type="url"],
  input[type="search"],
  textarea,
  select {
    font-size: 16px;
  }

  /* Improve scrolling performance */
  * {
    -webkit-overflow-scrolling: touch;
  }
}

/* Waffle chart animations */
@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: scale(0);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Community Feedback Help Icon Animations */
@keyframes communityFeedbackPulse {
  0%, 100% {
    opacity: 0.7;
    transform: scale(1);
  }
  25% {
    opacity: 0.9;
    transform: scale(1.1);
  }
  50% {
    opacity: 1;
    transform: scale(1.15) rotate(5deg);
  }
  75% {
    opacity: 0.9;
    transform: scale(1.1) rotate(-2deg);
  }
}

@keyframes communityFeedbackGlow {
  0%, 100% {
    filter: drop-shadow(0 0 0px hsl(var(--primary) / 0.3));
  }
  50% {
    filter: drop-shadow(0 0 8px hsl(var(--primary) / 0.6));
  }
}

@keyframes communityFeedbackBounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0) scale(1);
  }
  40% {
    transform: translateY(-4px) scale(1.05);
  }
  60% {
    transform: translateY(-2px) scale(1.02);
  }
}

/* Community feedback animations - optimized for next-theme */
.community-feedback-icon-animated {
  animation:
    communityFeedbackPulse 3s ease-in-out infinite,
    communityFeedbackGlow 2s ease-in-out infinite,
    communityFeedbackBounce 4s ease-in-out infinite;
  animation-delay: 0s, 0.5s, 1s;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.community-feedback-icon-animated:hover {
  animation-play-state: paused;
  transform: scale(1.2) rotate(10deg);
  filter: drop-shadow(0 0 12px hsl(var(--primary) / 0.8));
  color: hsl(var(--primary));
}

/* Notebook textarea styling */
.notebook-textarea {
  line-height: 32px;
  font-size: 16px;
  min-height: 235px;
  background-image: repeating-linear-gradient(
    transparent 0px,
    transparent 31px,
    var(--notebook-line) 31px,
    var(--notebook-line) 32px
  );
  background-position: 0 20px;
  background-attachment: local;
}

/* Enhanced table styles for analytics */
.analytics-table {
  border-spacing: 0;
  border-collapse: separate;
}

.analytics-table th {
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: inherit;
}

.analytics-table th::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #ff6308 0%, #97a4ff 100%);
  opacity: 0.6;
}

.analytics-table tr:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px hsl(var(--foreground) / 0.05);
}

/* Table cell animations */
.analytics-table td {
  transition: all 0.15s ease-in-out;
}

.analytics-table tr:hover td {
  background-color: hsl(var(--muted) / 0.8);
}

/* Zebra striping for better readability */
.analytics-table tbody tr:nth-child(even) {
  background-color: hsl(var(--muted) / 0.3);
}

/* Number formatting in tables */
.analytics-table .number-cell {
  font-variant-numeric: tabular-nums;
  text-align: right;
  font-weight: 500;
}

/* Status indicators in tables */
.analytics-table .status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.analytics-table .status-indicator::before {
  content: "";
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: currentColor;
  opacity: 0.7;
}

/* Table height protection styles */
.analytics-table-container {
  position: relative;
}

/* Sticky header support for scrollable tables */
.analytics-table-container.scrollable .analytics-table th {
  background-color: hsl(var(--background));
  backdrop-filter: blur(8px);
  border-bottom: 2px solid hsl(var(--border));
}

.dark .analytics-table-container.scrollable .analytics-table th {
  background-color: hsl(var(--background));
  border-bottom: 2px solid hsl(var(--border));
}

/* Scroll fade indicators for large tables */
.analytics-table-container.scrollable::before,
.analytics-table-container.scrollable::after {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  height: 8px;
  pointer-events: none;
  z-index: 5;
  transition: opacity 0.2s ease;
}

.analytics-table-container.scrollable::before {
  top: 0;
  background: linear-gradient(
    to bottom,
    hsl(var(--background) / 0.9),
    transparent
  );
}

.analytics-table-container.scrollable::after {
  bottom: 0;
  background: linear-gradient(to top, hsl(var(--background) / 0.9), transparent);
}

.dark .analytics-table-container.scrollable::before {
  background: linear-gradient(
    to bottom,
    hsl(var(--background) / 0.9),
    transparent
  );
}

.dark .analytics-table-container.scrollable::after {
  background: linear-gradient(to top, hsl(var(--background) / 0.9), transparent);
}

/* Enhanced scrollbar for table containers */
.analytics-table-container.scrollable {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.6) transparent;
}

.analytics-table-container.scrollable::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.analytics-table-container.scrollable::-webkit-scrollbar-track {
  background: transparent;
}

.analytics-table-container.scrollable::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.6);
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.analytics-table-container.scrollable::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.8);
}

.dark .analytics-table-container.scrollable::-webkit-scrollbar-thumb {
  background-color: rgba(100, 116, 139, 0.6);
}

.dark .analytics-table-container.scrollable::-webkit-scrollbar-thumb:hover {
  background-color: rgba(100, 116, 139, 0.8);
}

import { getTranslations } from 'next-intl/server';
import { z } from 'zod/v4';

/**
 * Server-side validation schemas for admin settings
 * These schemas can now use translations for better user experience
 * For client-side validation with translations, use the client validation utilities
 */

/**
 * Create church info schema with translations
 */
export async function createChurchInfoSchema(locale: 'en' | 'zh-CN' = 'en') {
  const t = await getTranslations({ locale, namespace: 'validation' });

  return z.object({
    churchName: z
      .string()
      .min(1, { error: t('churchNameRequired') })
      .regex(/^[^0-9]*$/, { error: t('invalidChurchName') }),
    email: z.email({ error: t('invalidEmailFormat') }),
    contactNumber: z
      .string()
      .min(1, { error: t('contactNumberRequired') })
      .length(10, { error: t('contactNumberLength') })
      .regex(/^[0-9]+$/, { error: t('contactNumberDigits') }),
    address: z.string().min(1, { error: t('addressRequired') }),
  });
}

/**
 * Create site URL schema with translations
 */
export async function createSiteUrlSchema(locale: 'en' | 'zh-CN' = 'en') {
  const t = await getTranslations({ locale, namespace: 'validation' });

  return z.object({
    siteUrl: z
      .string()
      .min(1, { error: t('siteUrlRequired') })
      .pipe(z.url({ error: t('invalidUrlFormat') }))
      .refine(
        (url) => url.startsWith('http://') || url.startsWith('https://'),
        {
          error: t('urlMustStartWithProtocol'),
        }
      )
      .refine((url) => !url.endsWith('/'), {
        error: t('urlNoTrailingSlash'),
      }),
  });
}

/**
 * Create homepage announcement schema with translations
 */
export async function createHomepageAnnouncementSchema(
  locale: 'en' | 'zh-CN' = 'en'
) {
  const t = await getTranslations({ locale, namespace: 'validation' });

  return z.object({
    enabled: z.boolean(),
    text: z.string().max(1000),
    type: z.enum(['info', 'warning', 'success', 'destructive']),
  });
}

/**
 * Create rate limit schema with translations
 */
export async function createRateLimitSchema(locale: 'en' | 'zh-CN' = 'en') {
  const t = await getTranslations({ locale, namespace: 'validation' });

  return z
    .object({
      maxAttempts: z.coerce
        .number()
        .int({ error: t('maxAttemptsInteger') })
        .min(1, { error: t('maxAttemptsMinimum') })
        .max(20, { error: t('maxAttemptsMaximum') }),
      lockoutMinutes: z.coerce
        .number()
        .int({ error: t('lockoutDurationInteger') })
        .min(1, { error: t('lockoutDurationMinimum') })
        .max(1440, { error: t('lockoutDurationCannotExceed24H') }),
      escalationMinutes: z.coerce
        .number()
        .int({ error: t('escalationIncrementInteger') })
        .min(0, { error: t('escalationIncrementCannotBeNeg') })
        .max(120, { error: t('escalationIncrementCannotExcee') }),
    })
    .refine(
      (data) => {
        // Validate that escalation doesn't exceed 24 hours
        // Check multiple escalation levels to ensure we don't exceed 24 hours
        const maxLockoutMinutes = 1440; // 24 hours

        // Check escalation levels 0-5 (matching the actual implementation)
        // Level 0 = base lockout, Level 1 = base + 1*increment, etc.
        for (let level = 0; level <= 5; level++) {
          const lockoutDuration =
            data.lockoutMinutes + level * data.escalationMinutes;
          if (lockoutDuration > maxLockoutMinutes) {
            return false; // Validation failed
          }
        }
        return true; // Validation passed
      },
      {
        error: t('escalationSettingsExceed24Hours'),
        path: ['escalationMinutes'],
      }
    );
}

// Type exports for server-side validation
export type ServerChurchInfoFormValues = z.infer<
  Awaited<ReturnType<typeof createChurchInfoSchema>>
>;
export type ServerSiteUrlFormValues = z.infer<
  Awaited<ReturnType<typeof createSiteUrlSchema>>
>;
export type ServerRateLimitFormValues = z.infer<
  Awaited<ReturnType<typeof createRateLimitSchema>>
>;

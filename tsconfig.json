{"compilerOptions": {"lib": ["dom", "dom.iterable", "ES2022", "ES2021.Intl"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "allowArbitraryExtensions": true, "target": "ES2022", "strictNullChecks": true}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "src/global.ts"], "exclude": ["node_modules"]}
/**
 * Cache Module
 *
 * Contains request deduplication caching logic for the chatbot system.
 * This module handles caching of query results to improve performance and reduce
 * redundant database operations.
 *
 * This module provides:
 * - Request deduplication cache with TTL
 * - Memory leak prevention with automatic cleanup
 * - Cache size management with LRU-style eviction
 * - Performance monitoring and debugging support
 */

// Cache configuration constants
export const CACHE_DURATION = 30_000; // 30 seconds
export const MAX_CACHE_SIZE = 100; // Maximum number of cached entries
export const CACHE_CLEANUP_THRESHOLD = 50; // Keep only this many entries when cleaning up

// Cache interface for type safety
interface CacheEntry {
  result: string[];
  timestamp: number;
}

// Request deduplication cache
export const requestCache = new Map<string, CacheEntry>();

/**
 * Generate cache key from user message
 * Normalizes the message for consistent caching
 */
export function generateCacheKey(userMessage: string): string {
  return userMessage.toLowerCase().trim();
}

/**
 * Get cached result if available and not expired
 * Returns null if cache miss or expired entry
 */
export function getCachedResult(userMessage: string): string[] | null {
  const cacheKey = generateCacheKey(userMessage);
  const cached = requestCache.get(cacheKey);

  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    if (process.env.NODE_ENV === 'development') {
      console.log(
        'Returning cached query result for:',
        cacheKey.substring(0, 50)
      );
    }
    return cached.result;
  }

  return null;
}

/**
 * Cache query result with timestamp
 * Automatically triggers cleanup to prevent memory leaks
 */
export function setCachedResult(userMessage: string, result: string[]): void {
  const cacheKey = generateCacheKey(userMessage);

  // Cache the result with current timestamp
  requestCache.set(cacheKey, {
    result: [...result], // Create a copy to avoid reference issues
    timestamp: Date.now(),
  });

  if (process.env.NODE_ENV === 'development') {
    console.log(
      `Cached result for: ${cacheKey.substring(0, 50)} (${result.length} items)`
    );
  }

  // Trigger cleanup to maintain cache health
  cleanupCache();
}

/**
 * Enhanced cache cleanup function with memory leak prevention
 * Removes expired entries and enforces size limits
 */
export function cleanupCache(): void {
  const now = Date.now();
  const keysToDelete: string[] = [];

  // Collect expired keys first to avoid iterator issues
  for (const [key, value] of requestCache.entries()) {
    if (now - value.timestamp > CACHE_DURATION) {
      keysToDelete.push(key);
    }
  }

  // Delete expired entries
  keysToDelete.forEach((key) => {
    const entry = requestCache.get(key);
    if (entry) {
      // Clear any references in the cached result to prevent memory leaks
      if (entry.result && Array.isArray(entry.result)) {
        entry.result.length = 0; // Clear array contents
      }
      requestCache.delete(key);
    }
  });

  // Force cleanup if cache is getting too large (LRU-style eviction)
  if (requestCache.size > MAX_CACHE_SIZE) {
    const entries = Array.from(requestCache.entries());

    // Sort by timestamp and keep only the most recent entries
    entries.sort((a, b) => b[1].timestamp - a[1].timestamp);

    // Clear the cache and repopulate with recent entries
    requestCache.clear();
    entries.slice(0, CACHE_CLEANUP_THRESHOLD).forEach(([key, value]) => {
      requestCache.set(key, value);
    });

    if (process.env.NODE_ENV === 'development') {
      console.log(
        `Cache size limit reached. Cleaned up to ${CACHE_CLEANUP_THRESHOLD} entries.`
      );
    }
  }

  if (process.env.NODE_ENV === 'development' && keysToDelete.length > 0) {
    console.log(
      `Cache cleanup: removed ${keysToDelete.length} expired entries`
    );
  }
}

/**
 * Clear all cached entries
 * Useful for testing or when cache needs to be invalidated
 */
export function clearCache(): void {
  // Clear all entries with proper memory cleanup
  for (const [key, entry] of requestCache.entries()) {
    if (entry.result && Array.isArray(entry.result)) {
      entry.result.length = 0; // Clear array contents
    }
  }

  requestCache.clear();

  if (process.env.NODE_ENV === 'development') {
    console.log('Request cache cleared completely');
  }
}

/**
 * Get cache statistics for monitoring and debugging
 */
export function getCacheStats(): {
  size: number;
  maxSize: number;
  cacheDuration: number;
  oldestEntry?: number;
  newestEntry?: number;
} {
  const now = Date.now();
  let oldestTimestamp = Number.MAX_SAFE_INTEGER;
  let newestTimestamp = 0;

  for (const [, entry] of requestCache.entries()) {
    if (entry.timestamp < oldestTimestamp) {
      oldestTimestamp = entry.timestamp;
    }
    if (entry.timestamp > newestTimestamp) {
      newestTimestamp = entry.timestamp;
    }
  }

  return {
    size: requestCache.size,
    maxSize: MAX_CACHE_SIZE,
    cacheDuration: CACHE_DURATION,
    oldestEntry:
      oldestTimestamp === Number.MAX_SAFE_INTEGER
        ? undefined
        : now - oldestTimestamp,
    newestEntry: newestTimestamp === 0 ? undefined : now - newestTimestamp,
  };
}

/**
 * Check if cache contains a specific key (for testing)
 */
export function hasCachedResult(userMessage: string): boolean {
  const cacheKey = generateCacheKey(userMessage);
  const cached = requestCache.get(cacheKey);
  return cached !== undefined && Date.now() - cached.timestamp < CACHE_DURATION;
}

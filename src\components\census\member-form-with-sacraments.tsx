'use client';

import { Loader2, Save, X } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { memo, useCallback, useMemo, useState } from 'react';
import { useFieldArray, useForm } from 'react-hook-form';
import { SacramentForm } from '@/components/census/sacrament-form';
import { Button } from '@/components/ui/button';
import { DatePicker } from '@/components/ui/date-picker';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { useMessage } from '@/hooks/useMessage';
import { zodResolver } from '@/lib/utils/zod-resolver-compat';
import {
  type ClientCombinedMemberSacramentFormValues,
  type ClientSacramentFormValues,
  createClientCombinedMemberSacramentSchema,
} from '@/lib/validation/client/census-client';

interface SacramentType {
  id: number;
  code: string;
  name: string;
  description?: string;
}

interface MemberFormWithSacramentsProps {
  initialData?: ClientCombinedMemberSacramentFormValues;
  onSubmit: (data: ClientCombinedMemberSacramentFormValues) => Promise<void>;
  onCancel: () => void;
  sacramentTypes: SacramentType[];
  isEdit?: boolean;
}

const MemberFormWithSacramentsComponent = ({
  initialData,
  onSubmit,
  onCancel,
  sacramentTypes,
  isEdit = false,
}: MemberFormWithSacramentsProps) => {
  const t = useTranslations('census');
  const tForms = useTranslations('forms');
  const tCommon = useTranslations('common');

  const tValidation = useTranslations('validation');

  const { showInfo, showError } = useMessage();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Create client-side validation schema with translations
  const clientSchema = createClientCombinedMemberSacramentSchema(tValidation);

  const form = useForm<ClientCombinedMemberSacramentFormValues>({
    resolver: zodResolver(clientSchema),
    defaultValues: initialData || {
      firstName: '',
      lastName: '',
      dateOfBirth: undefined,
      gender: undefined,
      mobilePhone: '',
      hobby: '',
      occupation: '',
      relationship: undefined,
      sacraments: [] as ClientSacramentFormValues[],
    },
  });

  const { fields, append, remove, update } = useFieldArray<
    ClientCombinedMemberSacramentFormValues,
    'sacraments',
    'fieldId'
  >({
    control: form.control,
    name: 'sacraments',
    keyName: 'fieldId',
  });

  const watchedSacramentsRaw = form.watch('sacraments');
  const watchedSacraments = useMemo(
    () => watchedSacramentsRaw || [],
    [watchedSacramentsRaw]
  );

  const uniqueUsedSacramentTypeIds = useMemo(() => {
    const ids = watchedSacraments
      .map((s) => s.sacramentTypeId)
      .filter((id): id is number => id !== undefined && id !== 0);
    return Array.from(new Set(ids));
  }, [watchedSacraments]);

  const handleSacramentFieldChange = useCallback(
    (
      sacramentIndex: number,
      fieldName: keyof ClientSacramentFormValues,
      value: string | number | Date | null
    ) => {
      const fieldItem = fields[sacramentIndex];
      // Destructure and ignore fieldId as it's not needed for the update
      const { fieldId: _, ...rawExistingData } = fieldItem;
      const existingData = rawExistingData as ClientSacramentFormValues;

      // Only update if the value has actually changed
      if (existingData[fieldName] !== value) {
        const updatedItem: ClientSacramentFormValues = {
          ...existingData,
          [fieldName]: value,
        };
        update(sacramentIndex, updatedItem);
      }
    },
    [fields, update]
  );

  const isTypeDisabledInOthersCallback = useCallback(
    (typeIdToCheck: number, currentSacramentIndex: number): boolean => {
      return watchedSacraments.some(
        (sacrament, index) =>
          index !== currentSacramentIndex &&
          sacrament.sacramentTypeId === typeIdToCheck
      );
    },
    [watchedSacraments]
  );

  const addSacrament = useCallback(() => {
    const currentSacramentsCount = fields.length;

    if (currentSacramentsCount >= sacramentTypes.length) {
      showInfo('sacramentLimitReached');
      return;
    }

    if (
      uniqueUsedSacramentTypeIds.length >= sacramentTypes.length &&
      sacramentTypes.length > 0
    ) {
      showInfo('allSacramentsRecorded');
      return;
    }

    append({
      memberId: initialData?.id,
      sacramentTypeId: 0,
      date: null,
      place: '',
    } as ClientSacramentFormValues);
  }, [
    fields.length,
    sacramentTypes.length,
    uniqueUsedSacramentTypeIds.length,
    showInfo,
    append,
    initialData?.id,
  ]);

  const removeSacrament = useCallback(
    (index: number) => {
      remove(index);
    },
    [remove]
  );

  const handleSubmit = useCallback(
    async (data: ClientCombinedMemberSacramentFormValues) => {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30_000); // 30s timeout

      try {
        setIsSubmitting(true);

        // Filter out incomplete sacraments (those without a selected sacrament type)
        const completeSacraments = (data.sacraments || []).filter(
          (s) => s.sacramentTypeId && s.sacramentTypeId !== 0
        );

        const sacramentTypeIdsInForm = completeSacraments.map(
          (s) => s.sacramentTypeId
        );
        const uniqueSacramentTypeIdsInSubmission = new Set(
          sacramentTypeIdsInForm
        );
        if (
          sacramentTypeIdsInForm.length !==
          uniqueSacramentTypeIdsInSubmission.size
        ) {
          showError('DuplicateSacramentTypes');
          return;
        }

        // Submit data with only complete sacraments
        const dataToSubmit = {
          ...data,
          sacraments: completeSacraments,
        };

        await onSubmit(dataToSubmit);
      } catch (error) {
        if (error instanceof Error) {
          if (error.name === 'AbortError') {
            showError('requestTimeout');
          } else if (error.message.includes('network')) {
            showError('networkError');
          } else {
            console.error('Error submitting form:', error);
            showError('UnexpectedError');
          }
        } else {
          console.error('Unknown error submitting form:', error);
          showError('UnexpectedError');
        }
      } finally {
        clearTimeout(timeoutId);
        setIsSubmitting(false);
      }
    },
    [onSubmit, showError]
  );

  return (
    <div>
      <div className="mb-4 flex flex-row items-center justify-between">
        <div>
          <p className="text-muted-foreground text-sm">
            {t('enterMemberDetailsAndSacraments')}
          </p>
        </div>
        <Button onClick={onCancel} size="icon" variant="ghost">
          <X className="h-4 w-4" />
        </Button>
      </div>

      <div>
        <Form {...form}>
          <form
            className="space-y-6"
            onSubmit={form.handleSubmit(handleSubmit)}
          >
            {/* Personal Information Section */}
            <div className="space-y-4">
              <h3 className="font-medium text-md">
                {t('personalInformation')}
              </h3>

              <div className="grid gap-4 sm:grid-cols-2">
                <FormField
                  control={form.control}
                  name="firstName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {tForms('firstName')}
                        <span className="ml-1 text-destructive">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder={tForms('enterFirstNamePlaceholder')}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="lastName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {tForms('lastName')}
                        <span className="ml-1 text-destructive">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder={tForms('enterLastNamePlaceholder')}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid gap-4 sm:grid-cols-2">
                <FormField
                  control={form.control}
                  name="dateOfBirth"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>
                        {tForms('dateOfBirth')}
                        <span className="ml-1 text-destructive">*</span>
                      </FormLabel>
                      <FormControl>
                        <DatePicker
                          date={
                            field.value instanceof Date ? field.value : null
                          }
                          placeholderText={tForms(
                            'selectDateOfBirthPlaceholder'
                          )}
                          preventFutureDates={true}
                          setDate={(date) => field.onChange(date)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="mobilePhone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {tForms('mobilePhone')}
                        <span className="ml-1 text-destructive">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder={tForms('enterMobileNumberPlaceholder')}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Row 1: Hobby + Occupation */}
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div className="col-span-1">
                  <FormField
                    control={form.control}
                    name="hobby"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{tForms('hobby')}</FormLabel>
                        <FormControl>
                          <Input
                            placeholder={tForms('enterHobbyPlaceholder')}
                            {...field}
                            data-tour="edit-form-hobby"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="col-span-1">
                  <FormField
                    control={form.control}
                    name="occupation"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{tForms('occupation')}</FormLabel>
                        <FormControl>
                          <Input
                            placeholder={tForms('enterOccupationPlaceholder')}
                            {...field}
                            data-tour="edit-form-occupation"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Row 2: Gender + Relationship */}
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div className="col-span-1">
                  <FormField
                    control={form.control}
                    name="gender"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {tForms('gender')}
                          <span className="ml-1 text-destructive">*</span>
                        </FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          value={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue
                                placeholder={tForms('selectGenderPlaceholder')}
                              />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="male">
                              {tForms('male')}
                            </SelectItem>
                            <SelectItem value="female">
                              {tForms('female')}
                            </SelectItem>
                            <SelectItem value="other">
                              {tForms('other')}
                            </SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="col-span-1">
                  <FormField
                    control={form.control}
                    name="relationship"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {tForms('relationshipToHousehold')}
                          <span className="ml-1 text-destructive">*</span>
                        </FormLabel>
                        <Select
                          disabled={
                            isEdit && initialData?.relationship === 'head'
                          }
                          onValueChange={field.onChange}
                          value={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue
                                placeholder={tForms(
                                  'selectRelationshipPlaceholder'
                                )}
                              />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {isEdit && initialData?.relationship === 'head' && (
                              <SelectItem value="head">
                                {tForms('head')}
                              </SelectItem>
                            )}
                            <SelectItem value="spouse">
                              {tForms('spouse')}
                            </SelectItem>
                            <SelectItem value="child">
                              {tForms('child')}
                            </SelectItem>
                            <SelectItem value="parent">
                              {tForms('parent')}
                            </SelectItem>
                            <SelectItem value="relative">
                              {tForms('relative')}
                            </SelectItem>
                            <SelectItem value="other">
                              {tForms('other')}
                            </SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </div>

            <Separator />

            {/* Sacraments Section */}
            <div className="space-y-4" data-tour="edit-form-sacraments">
              <div className="mb-4">
                <h3 className="font-medium text-md">{t('sacraments')}</h3>
              </div>

              {fields.length === 0 ? (
                <div className="rounded-lg border border-muted/30 bg-muted/10 py-6 text-center">
                  <div className="flex flex-col items-center gap-2">
                    <div>
                      <p className="font-medium text-muted-foreground">
                        {t('noSacramentsAddedYet')}
                      </p>
                      <p className="mt-1 text-muted-foreground text-sm">
                        {t('useAddSacramentButton')}
                      </p>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  {fields.map((fieldItem, index) => {
                    const { fieldId, ...actualSacramentData } = fieldItem;
                    return (
                      <SacramentForm
                        allUsedSacramentTypeIds={uniqueUsedSacramentTypeIds}
                        control={form.control}
                        errors={form.formState.errors.sacraments?.[index]}
                        index={index}
                        isTypeDisabledInOthers={isTypeDisabledInOthersCallback}
                        key={fieldId}
                        onFieldChange={handleSacramentFieldChange}
                        onRemove={removeSacrament}
                        sacramentData={
                          actualSacramentData as ClientSacramentFormValues
                        }
                        sacramentTypes={sacramentTypes}
                      />
                    );
                  })}
                </div>
              )}
            </div>

            <div className="flex justify-center pt-2">
              <Button
                className="touch-manipulation"
                data-tour="add-sacrament-button"
                disabled={
                  fields.length >= sacramentTypes.length ||
                  (uniqueUsedSacramentTypeIds.length >= sacramentTypes.length &&
                    sacramentTypes.length > 0)
                }
                onClick={addSacrament}
                size="sm"
                type="button"
                variant="outline"
              >
                {t('addSacrament')}
              </Button>
            </div>
          </form>
        </Form>
      </div>

      {/* Primary Action Button - Outside Form Container */}
      <div className="mt-6 flex justify-end">
        <Button
          className="touch-manipulation"
          disabled={isSubmitting}
          onClick={form.handleSubmit(handleSubmit)}
          size="default"
          type="button"
        >
          {isSubmitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              {tCommon('saving')}
            </>
          ) : isEdit ? (
            <>
              <Save className="mr-2 h-4 w-4" />
              {t('updateMember')}
            </>
          ) : (
            <>
              <Save className="mr-2 h-4 w-4" />
              {t('addMember')}
            </>
          )}
        </Button>
      </div>
    </div>
  );
};

// Memoize the component to prevent unnecessary re-renders
export const MemberFormWithSacraments = memo(MemberFormWithSacramentsComponent);

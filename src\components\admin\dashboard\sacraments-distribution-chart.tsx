'use client';

import { Droplets, Hand, Heart, Users, Wheat } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useEffect, useState } from 'react';
import { devLog } from '@/lib/utils';

interface SacramentData {
  sacramentDistribution: {
    baptism: number;
    confirmation: number;
    communion: number;
    matrimony: number;
  };
  totalMembers: number;
}

export function SacramentsDistributionChart() {
  const tAdmin = useTranslations('admin');
  const tSacraments = useTranslations('sacraments');
  const [data, setData] = useState<SacramentData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchSacraments = async () => {
      try {
        const response = await fetch('/api/admin/dashboard/demographics');
        if (response.ok) {
          const sacramentData = await response.json();
          setData({
            sacramentDistribution: sacramentData.sacramentDistribution,
            totalMembers: sacramentData.totalMembers,
          });
        }
      } catch (error) {
        devLog.error('Error fetching sacraments data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchSacraments();
  }, []);

  if (loading || !data) {
    return null;
  }

  const sacraments = [
    {
      name: tSacraments('baptism'),
      count: data.sacramentDistribution.baptism,
      icon: <Droplets className="h-5 w-5" />,
      color: '#97A4FF', // Blue - water, cleansing, rebirth
    },
    {
      name: tSacraments('confirmation'),
      count: data.sacramentDistribution.confirmation,
      icon: <Hand className="h-5 w-5" />,
      color: '#FF6308', // Orange - Holy Spirit, fire, strength
    },
    {
      name: tSacraments('communion'),
      count: data.sacramentDistribution.communion,
      icon: <Wheat className="h-5 w-5" />,
      color: '#F59E0B', // Gold - bread, wheat, body of Christ
    },
    {
      name: tSacraments('matrimony'),
      count: data.sacramentDistribution.matrimony,
      icon: <Heart className="h-5 w-5" />,
      color: '#EC4899', // Rose - love, union, covenant
    },
  ];

  return (
    <div className="relative">
      {/* Background glow effect */}
      <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-[#BDC9E6]/10 via-[#97A4FF]/5 to-[#FF6308]/5 blur-2xl dark:from-[#BDC9E6]/20 dark:via-[#97A4FF]/10 dark:to-[#FF6308]/10" />

      {/* Main card */}
      <div className="relative rounded-2xl border border-white/30 bg-white/80 p-8 shadow-lg shadow-slate-200/30 backdrop-blur-xl dark:border-slate-700/30 dark:bg-slate-800/80 dark:shadow-slate-900/30">
        {/* Card header */}
        <div className="mb-8 space-y-4">
          <div className="flex items-center gap-4">
            <div className="rounded-xl bg-gradient-to-r from-[#97A4FF]/10 to-[#BDC9E6]/10 p-3 dark:from-[#97A4FF]/20 dark:to-[#BDC9E6]/20">
              <div className="rounded-lg bg-gradient-to-r from-[#97A4FF] to-[#BDC9E6] p-2 text-white">
                <Users className="h-6 w-6" />
              </div>
            </div>
            <div className="space-y-1">
              <h2 className="font-light text-2xl text-slate-800 tracking-tight dark:text-slate-100">
                {tAdmin('sacramentsDistribution')}
              </h2>
              <p className="font-medium text-slate-600 dark:text-slate-300">
                {tAdmin('sacramentParticipationOverview')}
              </p>
            </div>
          </div>
        </div>

        {/* Sacraments Grid - Simple items with muted backgrounds */}
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
          {sacraments.map((sacrament) => (
            <div className="group relative" key={sacrament.name}>
              {/* Color-coordinated hover glow effect */}
              <div
                className="absolute inset-0 rounded-xl opacity-0 blur-xl transition-opacity duration-500 group-hover:opacity-10 dark:group-hover:opacity-20"
                style={{ backgroundColor: sacrament.color }}
              />

              {/* Simple sacrament item with muted background */}
              <div
                className="relative rounded-xl bg-slate-50/50 p-4 transition-all duration-300 hover:shadow-lg dark:bg-slate-700/20"
                onMouseEnter={(e) => {
                  e.currentTarget.style.boxShadow =
                    e.currentTarget.style.getPropertyValue('--hover-shadow');
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.boxShadow = '';
                }}
                style={
                  {
                    '--hover-shadow': `0 10px 25px -3px ${sacrament.color}20, 0 4px 6px -2px ${sacrament.color}10`,
                  } as React.CSSProperties & { '--hover-shadow': string }
                }
              >
                <div className="space-y-4">
                  {/* Icon and title */}
                  <div className="flex items-center gap-3">
                    <div
                      className="rounded-lg p-2 text-white"
                      style={{ backgroundColor: sacrament.color }}
                    >
                      {sacrament.icon}
                    </div>
                    <h4 className="font-medium text-base text-slate-700 tracking-wide dark:text-slate-200">
                      {sacrament.name}
                    </h4>
                  </div>

                  {/* Statistics */}
                  <div className="space-y-1">
                    <div className="font-light text-2xl text-slate-800 dark:text-slate-100">
                      {sacrament.count}
                    </div>
                    <div className="font-medium text-slate-600 text-sm dark:text-slate-400">
                      {((sacrament.count / data.totalMembers) * 100).toFixed(1)}
                      {tAdmin('percentOfMembers')}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

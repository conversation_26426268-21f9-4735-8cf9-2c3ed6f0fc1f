/**
 * Rate Limiting Module
 *
 * This module provides rate limiting functionality with LRU cache
 * to prevent abuse and memory leaks.
 */

import { LRUCache } from 'lru-cache';

// Simple rate limiting (with LRU cache to prevent memory leaks)
export const RATE_LIMIT_WINDOW = 60 * 1000; // 1 minute
export const RATE_LIMIT_MAX_REQUESTS = 20; // 20 requests per minute per user

export const rateLimitMap = new LRUCache<
  string,
  { count: number; resetTime: number }
>({
  max: 5000, // tune to memory budget
  ttl: RATE_LIMIT_WINDOW, // automatic eviction
});

// Rate limiting helper (copied from original)
export function checkRateLimit(userId: string): boolean {
  const now = Date.now();

  // Clean up expired entries periodically to prevent memory leaks
  if (rateLimitMap.size > 100) {
    for (const [key, value] of rateLimitMap.entries()) {
      if (now > value.resetTime) {
        rateLimitMap.delete(key);
      }
    }
  }

  const userLimit = rateLimitMap.get(userId);

  if (!userLimit || now > userLimit.resetTime) {
    // Reset or create new limit window
    rateLimitMap.set(userId, {
      count: 1,
      resetTime: now + RATE_LIMIT_WINDOW,
    });
    return true;
  }

  if (userLimit.count >= RATE_LIMIT_MAX_REQUESTS) {
    return false; // Rate limit exceeded
  }

  // Increment count
  userLimit.count++;
  return true;
}

/**
 * Database Import Functions with Prisma
 * Replaces MySQL-based import implementation with Prisma ORM
 */

import type { Prisma } from '@prisma/client';
import { getTranslations } from 'next-intl/server';
import { prisma } from './prisma';

// Types for import operations
export interface ImportValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  tables: string[];
  recordCount: number;
}

export interface ImportResult {
  success: boolean;
  message: string;
  recordCount?: number;
}

// Log import activity using Prisma
export async function logImport(
  adminId: number,
  importType: string,
  success: boolean,
  recordCount: number,
  details: Record<string, unknown>,
  ipAddress: string
): Promise<void> {
  try {
    await prisma.importLog
      ?.create({
        data: {
          adminId,
          fileName: 'database-import.sql',
          importType,
          success,
          recordCount,
          details: JSON.stringify(details),
          ipAddress,
          createdAt: new Date(),
        },
      })
      .catch(() => null); // Ignore if table doesn't exist
  } catch (error) {
    // Environment-aware logging - only in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Failed to log import activity:', error);
    }
    // Don't throw - this is a non-critical operation
  }
}

// Create pre-import backup using Prisma export
export async function createPreImportBackup(adminId: number): Promise<void> {
  try {
    // Export current data as backup
    const backupData = await prisma.$transaction(async (tx) => {
      const [
        members,
        households,
        householdMembers,
        censusYears,
        sacraments,
        sacramentTypes,
        uniqueCodes,
      ] = await Promise.all([
        tx.member.findMany(),
        tx.household.findMany(),
        tx.householdMember.findMany(),
        tx.censusYear.findMany(),
        tx.sacrament.findMany(),
        tx.sacramentType.findMany(),
        tx.uniqueCode.findMany(),
      ]);

      return {
        members,
        households,
        household_members: householdMembers,
        census_years: censusYears,
        sacraments,
        sacrament_types: sacramentTypes,
        unique_codes: uniqueCodes,
        backup_timestamp: new Date().toISOString(),
      };
    });

    // Store backup data (could be saved to file system or backup table)
    await prisma.backupLog
      ?.create({
        data: {
          adminId,
          fileName: 'database-backup.sql',
          backupType: 'pre_import',
          backupData: JSON.stringify(backupData),
          createdAt: new Date(),
        },
      })
      .catch(() => null);
  } catch (error) {
    console.error('Failed to create pre-import backup:', error);
    throw new Error('Failed to create backup before import');
  }
}

// Validate SQL import
export async function validateSQLImport(
  sqlContent: string
): Promise<ImportValidationResult> {
  const errors: string[] = [];
  const warnings: string[] = [];
  const tables: string[] = [];
  let recordCount = 0;

  try {
    // Basic SQL validation
    if (!sqlContent.trim()) {
      errors.push('SQL content is empty');
      return { isValid: false, errors, warnings, tables, recordCount };
    }

    // Check for dangerous SQL operations
    const dangerousPatterns = [
      /DROP\s+DATABASE/i,
      /DROP\s+SCHEMA/i,
      /TRUNCATE\s+TABLE/i,
      /DELETE\s+FROM.*WHERE\s+1\s*=\s*1/i,
    ];

    for (const pattern of dangerousPatterns) {
      if (pattern.test(sqlContent)) {
        errors.push(`Dangerous SQL operation detected: ${pattern.source}`);
      }
    }

    // Extract table names and count INSERT statements
    const insertMatches = sqlContent.match(/INSERT\s+INTO\s+`?(\w+)`?/gi);
    if (insertMatches) {
      insertMatches.forEach((match) => {
        const tableName = match.replace(/INSERT\s+INTO\s+`?(\w+)`?/i, '$1');
        if (!tables.includes(tableName)) {
          tables.push(tableName);
        }
        recordCount++;
      });
    }

    // Validate table names against allowed tables
    const allowedTables = [
      'members',
      'households',
      'household_members',
      'census_years',
      'sacraments',
      'sacrament_types',
      'unique_codes',
      'suburbs',
    ];

    const invalidTables = tables.filter(
      (table) => !allowedTables.includes(table)
    );
    if (invalidTables.length > 0) {
      errors.push(`Invalid tables detected: ${invalidTables.join(', ')}`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      tables,
      recordCount,
    };
  } catch (error) {
    errors.push(
      `SQL validation error: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
    return { isValid: false, errors, warnings, tables, recordCount };
  }
}

// Validate JSON import
export async function validateJSONImport(
  jsonData: Record<string, unknown[]>
): Promise<ImportValidationResult> {
  const errors: string[] = [];
  const warnings: string[] = [];
  const tables = Object.keys(jsonData);
  let recordCount = 0;

  try {
    // Validate table names
    const allowedTables = [
      'members',
      'households',
      'household_members',
      'census_years',
      'sacraments',
      'sacrament_types',
      'unique_codes',
      'suburbs',
    ];

    const invalidTables = tables.filter(
      (table) => !allowedTables.includes(table)
    );
    if (invalidTables.length > 0) {
      errors.push(`Invalid tables detected: ${invalidTables.join(', ')}`);
    }

    // Count records and validate structure
    for (const [tableName, records] of Object.entries(jsonData)) {
      if (!Array.isArray(records)) {
        errors.push(`Table ${tableName} data is not an array`);
        continue;
      }

      recordCount += records.length;

      // Basic structure validation for key tables
      if (tableName === 'members' && records.length > 0) {
        const requiredFields = ['firstName', 'lastName', 'gender'];
        const firstRecord = records[0];
        const missingFields = requiredFields.filter(
          (field) => !(field in (firstRecord as Record<string, unknown>))
        );
        if (missingFields.length > 0) {
          warnings.push(
            `Members table missing recommended fields: ${missingFields.join(', ')}`
          );
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      tables,
      recordCount,
    };
  } catch (error) {
    errors.push(
      `JSON validation error: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
    return { isValid: false, errors, warnings, tables, recordCount };
  }
}

// Validate CSV import
export async function validateCSVImport(
  csvFiles: Record<string, string>
): Promise<ImportValidationResult> {
  const errors: string[] = [];
  const warnings: string[] = [];
  const tables = Object.keys(csvFiles);
  let recordCount = 0;

  try {
    // Validate table names
    const allowedTables = [
      'members',
      'households',
      'household_members',
      'census_years',
      'sacraments',
      'sacrament_types',
      'unique_codes',
      'suburbs',
    ];

    const invalidTables = tables.filter(
      (table) => !allowedTables.includes(table)
    );
    if (invalidTables.length > 0) {
      errors.push(`Invalid tables detected: ${invalidTables.join(', ')}`);
    }

    // Validate CSV structure
    for (const [tableName, csvContent] of Object.entries(csvFiles)) {
      const lines = csvContent.trim().split('\n');
      if (lines.length < 2) {
        warnings.push(`Table ${tableName} has no data rows`);
        continue;
      }

      recordCount += lines.length - 1; // Subtract header row

      // Basic CSV validation
      const header = lines[0];
      if (!header.includes(',')) {
        warnings.push(`Table ${tableName} may not be properly formatted CSV`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      tables,
      recordCount,
    };
  } catch (error) {
    errors.push(
      `CSV validation error: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
    return { isValid: false, errors, warnings, tables, recordCount };
  }
}

// Import SQL (simplified for Prisma)
export async function importSQL(): Promise<ImportResult> {
  try {
    // Note: Prisma doesn't support raw SQL imports directly
    // This would need to be implemented using raw queries or manual parsing
    // For now, return a placeholder implementation

    const t = await getTranslations({ locale: 'en', namespace: 'common' });

    return {
      success: false,
      message: t('sqlImportNotYetImplementedWith'),
    };
  } catch (error) {
    return {
      success: false,
      message: `SQL import failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
    };
  }
}

// Import JSON using Prisma
export async function importJSON(
  jsonData: Record<string, unknown[]>
): Promise<ImportResult> {
  try {
    let totalRecords = 0;

    await prisma.$transaction(async (tx) => {
      // Import in dependency order
      const importOrder = [
        'census_years',
        'sacrament_types',
        'suburbs',
        'households',
        'members',
        'household_members',
        'sacraments',
        'unique_codes',
      ];

      for (const tableName of importOrder) {
        if (jsonData[tableName] && jsonData[tableName].length > 0) {
          const records = jsonData[tableName];
          totalRecords += records.length;

          // Import based on table type
          switch (tableName) {
            case 'members':
              await tx.member.createMany({
                data: records as Prisma.MemberCreateManyInput[],
                skipDuplicates: true,
              });
              break;
            case 'households':
              await tx.household.createMany({
                data: records as Prisma.HouseholdCreateManyInput[],
                skipDuplicates: true,
              });
              break;
            case 'household_members':
              await tx.householdMember.createMany({
                data: records as Prisma.HouseholdMemberCreateManyInput[],
                skipDuplicates: true,
              });
              break;
            case 'census_years':
              await tx.censusYear.createMany({
                data: records as Prisma.CensusYearCreateManyInput[],
                skipDuplicates: true,
              });
              break;
            case 'sacraments':
              await tx.sacrament.createMany({
                data: records as Prisma.SacramentCreateManyInput[],
                skipDuplicates: true,
              });
              break;
            case 'sacrament_types':
              await tx.sacramentType.createMany({
                data: records as Prisma.SacramentTypeCreateManyInput[],
                skipDuplicates: true,
              });
              break;
            case 'unique_codes':
              await tx.uniqueCode.createMany({
                data: records as Prisma.UniqueCodeCreateManyInput[],
                skipDuplicates: true,
              });
              break;
            case 'suburbs':
              await tx.suburb
                ?.createMany({
                  data: records as Prisma.SuburbCreateManyInput[],
                  skipDuplicates: true,
                })
                .catch(() => null);
              break;
          }
        }
      }
    });

    return {
      success: true,
      message: `Successfully imported ${totalRecords} records`,
      recordCount: totalRecords,
    };
  } catch (error) {
    return {
      success: false,
      message: `JSON import failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
    };
  }
}

// Import CSV using Prisma
export async function importCSV(
  csvFiles: Record<string, string>
): Promise<ImportResult> {
  try {
    // CSV import would require parsing CSV content and converting to JSON
    // Then using the JSON import function

    const jsonData: Record<string, unknown[]> = {};

    for (const [tableName, csvContent] of Object.entries(csvFiles)) {
      const lines = csvContent.trim().split('\n');
      if (lines.length < 2) continue;

      const headers = lines[0]
        .split(',')
        .map((h) => h.trim().replace(/"/g, ''));
      const records = [];

      for (let i = 1; i < lines.length; i++) {
        const values = lines[i]
          .split(',')
          .map((v) => v.trim().replace(/"/g, ''));
        const record: Record<string, unknown> = {};

        headers.forEach((header, index) => {
          record[header] = values[index] || null;
        });

        records.push(record);
      }

      jsonData[tableName] = records;
    }

    return await importJSON(jsonData);
  } catch (error) {
    return {
      success: false,
      message: `CSV import failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
    };
  }
}

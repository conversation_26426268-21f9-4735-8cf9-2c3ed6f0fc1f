# Database Backups

This directory contains database backups created by the backup script.

## Automatic Backups

Backups are automatically created when:

1. An administrator exports the database from the admin panel
2. Before a database import operation is performed (pre-import backup)
3. When the `npm run db:backup` script is executed

## Backup Retention

By default, the system keeps the 10 most recent backups and automatically removes older ones.

## Manual Backup

To create a manual backup, run:

```bash
npm run db:backup
```

## Restoring from Backup

To restore from a backup, you can:

1. Use the Import functionality in the admin panel
2. Or manually restore using the MySQL command line:

```bash
mysql -u username -p database_name < backup_file.sql
```

Replace `username` and `database_name` with your database credentials.

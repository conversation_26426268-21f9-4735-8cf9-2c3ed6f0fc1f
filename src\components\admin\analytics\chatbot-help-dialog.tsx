'use client';

import {
  Database,
  HelpCircle,
  Home,
  Lightbulb,
  Scroll,
  Search,
  Users,
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useEffect, useMemo, useState } from 'react';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Drawer,
  DrawerContent,
  DrawerHeader,
  DrawerTitle,
} from '@/components/ui/drawer';
import { Separator } from '@/components/ui/separator';

interface ExampleQuery {
  category: 'members' | 'households' | 'unique_codes';
  query: string;
  description: string;
  icon: React.ReactNode;
  color: string;
}

// Define the queries with translation keys
const getExampleQueries = (t: any): ExampleQuery[] => [
  // Members queries
  {
    category: 'members',
    query: t('quickActionMemberCount'),
    description: t('getTotalMemberCount'),
    icon: <Users className="h-3 w-3" />,
    color: 'bg-[#3B82F6]/10 text-[#3B82F6] border-[#3B82F6]/20',
  },
  {
    category: 'members',
    query: t('queryMembersGenderDistribution'),
    description: t('analyseMemberDemographics'),
    icon: <Users className="h-3 w-3" />,
    color: 'bg-[#3B82F6]/10 text-[#3B82F6] border-[#3B82F6]/20',
  },
  {
    category: 'members',
    query: t('queryMembersBornAfter2000'),
    description: t('filterByAgeCriteria'),
    icon: <Users className="h-3 w-3" />,
    color: 'bg-[#3B82F6]/10 text-[#3B82F6] border-[#3B82F6]/20',
  },
  {
    category: 'members',
    query: t('queryMembersAverageAge'),
    description: t('ageStatistics'),
    icon: <Users className="h-3 w-3" />,
    color: 'bg-[#3B82F6]/10 text-[#3B82F6] border-[#3B82F6]/20',
  },
  {
    category: 'members',
    query: t('queryMembersByRelationship'),
    description: t('familyRelationshipAnalysis'),
    icon: <Users className="h-3 w-3" />,
    color: 'bg-[#3B82F6]/10 text-[#3B82F6] border-[#3B82F6]/20',
  },

  // Households queries
  {
    category: 'households',
    query: t('queryHouseholdsBySuburb'),
    description: t('geographicDistribution'),
    icon: <Home className="h-3 w-3" />,
    color: 'bg-[#10B981]/10 text-[#10B981] border-[#10B981]/20',
  },
  {
    category: 'households',
    query: t('queryHouseholdsLargeFamilies'),
    description: t('largeFamilyAnalysis'),
    icon: <Home className="h-3 w-3" />,
    color: 'bg-[#10B981]/10 text-[#10B981] border-[#10B981]/20',
  },
  {
    category: 'households',
    query: t('queryHouseholdsTopSuburbs'),
    description: t('topSuburbsByHouseholdCount'),
    icon: <Home className="h-3 w-3" />,
    color: 'bg-[#10B981]/10 text-[#10B981] border-[#10B981]/20',
  },
  {
    category: 'households',
    query: t('queryHouseholdsByCensusYear'),
    description: t('censusYearBreakdown'),
    icon: <Home className="h-3 w-3" />,
    color: 'bg-[#10B981]/10 text-[#10B981] border-[#10B981]/20',
  },
  {
    category: 'households',
    query: t('queryHouseholdsSizeDistribution'),
    description: t('familySizeAnalysis'),
    icon: <Home className="h-3 w-3" />,
    color: 'bg-[#10B981]/10 text-[#10B981] border-[#10B981]/20',
  },

  // Unique codes queries
  {
    category: 'unique_codes',
    query: t('quickActionCodeStatus'),
    description: t('checkCodeAvailability'),
    icon: <Scroll className="h-3 w-3" />,
    color: 'bg-[#FF6308]/10 text-[#FF6308] border-[#FF6308]/20',
  },
  {
    category: 'unique_codes',
    query: t('queryCodesAssignmentStatus'),
    description: t('codeAssignmentStatus'),
    icon: <Scroll className="h-3 w-3" />,
    color: 'bg-[#FF6308]/10 text-[#FF6308] border-[#FF6308]/20',
  },
  {
    category: 'unique_codes',
    query: t('queryCodesByCensusYear'),
    description: t('censusYearBreakdown'),
    icon: <Scroll className="h-3 w-3" />,
    color: 'bg-[#FF6308]/10 text-[#FF6308] border-[#FF6308]/20',
  },
  {
    category: 'unique_codes',
    query: t('queryCodesLinkedToHouseholds'),
    description: t('codeUsageAnalysis'),
    icon: <Scroll className="h-3 w-3" />,
    color: 'bg-[#FF6308]/10 text-[#FF6308] border-[#FF6308]/20',
  },
  {
    category: 'unique_codes',
    query: t('queryCodesAssignmentTrends'),
    description: t('assignmentPatterns'),
    icon: <Scroll className="h-3 w-3" />,
    color: 'bg-[#FF6308]/10 text-[#FF6308] border-[#FF6308]/20',
  },
];

interface ChatbotHelpDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onQuerySelect: (query: string) => void;
}

export function ChatbotHelpDialog({
  open,
  onOpenChange,
  onQuerySelect,
}: ChatbotHelpDialogProps) {
  const t = useTranslations('admin');
  const tCommon = useTranslations('common');
  const [isDesktop, setIsDesktop] = useState(false);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
    const checkIsDesktop = () => setIsDesktop(window.innerWidth >= 768);
    checkIsDesktop();
    window.addEventListener('resize', checkIsDesktop);
    return () => window.removeEventListener('resize', checkIsDesktop);
  }, []);

  const handleQuerySelect = (query: string) => {
    onQuerySelect(query);
    onOpenChange(false);
  };

  const exampleQueries = useMemo(() => getExampleQueries(tCommon), [tCommon]);

  const groupedQueries = useMemo(() => {
    return exampleQueries.reduce(
      (acc, query) => {
        if (!acc[query.category]) {
          acc[query.category] = [];
        }
        acc[query.category].push(query);
        return acc;
      },
      {} as Record<string, ExampleQuery[]>
    );
  }, [exampleQueries]);

  const categoryConfig = {
    members: {
      title: t('chatbotMemberCount'),
      icon: <Users className="h-4 w-4" />,
      color: '#3B82F6',
    },
    households: {
      title: t('chatbotHouseholdStats'),
      icon: <Home className="h-4 w-4" />,
      color: '#10B981',
    },
    unique_codes: {
      title: t('chatbotCodeStatus'),
      icon: <Scroll className="h-4 w-4" />,
      color: '#FF6308',
    },
  };

  const content = (
    <div className="flex h-full flex-col">
      <div className="flex-1 space-y-6">
        {Object.entries(groupedQueries).map(([category, queries]) => {
          const config =
            categoryConfig[category as keyof typeof categoryConfig];
          return (
            <div key={category}>
              <div className="mb-3 flex items-center gap-2">
                <div
                  className="flex h-6 w-6 items-center justify-center rounded-md"
                  style={{
                    backgroundColor: `${config.color}20`,
                    color: config.color,
                  }}
                >
                  {config.icon}
                </div>
                <h3 className="font-medium text-foreground">{config.title}</h3>
              </div>

              <div className="ml-4 space-y-2 sm:ml-8">
                {queries.map((example, index) => (
                  <div
                    aria-label={`Select query: ${example.query}`}
                    className="group cursor-pointer touch-manipulation rounded-lg border border-border p-3 transition-all duration-200 hover:border-border/80 hover:bg-muted/50 focus:outline-none focus:ring-2 focus:ring-[#97A4FF] focus:ring-offset-2"
                    key={index}
                    onClick={() => handleQuerySelect(example.query)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        handleQuerySelect(example.query);
                      }
                    }}
                    role="button"
                    tabIndex={0}
                  >
                    <div className="flex items-start justify-between gap-2 sm:gap-3">
                      <div className="min-w-0 flex-1">
                        <div className="mb-1 flex items-center gap-2">
                          <Search className="h-3 w-3 shrink-0 text-muted-foreground" />
                          <span className="break-words font-medium text-foreground text-sm transition-colors group-hover:text-[#FF6308]">
                            {example.query}
                          </span>
                        </div>
                        <p className="ml-5 break-words text-muted-foreground text-xs">
                          {example.description}
                        </p>
                      </div>
                      <Badge
                        className={`${example.color} shrink-0 text-xs`}
                        variant="outline"
                      >
                        {example.icon}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          );
        })}

        <Separator />

        {/* Tips Section */}
        <div>
          <div className="mb-3 flex items-center gap-2">
            <Lightbulb className="h-4 w-4 text-[#FF6308]" />
            <h3 className="font-medium text-foreground">{t('chatbotTips')}</h3>
          </div>

          <div className="space-y-2 text-muted-foreground text-sm">
            <div className="flex items-start gap-2">
              <div className="mt-2 h-1 w-1 shrink-0 rounded-full bg-[#97A4FF]" />
              <span>{tCommon('askQuestionsInNaturalLanguageA')}</span>
            </div>
            <div className="flex items-start gap-2">
              <div className="mt-2 h-1 w-1 shrink-0 rounded-full bg-[#97A4FF]" />
              <span>{tCommon('useSpecificCriteriaLikeAgeRang')}</span>
            </div>
            <div className="flex items-start gap-2">
              <div className="mt-2 h-1 w-1 shrink-0 rounded-full bg-[#97A4FF]" />
              <span>{tCommon('requestComparisonsTrendsAndSta')}</span>
            </div>
            <div className="flex items-start gap-2">
              <div className="mt-2 h-1 w-1 shrink-0 rounded-full bg-[#97A4FF]" />
              <span>{tCommon('theAiWillOnlyAnswerQuestionsAb')}</span>
            </div>
          </div>
        </div>

        {/* Database Schema Info */}
        <div>
          <div className="mb-3 flex items-center gap-2">
            <Database className="h-4 w-4 text-[#10B981]" />
            <h3 className="font-medium text-foreground">
              {t('chatbotAvailableData')}
            </h3>
          </div>

          <div className="grid grid-cols-1 gap-2 text-xs">
            <div className="rounded border border-border p-3 sm:p-2">
              <div className="mb-1 font-medium text-[#3B82F6]">
                {t('chatbotMembersCategory')}
              </div>
              <div className="break-words text-muted-foreground">
                {t('chatbotPersonalInfoDesc')}
              </div>
            </div>
            <div className="rounded border border-border p-3 sm:p-2">
              <div className="mb-1 font-medium text-[#10B981]">
                {t('chatbotHouseholdsCategory')}
              </div>
              <div className="break-words text-muted-foreground">
                {t('chatbotLocationDesc')}
              </div>
            </div>
            <div className="rounded border border-border p-3 sm:p-2">
              <div className="mb-1 font-medium text-[#FF6308]">
                {t('chatbotUniqueCodesCategory')}
              </div>
              <div className="break-words text-muted-foreground">
                {t('chatbotLoginCodesDesc')}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  // Prevent hydration mismatch by not rendering until mounted
  if (!isMounted) {
    return null;
  }

  if (isDesktop) {
    return (
      <Dialog onOpenChange={onOpenChange} open={open}>
        <DialogContent
          className="flex flex-col"
          style={{
            height: '80dvh',
            width: '80dvw',
            maxWidth: '1600px',
            minWidth: '320px',
          }}
        >
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <HelpCircle className="h-4 w-4 text-[#97A4FF]" />
              {t('queryExamples')}
            </DialogTitle>
          </DialogHeader>
          <div className="thin-scrollbar flex-1 overflow-y-auto pr-3">
            {content}
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer onOpenChange={onOpenChange} open={open}>
      <DrawerContent className="flex max-h-[80vh] min-h-[60vh] flex-col">
        <div className="thin-scrollbar flex-1 overflow-y-auto px-4 pb-6">
          <div className="pb-2 pt-4">
            <h2 className="flex items-center gap-2 font-semibold text-lg">
              <HelpCircle className="h-4 w-4 text-[#97A4FF]" />
              {t('queryExamples')}
            </h2>
          </div>
          {content}
        </div>
      </DrawerContent>
    </Drawer>
  );
}

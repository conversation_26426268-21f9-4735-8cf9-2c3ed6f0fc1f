'use client';

import { CheckCircle, ChevronRight, Circle, MousePointer2 } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { memo, useCallback, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { useCensusProgress } from '@/hooks/use-census-progress';
import { useCensusTour } from '@/hooks/use-census-tour';
import type {
  INextStepItem,
  INextStepsSectionProps,
} from '@/types/welcome-modal';

// Static data moved outside component for performance
const NEXT_STEPS_DATA: INextStepItem[] = [
  {
    id: 'household-registration',
    titleKey: 'householdRegistrationTitle',
    descriptionKey: 'householdRegistrationDescription',
    isOptional: false, // Required - auto-completed after login
  },
  {
    id: 'hobby-fields',
    titleKey: 'hobbyFieldsTitle',
    descriptionKey: 'hobbyFieldsDescription',
    isOptional: true, // Optional field
  },
  {
    id: 'occupation',
    titleKey: 'occupationTitle',
    descriptionKey: 'occupationDescription',
    isOptional: true, // Optional field
  },
  {
    id: 'sacraments',
    titleKey: 'sacramentsTitle',
    descriptionKey: 'sacramentsDescription',
    isOptional: true, // Optional field
  },
  {
    id: 'household-members',
    titleKey: 'householdMembersTitle',
    descriptionKey: 'householdMembersDescription',
    isOptional: false, // Required for complete household census
  },
  {
    id: 'community-feedback',
    titleKey: 'communityFeedbackTitle',
    descriptionKey: 'communityFeedbackDescription',
    isOptional: false, // Required for community engagement
  },
];

/**
 * NextStepsSection component displays progressive action items for census completion
 * Features:
 * - Real-time completion tracking with progress indicators
 * - Clickable steps that navigate to functionality
 * - Vertical step layout with completion status
 * - Smart button behavior (Get Started vs Continue Progress vs Skip)
 * Optimized with React.memo and useMemo for performance
 */
export const NextStepsSection = memo(function NextStepsSection({
  onGetStarted,
}: INextStepsSectionProps) {
  const t = useTranslations('onboarding');

  // Progress tracking and tour functionality
  const { completionState } = useCensusProgress();
  const { startTour } = useCensusTour();

  // Memoize static data to prevent re-creation on every render
  const nextSteps = useMemo(() => NEXT_STEPS_DATA, []);

  // Calculate step completion status
  const getStepCompletion = useMemo(() => {
    return {
      'household-registration': completionState.householdRegistration,
      'hobby-fields': completionState.hobbies,
      occupation: completionState.occupation,
      sacraments: completionState.sacraments,
      'household-members': completionState.familyMembers,
      'community-feedback': completionState.communityFeedback,
    };
  }, [completionState]);

  // Handle step-specific navigation
  const handleStepNavigation = useCallback(
    (stepId: string) => {
      switch (stepId) {
        case 'household-registration':
          // Already completed, just show success message
          console.log('Household registration already completed');
          break;

        case 'hobby-fields':
          // Context-aware hobby tour
          startTour('hobby-fields');
          break;

        case 'occupation':
          // Context-aware occupation tour (same pattern as hobby fields)
          startTour('occupation-fields');
          break;

        case 'sacraments':
          // Context-aware sacraments tour
          startTour('sacraments');
          break;

        case 'household-members':
          // For family members, highlight the add member button
          startTour('add-member');
          break;

        case 'community-feedback':
          // Highlight the community feedback card
          startTour('community-feedback');
          break;

        default:
          console.warn(`Unknown step ID: ${stepId}`);
          break;
      }
    },
    [startTour]
  );

  return (
    <div className="space-y-6">
      {/* Next Steps Title */}
      <div className="text-center">
        <h3 className="mb-4 font-semibold text-foreground text-lg">
          {t('nextStepsTitle')}
        </h3>
      </div>

      {/* Clickable Instructions */}
      <div className="flex justify-center">
        <div className="inline-flex items-center gap-2 rounded-lg border border-accent/50 bg-accent/30 px-4 py-2">
          <MousePointer2 className="h-4 w-4 text-accent-foreground/70" />
          <p className="text-accent-foreground/80 text-xs">
            {t('welcomeDescription')}
          </p>
        </div>
      </div>

      {/* Progressive Steps List */}
      <div className="space-y-3">
        {nextSteps.map((step, index) => {
          const isCompleted =
            getStepCompletion[step.id as keyof typeof getStepCompletion];
          const isClickable = !isCompleted; // Only incomplete steps are clickable

          // Dynamic styling based on completion status
          const getStepStyles = () => {
            if (isCompleted) {
              return {
                container: 'bg-green-50 opacity-75', // No hover effect for completed
                title: 'text-green-800',
                description: 'text-green-600',
              };
            }
            return {
              container:
                'bg-card/50 hover:bg-card hover:shadow-md hover:scale-[1.02] active:scale-[0.98]',
              title: 'text-foreground',
              description: 'text-muted-foreground',
            };
          };

          const styles = getStepStyles();

          return (
            <div
              className={`group flex items-center gap-3 rounded-lg p-4 transition-all duration-300 ease-out ${styles.container} ${
                isClickable ? 'cursor-pointer select-none' : 'cursor-default'
              }`}
              key={step.id}
              onClick={
                isClickable
                  ? (e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      console.log(`Step clicked: ${step.id}`);

                      // Close modal first
                      onGetStarted();

                      // Navigate to specific section based on step type
                      setTimeout(() => {
                        handleStepNavigation(step.id);
                      }, 800);
                    }
                  : undefined
              }
              role={isClickable ? 'button' : undefined}
              tabIndex={isClickable ? 0 : undefined}
            >
              {/* Step Number and Completion Indicator */}
              <div className="relative flex flex-shrink-0 items-center gap-3">
                {/* Vertical connecting line - only show if not the last step */}
                {index < nextSteps.length - 1 && (
                  <div
                    className={`absolute top-8 left-4 w-px ${
                      isCompleted ? 'bg-green-300' : 'bg-border/50'
                    }`}
                    style={{ height: 'calc(100% + 2.75rem)' }}
                  />
                )}

                <div
                  className={`relative z-10 flex h-8 w-8 items-center justify-center rounded-full font-medium text-sm ${
                    isCompleted
                      ? 'border-2 border-green-200 bg-green-100 text-green-700'
                      : 'bg-muted text-muted-foreground'
                  }`}
                >
                  {index + 1}
                </div>
                {isCompleted ? (
                  <CheckCircle className="h-5 w-5 text-green-500" />
                ) : (
                  <Circle className="h-5 w-5 text-muted-foreground" />
                )}
              </div>

              {/* Content */}
              <div className="min-w-0 flex-1">
                <div className="mb-1 flex items-center gap-2">
                  <h4 className={`font-medium text-sm ${styles.title}`}>
                    {t(step.titleKey as any)}
                  </h4>
                  {/* Hide optional badge for hobby, occupation, and sacraments steps */}
                  {step.isOptional &&
                    !['hobby-fields', 'occupation', 'sacraments'].includes(
                      step.id
                    ) && (
                      <span className="rounded-full bg-muted px-2 py-0.5 text-muted-foreground text-xs">
                        Optional
                      </span>
                    )}
                </div>
                <p className={`text-xs leading-relaxed ${styles.description}`}>
                  {t(step.descriptionKey as any)}
                </p>
              </div>

              {/* Clickable indicator for incomplete steps */}
              {isClickable && (
                <div className="flex-shrink-0">
                  <ChevronRight className="h-4 w-4 text-muted-foreground/60 transition-transform duration-200 group-hover:translate-x-1" />
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Simple Action Button */}
      <div className="flex flex-col gap-3 pt-6 pb-4">
        <div className="flex justify-center">
          <Button
            className="min-h-[44px] touch-manipulation px-6 py-3 font-medium transition-all duration-200"
            onClick={onGetStarted}
            variant="outline"
          >
            {t('skipForNow')}
          </Button>
        </div>
      </div>
    </div>
  );
});

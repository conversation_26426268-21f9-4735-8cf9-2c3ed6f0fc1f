import { NextResponse } from 'next/server';
import { getTranslations } from 'next-intl/server';
import { authErrorKeys } from '@/lib/errors/auth-errors';
import { censusErrorKeys } from '@/lib/errors/census-errors';
import { successMessageKeys } from '@/lib/messages/success-messages';
import { getLocaleFromCookies } from '@/lib/utils/server-messages';

/**
 * Detect auth context from request URL
 * @param request - The incoming request
 * @returns 'admin' | 'census'
 */
function getAuthContextFromRequest(request: Request): 'admin' | 'census' {
  const url = new URL(request.url);
  return url.pathname.includes('/api/admin') ? 'admin' : 'census';
}

/**
 * Create standardised success response with translation
 * @param request - The incoming request
 * @param messageKey - Success message key
 * @param data - Optional response data
 */
export async function createSuccessResponse(
  request: Request,
  messageKey: string,
  data?: any
) {
  const locale = await getLocaleFromCookies();
  const authContext = getAuthContextFromRequest(request);

  // Get translated message
  const t = await getTranslations({ locale, namespace: 'notifications' });
  const translationKey =
    successMessageKeys[messageKey] || successMessageKeys.default;
  const message = t(translationKey as any);

  return NextResponse.json({
    success: true,
    message,
    data,
    _context: authContext, // For debugging/logging
  });
}

/**
 * Create standardised error response with translation
 * @param request - The incoming request
 * @param errorKey - Error key
 * @param status - HTTP status code
 * @param details - Optional error details
 */
export async function createErrorResponse(
  request: Request,
  errorKey: string,
  status = 500,
  details?: any
) {
  const locale = await getLocaleFromCookies();
  const authContext = getAuthContextFromRequest(request);

  // Determine error key mapping based on context
  const errorKeys = authContext === 'admin' ? authErrorKeys : censusErrorKeys;
  const translationKey = errorKeys[errorKey] || errorKeys.default;

  // Get translated message
  const namespace = authContext === 'admin' ? 'auth' : 'auth'; // Both use auth namespace
  const t = await getTranslations({ locale, namespace });
  const message = t(translationKey as any);

  return NextResponse.json(
    {
      success: false,
      error: message,
      ...(details && { details }),
      _context: authContext,
    },
    { status }
  );
}

/**
 * Create standardised warning response with translation
 * @param request - The incoming request
 * @param messageKey - Warning message key
 * @param data - Optional response data
 */
export async function createWarningResponse(
  request: Request,
  messageKey: string,
  data?: any
) {
  const locale = await getLocaleFromCookies();
  const authContext = getAuthContextFromRequest(request);

  // Get translated message
  const t = await getTranslations({ locale, namespace: 'warnings' });
  const message = t(messageKey as any);

  return NextResponse.json({
    success: true,
    warning: message,
    data,
    _context: authContext,
  });
}

/**
 * Create standardised info response with translation
 * @param request - The incoming request
 * @param messageKey - Info message key
 * @param data - Optional response data
 */
export async function createInfoResponse(
  request: Request,
  messageKey: string,
  data?: any
) {
  const locale = await getLocaleFromCookies();
  const authContext = getAuthContextFromRequest(request);

  // Get translated message
  const t = await getTranslations({ locale, namespace: 'common' });
  const message = t(messageKey as any);

  return NextResponse.json({
    success: true,
    info: message,
    data,
    _context: authContext,
  });
}

import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { getTranslations } from 'next-intl/server';
import { z } from 'zod/v4';
import { authOptions } from '@/lib/auth/auth-options';
import {
  getCensusYearSettings,
  syncCurrentCensusYear,
} from '@/lib/db/census-year-settings';
import { getCensusYearByYear } from '@/lib/db/census-years';
import {
  getErrorMessage,
  getZodErrorDetails,
  isZodError,
} from '@/lib/utils/error-handling';
import { getLocaleFromCookies } from '@/lib/utils/server-messages';

// Schema for validating census year update requests
const censusYearSchema = (lastCensusYear: string) =>
  z.object({
    currentCensusYear: z
      .string()
      .min(1, 'Current census year is required')
      .refine(
        (val) => !Number.isNaN(Number.parseInt(val, 10)),
        'Must be a valid year'
      )
      .refine(
        (val) =>
          Number.parseInt(val, 10) >= 2000 && Number.parseInt(val, 10) <= 2100,
        'Year must be between 2000 and 2100'
      )
      .refine(
        (val) => Number.parseInt(val, 10) > Number.parseInt(lastCensusYear, 10),
        `Current census year must be greater than the last census year (${lastCensusYear})`
      ),
  });

export async function GET(_request: Request) {
  // Get locale from centralized utility at the beginning
  const locale = await getLocaleFromCookies();
  const tAdmin = await getTranslations({ locale, namespace: 'admin' });

  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Require authentication for accessing settings
    if (!session || session.user.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    try {
      // Try to get census year settings from database
      const censusYearSettings = await getCensusYearSettings();
      return NextResponse.json(censusYearSettings);
    } catch (_dbError) {
      // Return default values for development
      return NextResponse.json({
        currentCensusYear: '2023',
        lastCensusYear: '2022',
      });
    }
  } catch (error) {
    return NextResponse.json(
      {
        error: tAdmin('censusYearFetchFailed'),
        details: getErrorMessage(error),
      },
      { status: 500 }
    );
  }
}

export async function PUT(request: Request) {
  // Get locale from centralized utility at the beginning
  const locale = await getLocaleFromCookies();
  const tAdmin = await getTranslations({ locale, namespace: 'admin' });
  const tErrors = await getTranslations({ locale, namespace: 'errors' });

  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Require authentication for updating settings
    if (!session || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: tAdmin('unauthorized') },
        { status: 401 }
      );
    }

    // Parse and validate request body
    const data = await request.json();

    // Get current settings to check against last census year
    const currentSettings = await getCensusYearSettings();
    const lastCensusYear = currentSettings.lastCensusYear;

    // Validate with the schema that includes the last census year check
    const validatedData = censusYearSchema(lastCensusYear).parse(data);

    try {
      // Synchronize the current census year across both system_settings and census_years tables
      const _censusYearId = await syncCurrentCensusYear(
        validatedData.currentCensusYear
      );

      // Get the census year details to include in the response
      const censusYear = await getCensusYearByYear(
        Number.parseInt(validatedData.currentCensusYear, 10)
      );

      return NextResponse.json({
        message: tErrors('censusYearUpdatedSuccessfullyInBothTables'),
        censusYear,
      });
    } catch (_dbError) {
      // Create a mock census year for development response
      const mockCensusYear = {
        id: 999,
        year: Number.parseInt(validatedData.currentCensusYear, 10),
        isActive: true,
        startDate: new Date(
          Number.parseInt(validatedData.currentCensusYear, 10),
          0,
          1
        ),
        endDate: new Date(
          Number.parseInt(validatedData.currentCensusYear, 10),
          11,
          31
        ),
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      return NextResponse.json({
        message: tAdmin('settingsWouldBeSavedInProductionCensusYear'),
        data: validatedData,
        censusYear: mockCensusYear,
      });
    }
  } catch (error) {
    // Handle validation errors
    if (isZodError(error)) {
      return NextResponse.json(
        {
          error: tAdmin('validationFailed'),
          details: getZodErrorDetails(error),
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        error: tAdmin('failedToUpdateCensusYear'),
        details: getErrorMessage(error),
      },
      { status: 500 }
    );
  }
}

import { hasLocale } from 'next-intl';
import { getRequestConfig } from 'next-intl/server';
import { getCurrentSydneyTime } from '@/lib/utils/date-time'; // Use your existing utility
import { routing } from './routing';

// Define formats using your existing date format constants
export const formats = {
  dateTime: {
    // Australian format: dd/MM/yyyy h:mm a (matches your DATE_FORMAT and TIME_FORMAT)
    short: {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    },
    // Long format for detailed displays
    long: {
      weekday: 'long',
      day: '2-digit',
      month: 'long',
      year: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    },
    // Date only (matches your DATE_FORMAT: dd/MM/yyyy)
    dateOnly: {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    },
  },
  number: {
    precise: {
      maximumFractionDigits: 5,
    },
    currency: {
      style: 'currency',
      currency: 'AUD', // Australian dollars
    },
  },
  list: {
    enumeration: {
      style: 'long',
      type: 'conjunction',
    },
  },
} as const;

export default getRequestConfig(async ({ requestLocale }) => {
  // This typically corresponds to the `[locale]` segment
  const requested = await requestLocale;

  // Ensure that the incoming locale is valid
  const locale = hasLocale(routing.locales, requested)
    ? requested
    : routing.defaultLocale;

  return {
    locale,
    messages: (await import(`../../lang/${locale}.json`)).default,
    formats,
    // Use your existing Sydney timezone constant
    timeZone: 'Australia/Sydney', // Matches your DEFAULT_TIMEZONE
    // Use your existing Sydney time utility
    now: getCurrentSydneyTime(),
  };
});

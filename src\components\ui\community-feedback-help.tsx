'use client';

import { HelpCircle } from 'lucide-react';
import { useTranslations } from 'next-intl';
import React, { useState } from 'react';
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
} from '@/components/ui/drawer';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { useIsMobile } from '@/hooks/use-mobile';

interface CommunityFeedbackHelpProps {
  className?: string;
  children?: React.ReactNode;
}

export function CommunityFeedbackHelp({ className, children }: CommunityFeedbackHelpProps) {
  const isMobile = useIsMobile();
  const tForms = useTranslations('forms');
  const tCensus = useTranslations('census');
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);

  // Parse the placeholder text to get individual questions
  const placeholderText = tForms('enterCommunityFeedbackPlaceholder');
  const questions = placeholderText.split('\n').filter((q) => q.trim());

  // Mobile implementation with drawer
  if (isMobile) {
    return (
      <>
        <button
          type="button"
          onClick={() => setIsDrawerOpen(true)}
          className={`inline-flex items-center justify-center transition-all duration-200 hover:text-primary focus:outline-none cursor-pointer active:scale-95 ${className}`}
          aria-label="Show community feedback questions"
        >
          {children || <HelpCircle className="h-5 w-5 text-primary/70 community-feedback-icon-animated" />}
        </button>

        <Drawer open={isDrawerOpen} onOpenChange={setIsDrawerOpen}>
          <DrawerContent className="max-h-[85vh] min-h-fit">
            <DrawerHeader className="text-left pb-4">
              <div className="flex items-start gap-3 mb-3">
                <HelpCircle className="h-6 w-6 text-primary mt-1" />
                <div>
                  <DrawerTitle className="text-xl font-semibold text-foreground">
                    {tCensus('communityFeedbackGuide')}
                  </DrawerTitle>
                  <DrawerDescription className="text-muted-foreground text-sm mt-1">
                    {tCensus('considerSharingThoughts')}
                  </DrawerDescription>
                </div>
              </div>
            </DrawerHeader>

            <div className="px-4 pb-8 space-y-4">
              {questions.map((question, index) => (
                <div
                  key={index}
                  className="p-4 rounded-xl bg-card/50 hover:bg-accent/30 hover:scale-[1.02] transition-all duration-200"
                >
                  <div className="flex items-start gap-3">
                    <div className="flex-shrink-0 w-2 h-2 rounded-full bg-primary/60 group-hover:bg-primary/80 transition-colors mt-2"></div>
                    <p className="text-sm leading-relaxed text-foreground pt-0.5">{question}</p>
                  </div>
                </div>
              ))}
            </div>
          </DrawerContent>
        </Drawer>
      </>
    );
  }

  // Desktop implementation with tooltip
  return (
    <TooltipProvider>
      <Tooltip delayDuration={0}>
        <TooltipTrigger asChild>
          <button
            type="button"
            className={`inline-flex items-center justify-center transition-all duration-300 hover:text-primary focus:outline-none cursor-pointer hover:scale-110 ${className}`}
            aria-label="Show community feedback questions"
          >
            {children || <HelpCircle className="h-5 w-5 text-primary/70 community-feedback-icon-animated" />}
          </button>
        </TooltipTrigger>
        <TooltipContent
          side="bottom"
          align="start"
          className="w-max max-w-[min(90vw,600px)] p-0 bg-background/95 backdrop-blur-sm border shadow-xl"
          sideOffset={12}
          hideArrow={true}
        >
          <div className="p-6 space-y-5">
            <div className="flex items-start gap-3 pb-3 border-b border-border/50">
              <HelpCircle className="h-5 w-5 text-primary mt-0.5" />
              <div>
                <h3 className="font-semibold text-base text-foreground">{tCensus('communityFeedbackGuide')}</h3>
                <p className="text-muted-foreground text-sm mt-0.5">
                  {tCensus('considerSharingThoughts')}
                </p>
              </div>
            </div>

            <div className="space-y-3">
              {questions.map((question, index) => (
                <div
                  key={index}
                  className="group p-3 rounded-lg bg-card/50 hover:bg-accent/30 hover:scale-[1.02] transition-all duration-200"
                >
                  <div className="flex items-start gap-3">
                    <div className="flex-shrink-0 w-2 h-2 rounded-full bg-primary/60 group-hover:bg-primary/80 transition-colors mt-2"></div>
                    <p className="text-sm leading-relaxed text-foreground">{question}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

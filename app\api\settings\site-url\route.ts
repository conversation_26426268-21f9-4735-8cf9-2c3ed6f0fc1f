import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { getTranslations } from 'next-intl/server';
import { authOptions } from '@/lib/auth/auth-options';
import { prisma } from '@/lib/db/prisma';
import { getSiteUrl, updateSiteUrl } from '@/lib/db/settings';
import {
  getErrorMessage,
  getZodErrorDetails,
  isZodError,
} from '@/lib/utils/error-handling';
import { getLocaleFromCookies } from '@/lib/utils/server-messages';
import { createSiteUrlSchema } from '@/lib/validation/settings';

export async function GET(_request: Request) {
  // Get locale from centralized utility at the beginning
  const locale = await getLocaleFromCookies();
  const t = await getTranslations({ locale, namespace: 'admin' });

  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Require authentication for accessing settings
    if (!session || session.user.role !== 'admin') {
      return NextResponse.json({ error: t('unauthorized') }, { status: 401 });
    }

    try {
      // Try to get site URL from database
      const siteUrl = await getSiteUrl();
      return NextResponse.json({ siteUrl });
    } catch (_dbError) {
      // If database connection fails, return default value
      if (process.env.NODE_ENV === 'development') {
      }

      // Return default value for development
      return NextResponse.json({
        siteUrl: 'http://localhost:3000',
      });
    }
  } catch (error) {
    return NextResponse.json(
      {
        error: t('failedToFetchSiteUrl'),
        details: getErrorMessage(error),
      },
      { status: 500 }
    );
  }
}

export async function PUT(request: Request) {
  // Get locale from centralized utility at the beginning
  const locale = await getLocaleFromCookies();
  const tAdmin = await getTranslations({ locale, namespace: 'admin' });
  const tNotifications = await getTranslations({
    locale,
    namespace: 'notifications',
  });

  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Require authentication for updating settings
    if (!session || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: tAdmin('unauthorized') },
        { status: 401 }
      );
    }

    // Parse and validate request body with translations
    const data = await request.json();
    const siteUrlSchema = await createSiteUrlSchema(locale);
    const validatedData = siteUrlSchema.parse(data);

    try {
      // Try to update site URL
      await updateSiteUrl(validatedData.siteUrl);

      // Try to log the action
      try {
        await prisma.auditLog.create({
          data: {
            userType: 'admin',
            userId: Number.parseInt(session.user.id, 10),
            action: 'update-site-url',
            entityType: 'system_settings',
            entityId: null,
            newValues: JSON.stringify({ siteUrl: validatedData.siteUrl }),
            ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
          },
        });
      } catch (_logError) {}

      return NextResponse.json({
        message: tNotifications('siteUrlUpdatedSuccessfully'),
      });
    } catch (_dbError) {
      return NextResponse.json({
        message: tAdmin('settingsWouldBeSavedInProduction'),
        data: validatedData,
      });
    }
  } catch (error) {
    // Handle validation errors (reuse tAdmin from function scope)
    if (isZodError(error)) {
      return NextResponse.json(
        {
          error: tAdmin('validationFailed'),
          details: getZodErrorDetails(error),
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        error: tAdmin('failedToUpdateSiteUrl'),
        details: getErrorMessage(error),
      },
      { status: 500 }
    );
  }
}

'use client';

import { Eye, Lightbulb, MessageSquare } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { SettingsCard } from '@/components/admin/settings/SettingsCard';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { useFormSubmit } from '@/hooks/useFormSubmit';
import { useMessage } from '@/hooks/useMessage';
import { zodResolver } from '@/lib/utils/zod-resolver-compat';
import { createClientHomepageAnnouncementSchema } from '@/lib/validation/client/settings-client';

// Form validation schema using centralized validation
type HomepageAnnouncementFormValues = {
  enabled: boolean;
  text: string;
  type: 'info' | 'warning' | 'success' | 'destructive';
};

export function HomepageAnnouncementSettings() {
  const t = useTranslations('admin');
  const tValidation = useTranslations('validation');

  const { showError } = useMessage();

  const [preview, setPreview] = useState<string>('');
  const [isLoadingPreview, setIsLoadingPreview] = useState(false);

  // Create validation schema with translations
  const homepageAnnouncementSchema =
    createClientHomepageAnnouncementSchema(tValidation);

  const form = useForm<HomepageAnnouncementFormValues>({
    resolver: zodResolver(homepageAnnouncementSchema),
    defaultValues: {
      enabled: false,
      text: 'Welcome to [CHURCH_NAME]! [CENSUS_STATUS]',
      type: 'info',
    },
  });

  const { handleSubmit: submitForm, isSubmitting } = useFormSubmit({
    onSubmit: async (data: HomepageAnnouncementFormValues) => {
      const response = await fetch('/api/settings/homepage-announcement', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save settings');
      }

      const result = await response.json();
      return { success: true, message: result.message };
    },
    defaultSuccessMessage: 'Homepage announcement settings saved successfully',
    defaultErrorMessage: 'Failed to save homepage announcement settings',
  });

  // Fetch current settings
  useEffect(() => {
    const fetchSettings = async () => {
      try {
        const response = await fetch('/api/settings/homepage-announcement');

        if (!response.ok) {
          if (response.status === 401) {
            showError('needAdminLoginToViewSettings');
          } else {
            showError('failedToLoadHomepageAnnouncementSettings');
          }
          return;
        }

        const data = await response.json();
        form.reset(data);

        // Generate initial preview
        generatePreview(data.text);
      } catch (error) {
        showError('failedToLoadHomepageAnnouncementSettings');
      }
    };

    fetchSettings();
  }, [form, showError]);

  // Generate preview using secure POST method
  const generatePreview = async (text: string) => {
    if (!text.trim()) {
      setPreview('');
      return;
    }

    setIsLoadingPreview(true);
    try {
      const response = await fetch('/api/settings/homepage-announcement', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          preview: true,
          text: text.trim(),
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setPreview(data.processed || '');
      }
    } catch (error) {
      // Silent failure for preview - not critical
    } finally {
      setIsLoadingPreview(false);
    }
  };

  // Watch text changes for live preview
  const watchedText = form.watch('text');
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      generatePreview(watchedText);
    }, 500); // Debounce preview generation

    return () => clearTimeout(timeoutId);
  }, [watchedText]);

  // Submit form
  const onSubmit = submitForm;

  // Available placeholders with dynamic descriptions
  const placeholders = [
    { key: '[CENSUS_DATES]', desc: t('placeholderCensusDates') },
    { key: '[CENSUS_STATUS]', desc: t('placeholderCensusStatus') },
    { key: '[CHURCH_NAME]', desc: t('placeholderChurchName') },
    { key: '[START_DATE]', desc: t('placeholderStartDate') },
    { key: '[END_DATE]', desc: t('placeholderEndDate') },
  ];

  return (
    <SettingsCard
      description={t('configureHomepageAnnouncementMessage')}
      form={form}
      isSubmitting={isSubmitting}
      onFormSubmit={onSubmit}
      title={t('homepageAnnouncement')}
    >
      <div className="space-y-6">
        {/* Enable/Disable Toggle */}
        <div className="space-y-2">
          <label
            className="font-medium text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            htmlFor="enabled"
          >
            {t('homepageAnnouncementStatus')}
          </label>
          <div className="flex items-center space-x-2">
            <Switch
              checked={form.watch('enabled')}
              id="enabled"
              onCheckedChange={(checked) => form.setValue('enabled', checked)}
            />
            <span className="font-medium text-sm">
              {form.watch('enabled')
                ? t('homepageAnnouncementEnabled')
                : t('homepageAnnouncementDisabled')}
            </span>
          </div>
        </div>

        {/* Announcement Text */}
        <div className="space-y-2">
          <label
            className="font-medium text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            htmlFor="text"
          >
            {t('announcementText')}
          </label>
          {form.formState.errors.text && (
            <p className="text-destructive text-sm">
              {form.formState.errors.text.message}
            </p>
          )}
          <Textarea
            className="min-h-[120px] resize-none"
            id="text"
            maxLength={1000}
            placeholder={t('enterAnnouncementText')}
            {...form.register('text')}
          />
          <div className="mt-1 text-muted-foreground text-xs">
            {form.watch('text')?.length || 0}/1000
          </div>
        </div>

        {/* Available Placeholders */}
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Lightbulb className="h-4 w-4 text-amber-500" />
            <span className="font-medium text-sm">
              {t('availablePlaceholders')}:
            </span>
          </div>
          <div className="flex flex-wrap gap-2">
            {placeholders.map((placeholder) => (
              <Badge
                className="cursor-pointer hover:bg-accent"
                key={placeholder.key}
                onClick={() => {
                  const currentText = form.getValues('text');
                  const newText =
                    currentText + (currentText ? ' ' : '') + placeholder.key;
                  form.setValue('text', newText);
                }}
                title={placeholder.desc}
                variant="outline"
              >
                {placeholder.key}
              </Badge>
            ))}
          </div>
        </div>

        {/* Alert Type */}
        <div className="space-y-2">
          <label
            className="font-medium text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            htmlFor="type"
          >
            {t('alertType')}
          </label>
          <Select
            onValueChange={(value) => form.setValue('type', value as any)}
            value={form.watch('type')}
          >
            <SelectTrigger id="type">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="info">{t('info')}</SelectItem>
              <SelectItem value="warning">{t('warning')}</SelectItem>
              <SelectItem value="success">{t('success')}</SelectItem>
              <SelectItem value="destructive">{t('urgent')}</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Live Preview */}
        {form.watch('enabled') && form.watch('text') && (
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Eye className="h-4 w-4 text-blue-500" />
              <span className="font-medium text-sm">{t('preview')}:</span>
            </div>

            {isLoadingPreview ? (
              <div className="animate-pulse">
                <div className="h-12 rounded-md bg-muted" />
              </div>
            ) : preview ? (
              <Alert variant={form.watch('type') as any}>
                <MessageSquare className="h-4 w-4" />
                <AlertDescription>
                  {preview || t('emptyAnnouncementPreview')}
                </AlertDescription>
              </Alert>
            ) : null}
          </div>
        )}
      </div>
    </SettingsCard>
  );
}

'use client';

import { Respons<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Treemap } from 'recharts';
import { type BaseChartProps, registerChart } from './chart-registry';
import { CHART_DEFAULTS, getChartColor, getColorByString } from './constants';

interface TreemapDataPoint {
  name: string;
  value: number;
  category?: string;
  [key: string]: unknown;
}

interface TreemapContentProps {
  x: number;
  y: number;
  width: number;
  height: number;
  payload: TreemapDataPoint & { fill: string };
  name: string;
  [key: string]: unknown;
}

function TreemapChartComponent({
  data,
  isAnimationActive = true,
  className,
}: BaseChartProps) {
  // Note: Config could be used for customisation but current implementation uses direct data structure

  // Process data for treemap
  const processedData = data.data.map((item, index) => {
    const treemapItem = item as TreemapDataPoint;
    return {
      ...treemapItem,
      fill: treemapItem.category
        ? getColorByString(treemapItem.category)
        : getChartColor(index),
    };
  });

  // Custom content renderer for treemap cells
  const CustomContent = ({
    x,
    y,
    width,
    height,
    payload,
    name,
  }: TreemapContentProps) => {
    const fontSize = Math.min(width / 8, height / 4, 14);
    const textColor = '#ffffff';

    return (
      <g>
        <rect
          aria-label={`${name}: ${payload.value?.toLocaleString()}`}
          className="transition-all duration-200 hover:opacity-80"
          height={height}
          role="button"
          style={{
            fill: payload.fill,
            stroke: '#fff',
            strokeWidth: 2,
            strokeOpacity: 1,
          }}
          tabIndex={0}
          width={width}
          x={x}
          y={y}
        />
        {width > 40 && height > 20 && (
          <>
            <text
              className="pointer-events-none"
              fill={textColor}
              fontSize={fontSize}
              fontWeight="bold"
              textAnchor="middle"
              x={x + width / 2}
              y={y + height / 2 - fontSize / 2}
            >
              {name}
            </text>
            <text
              className="pointer-events-none"
              fill={textColor}
              fontSize={fontSize * 0.8}
              textAnchor="middle"
              x={x + width / 2}
              y={y + height / 2 + fontSize / 2}
            >
              {payload.value?.toLocaleString()}
            </text>
          </>
        )}
      </g>
    );
  };

  // Custom tooltip
  const CustomTooltip = ({
    active,
    payload,
  }: {
    active?: boolean;
    payload?: Array<{ payload: TreemapDataPoint & { percentage: string } }>;
  }) => {
    if (active && payload && payload.length) {
      const data = payload[0]?.payload;
      if (!data || data.value === undefined || data.value === null) {
        return null;
      }

      return (
        <div className="rounded-lg border border-slate-200 bg-white p-3 shadow-lg dark:border-slate-600 dark:bg-slate-800">
          <p className="mb-2 font-medium text-slate-900 dark:text-slate-100">
            {data.name}
          </p>
          <div className="space-y-1 text-sm">
            <p className="text-slate-600 dark:text-slate-400">
              Value:{' '}
              <span className="font-medium">
                {data.value?.toLocaleString()}
              </span>
            </p>
            {data.category && (
              <p className="text-slate-600 dark:text-slate-400">
                Category: <span className="font-medium">{data.category}</span>
              </p>
            )}
            {data.percentage && (
              <p className="text-slate-600 dark:text-slate-400">
                Percentage:{' '}
                <span className="font-medium">{data.percentage}%</span>
              </p>
            )}
          </div>
        </div>
      );
    }
    return null;
  };

  // Calculate percentages
  const total = processedData.reduce((sum, item) => sum + item.value, 0);
  const dataWithPercentages = processedData.map((item) => ({
    ...item,
    percentage: total > 0 ? ((item.value / total) * 100).toFixed(1) : '0',
  }));

  // Get unique categories for legend
  const categories = Array.from(
    new Set(
      processedData
        .map((d) => d.category)
        .filter((cat): cat is string => Boolean(cat))
    )
  );

  return (
    <div className={`w-full ${className || ''}`}>
      <ResponsiveContainer height={CHART_DEFAULTS.HEIGHT} width="100%">
        <Treemap
          animationDuration={CHART_DEFAULTS.ANIMATION_DURATION}
          aspectRatio={4 / 3}
          content={CustomContent as any}
          data={dataWithPercentages}
          dataKey="value"
          fill="#8884d8"
          isAnimationActive={isAnimationActive}
          stroke="#fff"
        >
          <Tooltip content={<CustomTooltip />} />
        </Treemap>
      </ResponsiveContainer>

      {/* Legend */}
      {categories.length > 1 && (
        <div className="mt-4 flex flex-wrap justify-center gap-4 text-sm">
          {categories.map((category) => (
            <div className="flex items-center gap-2" key={category}>
              <div
                className="h-3 w-3 rounded-sm"
                style={{ backgroundColor: getColorByString(category) }}
              />
              <span className="text-slate-700 dark:text-slate-300">
                {category}
              </span>
            </div>
          ))}
        </div>
      )}

      {/* Summary stats */}
      <div className="mt-4 text-center text-slate-600 text-sm dark:text-slate-400">
        <p>
          Total: <span className="font-medium">{total.toLocaleString()}</span> •
          Items: <span className="font-medium">{processedData.length}</span>
        </p>
      </div>
    </div>
  );
}

// Register the component
registerChart('treemap', TreemapChartComponent);

export default TreemapChartComponent;

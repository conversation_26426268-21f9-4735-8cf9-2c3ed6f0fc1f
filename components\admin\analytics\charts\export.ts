// INDUSTRY STANDARD 2025: Chart export with proper OKLCH color conversion

import type { ChartData } from '@/lib/utils/chart-data-formatter';

// Export format types - simplified to CSV and PNG only
export type ExportFormat = 'csv' | 'png';

export interface ExportOptions {
  format: ExportFormat;
  filename?: string;
  includeMetadata?: boolean;
  includeTimestamp?: boolean;
  customHeaders?: Record<string, string>;
}

// Note: OKLCH conversion interfaces and functions removed since html2canvas-pro handles OKLCH natively

// Note: OKLCH parsing and conversion functions removed since html2canvas-pro handles OKLCH natively

// Note: OKLCH conversion functions removed since html2canvas-pro handles OK<PERSON><PERSON> natively

// Note: createOklchFreeClone function removed since html2canvas-pro handles OK<PERSON>H natively

// Utility function to wait for element to be ready for export
const waitForElementReady = (
  element: HTMLElement,
  maxWaitTime = 5000 // 5 second timeout
): Promise<void> => {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();

    const checkElementReady = () => {
      // Check if element exists and has measurable dimensions
      if (element && element.offsetWidth > 0 && element.offsetHeight > 0) {
        // Additional check for SVG content (common in chart libraries)
        const svgElements = element.querySelectorAll('svg');
        const hasValidSvg =
          svgElements.length === 0 ||
          Array.from(svgElements).some(
            (svg) =>
              svg.getBoundingClientRect().width > 0 &&
              svg.getBoundingClientRect().height > 0
          );

        if (hasValidSvg) {
          resolve();
          return;
        }
      }

      // Check timeout
      if (Date.now() - startTime > maxWaitTime) {
        reject(new Error(`Element failed to render within ${maxWaitTime}ms`));
        return;
      }

      // Continue polling
      setTimeout(checkElementReady, 50);
    };

    checkElementReady();
  });
};

// Utility function to safely format CSV cells and prevent injection attacks
function formatSafeCSVCell(value: string): string {
  // Prevent CSV injection by checking for dangerous characters at the start
  const dangerousChars = /^[=+\-@\t\r]/;
  let sanitizedValue = value;

  if (dangerousChars.test(value)) {
    // Prepend a space to neutralize formula injection
    sanitizedValue = ` ${value}`;
  }

  // Escape quotes by doubling them and wrap in quotes
  return `"${sanitizedValue.replace(/"/g, '""')}"`;
}

// CSV export functionality
export function exportToCSV(
  data: ChartData,
  options: ExportOptions = { format: 'csv' }
): void {
  const {
    filename = 'chart-data',
    includeMetadata = true,
    includeTimestamp = true,
  } = options;

  let csvContent = '';

  // Add metadata header if requested
  if (includeMetadata) {
    csvContent += '# Chart Data Export\n';
    csvContent += `# Chart Type: ${data.type}\n`;
    if (data.title) {
      csvContent += `# Title: ${data.title}\n`;
    }
    if (includeTimestamp) {
      csvContent += `# Exported: ${new Date().toISOString()}\n`;
    }
    csvContent += `# Total Records: ${data.data.length}\n`;
    csvContent += '\n';
  }

  if (data.data.length === 0) {
    csvContent += 'No data available\n';
  } else {
    // Get headers with deterministic order
    const headers =
      data.data.length > 0
        ? // take the key order of the first item to keep a stable header order
          Object.keys(data.data[0])
        : [];

    // fallback: merge in keys that do not appear in the first row
    for (const row of data.data) {
      for (const k of Object.keys(row)) {
        if (!headers.includes(k)) {
          headers.push(k);
        }
      }
    }

    // Add headers (also protect against injection in column names)
    csvContent += `${headers.map((header) => formatSafeCSVCell(header)).join(',')}\n`;

    // Add data rows
    data.data.forEach((item) => {
      const row = headers.map((header) => {
        const value = (item as Record<string, unknown>)[header];
        if (value === null || value === undefined) {
          return '';
        }
        return formatSafeCSVCell(String(value));
      });
      csvContent += `${row.join(',')}\n`;
    });
  }

  // Create and download file
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  downloadBlob(blob, `${filename}.csv`);
}

// Chart PNG export functionality using html-to-image (modern CSS support)
export async function exportChartAsImage(
  chartElement: HTMLElement,
  options: ExportOptions
): Promise<void> {
  const { filename = 'chart' } = options;

  try {
    // Wait for chart to be fully rendered with proper readiness check
    await waitForElementReady(chartElement);

    // SIMPLIFIED: html2canvas-pro has native OKLCH support, minimal conversion needed
    if (process.env.NODE_ENV === 'development') {
    }

    // Wait for DOM updates and force reflow
    await new Promise((resolve) => setTimeout(resolve, 100));
    void chartElement.offsetHeight;

    // Try html-to-image first (better modern CSS support)
    try {
      const htmlToImage = await import('html-to-image');

      const dataUrl = await htmlToImage.toPng(chartElement, {
        backgroundColor: '#ffffff',
        pixelRatio: 2,
        skipAutoScale: false,
        canvasWidth: chartElement.offsetWidth * 2,
        canvasHeight: chartElement.offsetHeight * 2,
        style: {
          transform: 'scale(1)',
          transformOrigin: 'top left',
        },
      });

      // Convert data URL to blob and download
      const response = await fetch(dataUrl);
      const blob = await response.blob();
      downloadBlob(blob, `${filename}.png`);

      return; // Success with html-to-image
    } catch (_htmlToImageError) {
      // Fallback to html2canvas-pro (native OKLCH support)
      await exportWithSimplifiedFallback(chartElement, options);
    }
  } catch (_error) {
    throw new Error('Failed to export chart as PNG');
  } finally {
    // SIMPLIFIED: html2canvas-pro handles OKLCH natively, minimal cleanup needed
    if (process.env.NODE_ENV === 'development') {
    }
  }
}

// INDUSTRY STANDARD 2025: html2canvas-pro with native OKLCH support
async function exportWithSimplifiedFallback(
  chartElement: HTMLElement,
  options: ExportOptions
): Promise<void> {
  const { filename = 'chart' } = options;

  try {
    // SIMPLIFIED APPROACH: Use html2canvas-pro directly on original element
    // html2canvas-pro has native OKLCH support, so no cloning needed

    // Force style recalculation to ensure all styles are computed
    void chartElement.offsetHeight;

    // INDUSTRY STANDARD 2025: Use html2canvas-pro for native OKLCH support
    const html2canvas = await import('html2canvas-pro');

    if (process.env.NODE_ENV === 'development') {
    }

    const canvas = await html2canvas.default(chartElement, {
      backgroundColor: '#ffffff',
      scale: 2,
      logging: process.env.NODE_ENV === 'development', // Enable logging in development
      useCORS: true,
      allowTaint: true,
      foreignObjectRendering: false,
      // html2canvas-pro supports OKLCH natively, so minimal filtering needed
      ignoreElements: (element) => {
        return element.tagName === 'SCRIPT' || element.tagName === 'STYLE';
      },
    });

    canvas.toBlob((blob) => {
      if (blob) {
        downloadBlob(blob, `${filename}.png`);
      } else {
        throw new Error('Failed to create blob from canvas');
      }
    }, 'image/png');
  } catch (fallbackError) {
    // html2canvas-pro should handle OKLCH natively, so this is likely a different issue
    if (fallbackError instanceof Error) {
      const enhancedError = new Error(
        `Chart export failed with html2canvas-pro: ${fallbackError.message}\n` +
          'This may be due to complex CSS or DOM structure rather than OKLCH colors.'
      );
      enhancedError.name = 'ChartExportError';
      throw enhancedError;
    }

    throw fallbackError;
  }
}

// Utility function to download blob
function downloadBlob(blob: Blob, filename: string): void {
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  link.style.display = 'none';

  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);

  // Clean up the URL object
  setTimeout(() => URL.revokeObjectURL(url), 100);
}

// Main export function
export async function exportChart(
  data: ChartData,
  chartElement: HTMLElement | null,
  options: ExportOptions
): Promise<void> {
  const { format } = options;
  switch (format) {
    case 'csv':
      exportToCSV(data, options);
      break;

    case 'png':
      if (!chartElement) {
        throw new Error('Chart element is required for PNG export');
      }
      await exportChartAsImage(chartElement, options);
      break;

    default:
      throw new Error(
        `Unsupported export format: ${format}. Only CSV and PNG are supported.`
      );
  }
}

// Utility to get available export formats
export function getAvailableExportFormats(): ExportFormat[] {
  const formats: ExportFormat[] = ['csv'];

  // Check if we're in browser environment for PNG export
  if (typeof window !== 'undefined') {
    formats.push('png');
  }

  return formats;
}

// Utility to validate export options
export function validateExportOptions(options: ExportOptions): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];
  const availableFormats = getAvailableExportFormats();

  if (!availableFormats.includes(options.format)) {
    errors.push(`Unsupported export format: ${options.format}`);
  }

  if (options.filename && !/^[a-zA-Z0-9_-]+$/.test(options.filename)) {
    errors.push(
      'Filename can only contain letters, numbers, hyphens, and underscores'
    );
  }

  return { isValid: errors.length === 0, errors };
}

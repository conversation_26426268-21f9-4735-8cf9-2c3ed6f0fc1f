/**
 * Response Security Validation Module
 *
 * This module provides security filtering for AI responses to prevent
 * information leakage while preserving chart data markers.
 */

// INDUSTRY STANDARD 2025: Preserve structured data markers for chart generation
export function validateResponseSecurity(content: string): string {
  if (typeof content !== 'string') {
    return '';
  }

  // Remove potential sensitive patterns but preserve CHART_DATA markers
  const lines = content.split('\n');
  const filteredLines = lines.map((line) => {
    // Skip filtering lines that contain CHART_DATA to preserve chart generation
    if (line.includes('CHART_DATA:')) {
      return line;
    }

    // Apply security filtering to other lines
    return (
      line
        // Remove potential stack traces
        .replace(/at\s+[\w.]+\s*\([^)]+\)/g, '[FILTERED]')
        // Remove file paths (Windows and Unix)
        .replace(/[A-Za-z]:\\[^\\]+\\[^\s]+/g, '[FILTERED]')
        .replace(/\/[^/\s]+\/[^/\s]+\/[^\s]+/g, '[FILTERED]')
        // Remove potential connection strings
        .replace(/postgresql:\/\/[^\s]+/g, '[FILTERED]')
        .replace(/mongodb:\/\/[^\s]+/g, '[FILTERED]')
        .replace(/mysql:\/\/[^\s]+/g, '[FILTERED]')
        .replace(/redis:\/\/[^\s]+/g, '[FILTERED]')
        // Remove potential API keys, tokens, and secrets
        .replace(/[A-Za-z0-9]{32,}/g, '[FILTERED]')
        .replace(/sk-[A-Za-z0-9]{32,}/g, '[FILTERED]') // OpenAI API keys
        .replace(/AIza[A-Za-z0-9]{35}/g, '[FILTERED]') // Google API keys
        // Remove potential email addresses in error messages
        .replace(
          /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g,
          '[FILTERED]'
        )
        // Remove potential IP addresses
        .replace(/\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b/g, '[FILTERED]')
        // Remove potential port numbers in URLs
        .replace(/:(\d{4,5})\b/g, ':[FILTERED]')
        // Remove potential database table/column references
        .replace(
          /\b(SELECT|FROM|WHERE|INSERT|UPDATE|DELETE)\s+[^\s]+/gi,
          '[FILTERED]'
        )
        // Remove potential environment variables
        .replace(/\$[A-Z_][A-Z0-9_]*/g, '[FILTERED]')
        .replace(/process\.env\.[A-Z_][A-Z0-9_]*/g, '[FILTERED]')
    );
  });

  return filteredLines.join('\n').trim();
}

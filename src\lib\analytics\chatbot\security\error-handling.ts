/**
 * Security Error Handling Module
 *
 * This module provides secure error logging and response generation
 * with internationalization support and information leakage prevention.
 */

import type { NextRequest } from 'next/server';
import { getTranslations } from 'next-intl/server';

// --- SECURITY: Error Information Leakage Prevention ---

// Secure error logging - detailed logs server-side only
export function logSecureError(
  context: string,
  error: unknown,
  additionalInfo?: Record<string, unknown>
): void {
  if (process.env.NODE_ENV === 'development') {
    // Development: Log detailed error information for debugging
    console.error(`[${context}]`, error, additionalInfo);
  } else {
    // Production: Log to proper logging service (structured logging)
    const _errorInfo = {
      context,
      timestamp: new Date().toISOString(),
      error:
        error instanceof Error
          ? {
              name: error.name,
              message: error.message,
              stack: error.stack,
            }
          : String(error),
      ...additionalInfo,
    };
  }
}

// Generic user-facing error messages - now with internationalization support
export async function getSecureErrorResponse(
  context: string,
  _request: NextRequest,
  locale: 'en' | 'zh-CN'
): Promise<string> {
  try {
    // Use provided locale from centralized detection
    const t = await getTranslations({ locale, namespace: 'api' });

    const messageMap = {
      auth: () => t('authenticationFailed'),
      validation: () => t('invalidRequestFormat'),
      database: () => t('databaseUnavailable'),
      ai_service: () => t('aiServiceUnavailable'),
      timeout: () => t('requestTimedOut'),
      rate_limit: () => t('tooManyRequests'),
      general: () => t('generalError'),
    };

    const messageFunction =
      messageMap[context as keyof typeof messageMap] || messageMap.general;
    return messageFunction();
  } catch (_translationError) {
    // Fallback to English if translation fails
    const fallbackMessages = {
      auth: 'Authentication failed. Please try logging in again.',
      validation:
        'Invalid request format. Please check your input and try again.',
      database:
        'Database temporarily unavailable. Please try again in a moment.',
      ai_service: 'AI service temporarily unavailable. Please try again.',
      timeout: 'Request timed out. Please try again with a simpler query.',
      rate_limit: 'Too many requests. Please wait before trying again.',
      general:
        'An error occurred while processing your request. Please try again.',
    };
    return (
      fallbackMessages[context as keyof typeof fallbackMessages] ||
      fallbackMessages.general
    );
  }
}

// Sanitize database errors to prevent information leakage
export function sanitizeDatabaseError(error: unknown): string {
  if (process.env.NODE_ENV === 'development') {
    // Development: Show more details for debugging
    if (error instanceof Error) {
      return `Database error: ${error.message}`;
    }
  }

  // Production: Generic message only
  return 'Database query failed - providing general guidance.';
}

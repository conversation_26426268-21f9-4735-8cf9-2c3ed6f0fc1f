/**
 * Utils Module Index
 *
 * Central export point for all utility functionality in the chatbot system.
 * This module provides a clean interface for importing utility functions throughout the application.
 */

export type {
  AnalyticsEvent,
  AnalyticsMetrics,
} from './analytics';

// Analytics utilities
export {
  analyticsTracker,
  createPerformanceTimer,
  getAnalyticsMetrics,
  getAnalyticsSummary,
  PerformanceTimer,
  resetAnalytics,
  trackCacheHit,
  trackError,
  trackRequest,
} from './analytics';
// Cache utilities
export {
  CACHE_CLEANUP_THRESHOLD,
  CACHE_DURATION,
  cleanupCache,
  clearCache,
  generateCacheKey,
  getCachedResult,
  getCacheStats,
  hasCachedResult,
  MAX_CACHE_SIZE,
  requestCache,
  setCachedResult,
} from './cache';

// Helper utilities
export {
  containsSQLCommands,
  extractSacramentType,
  formatNumber,
  formatPercentage,
  isMessageSecure,
  isValidNumber,
  normalizeWhitespace,
  parseYear,
  sanitizeDatabaseResult,
  sanitizeUserInput,
  truncateText,
  validateIntentSecurity,
  validateQueryLength,
} from './helpers';

import { type NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { getTranslations } from 'next-intl/server';
import { z } from 'zod/v4';
import { authOptions } from '@/lib/auth/auth-options';
import { prisma } from '@/lib/db/prisma';
import {
  AUDIT_LOG_RETENTION_DAYS,
  SESSION_EXPIRY_HOURS,
} from '@/lib/utils/date-time';
import {
  getErrorMessage,
  getZodErrorDetails,
  isZodError,
} from '@/lib/utils/error-handling';
import { getLocaleFromCookies } from '@/lib/utils/server-messages';

// Environment-aware logging
const isDevelopment = process.env.NODE_ENV === 'development';

function logError(_message: string, _error?: any) {
  if (isDevelopment) {
  }
}

// Schema for validating service action requests
const serviceActionSchema = z.object({
  action: z.enum(['clean-sessions', 'clean-audit-logs']),
  parameters: z
    .record(
      z.string(),
      z.union([z.string(), z.number(), z.boolean(), z.null()])
    )
    .optional(),
});

/**
 * GET endpoint to retrieve database service information
 */
export async function GET(_request: NextRequest) {
  // Get locale from centralized utility at the beginning
  const locale = await getLocaleFromCookies();
  const tErrors = await getTranslations({ locale, namespace: 'errors' });

  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Require admin authentication
    if (!session || session.user.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get database statistics using Prisma
    const [sessionCount, expiredSessionCount, auditLogCount, oldAuditLogCount] =
      await Promise.all([
        // Check if user_sessions table exists, if not return 0
        prisma.userSession?.count().catch(() => 0) || 0,
        // Check expired sessions
        prisma.userSession
          ?.count({
            where: {
              lastActivity: {
                lt: new Date(
                  Date.now() - SESSION_EXPIRY_HOURS * 60 * 60 * 1000
                ),
              },
            },
          })
          .catch(() => 0) || 0,
        // Check if audit_logs table exists, if not return 0
        prisma.auditLog?.count().catch(() => 0) || 0,
        // Check old audit logs
        prisma.auditLog
          ?.count({
            where: {
              createdAt: {
                lt: new Date(
                  Date.now() - AUDIT_LOG_RETENTION_DAYS * 24 * 60 * 60 * 1000
                ),
              },
            },
          })
          .catch(() => 0) || 0,
      ]);

    return NextResponse.json({
      sessions: {
        total: sessionCount,
        expired: expiredSessionCount,
      },
      auditLogs: {
        total: auditLogCount,
        old: oldAuditLogCount,
      },
    });
  } catch (error) {
    logError('Error fetching database service information', error);
    return NextResponse.json(
      {
        error: tErrors('failedToFetchDatabaseInfo'),
        details: getErrorMessage(error),
      },
      { status: 500 }
    );
  }
}

/**
 * POST endpoint to perform database service actions
 */
export async function POST(request: NextRequest) {
  // Get locale from centralized utility at the beginning
  const locale = await getLocaleFromCookies();
  const tAdmin = await getTranslations({ locale, namespace: 'admin' });
  const tErrors = await getTranslations({ locale, namespace: 'errors' });

  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Require admin authentication
    if (!session || session.user.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse and validate request body
    const data = await request.json();
    const validatedData = serviceActionSchema.parse(data);

    // Get client IP address for audit logging
    const ipAddress =
      request.headers.get('x-forwarded-for') ||
      request.headers.get('x-real-ip') ||
      'unknown';

    // Perform the requested action
    switch (validatedData.action) {
      case 'clean-sessions': {
        // Delete expired sessions using Prisma
        const deleteSessionsResult = (await prisma.userSession
          ?.deleteMany({
            where: {
              lastActivity: {
                lt: new Date(
                  Date.now() - SESSION_EXPIRY_HOURS * 60 * 60 * 1000
                ),
              },
            },
          })
          .catch(() => ({ count: 0 }))) || { count: 0 };

        // Log the action using Prisma
        await prisma.auditLog
          ?.create({
            data: {
              userType: 'admin',
              userId: Number.parseInt(session.user.id, 10),
              action: 'clean-sessions',
              entityType: 'user_sessions',
              entityId: 0,
              ipAddress,
            },
          })
          .catch(() => null);

        return NextResponse.json({
          success: true,
          message: `Successfully cleaned ${deleteSessionsResult.count} expired sessions`,
          affectedRows: deleteSessionsResult.count,
        });
      }

      case 'clean-audit-logs': {
        // Delete old audit logs using Prisma
        const deleteLogsResult = (await prisma.auditLog
          ?.deleteMany({
            where: {
              createdAt: {
                lt: new Date(
                  Date.now() - AUDIT_LOG_RETENTION_DAYS * 24 * 60 * 60 * 1000
                ),
              },
            },
          })
          .catch(() => ({ count: 0 }))) || { count: 0 };

        // Log the action using Prisma
        await prisma.auditLog
          ?.create({
            data: {
              userType: 'admin',
              userId: Number.parseInt(session.user.id, 10),
              action: 'clean-audit-logs',
              entityType: 'audit_logs',
              entityId: 0,
              ipAddress,
            },
          })
          .catch(() => null);

        return NextResponse.json({
          success: true,
          message: `Successfully cleaned ${deleteLogsResult.count} old audit logs`,
          affectedRows: deleteLogsResult.count,
        });
      }

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }
  } catch (error) {
    logError('Error performing database service action', error);

    // Handle validation errors
    if (isZodError(error)) {
      return NextResponse.json(
        {
          error: tAdmin('validationError'),
          details: getZodErrorDetails(error),
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        error: tErrors('databaseServiceActionFailed'),
        details: getErrorMessage(error),
      },
      { status: 500 }
    );
  }
}

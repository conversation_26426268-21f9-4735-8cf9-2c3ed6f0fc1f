# Welcome Modal System Implementation Guide

## Overview

The Welcome Modal System provides guided onboarding for census participants, showing a welcome modal at strategic moments to help users complete their census forms. The system uses **transition-based detection** with **real-time progress tracking** to determine when and how to display the modal.

## Key Features

- **Transition-Based Detection**: Shows modal when users transition to the main census form
- **Real-Time Progress Tracking**: Monitors completion status to determine modal visibility
- **Session-Based Prevention**: Prevents modal spam by showing once per session
- **Progress-Dependent Display**: Only shows for users with incomplete progress (<100%)
- **Permanent Dismissal**: Users can permanently dismiss the modal
- **Responsive Design**: Dialog for desktop, drawer for mobile
- **Future-Proof Architecture**: Compatible with planned Catholic screening flow

## Architecture Components

### 1. Core Hook: `useWelcomeModal`
```typescript
// Location: src/components/census/welcome-modal/useWelcomeModal.ts
const { isOpen, setIsOpen, isDismissed, dismissModal } = useWelcomeModal();
```

**Features:**
- Manages modal open/close state
- <PERSON>les permanent dismissal via localStorage
- Provides session-persistent state management

### 2. Progress Tracking: `useCensusProgress`
```typescript
// Location: src/hooks/use-census-progress.tsx
const { progress, isLoading: isProgressLoading, refreshProgress } = useCensusProgress();
```

**Features:**
- Real-time progress calculation (0-100%)
- Loading state management
- Event-driven refresh system
- SSR-compatible hydration

### 3. Responsive Modal Component: `CensusWelcomeModal`
```typescript
// Location: src/components/census/welcome-modal/CensusWelcomeModal.tsx
<CensusWelcomeModal isOpen={isOpen} onClose={onClose} onDismiss={onDismiss} />
```

**Features:**
- Desktop: Dialog component (≥768px)
- Mobile: Drawer component (<768px) with swipe-to-dismiss
- Lazy-loaded for performance
- Accessible design with proper ARIA labels

## Implementation Details

### Transition Detection Logic

The core logic detects when users transition from registration to the main census form:

```typescript
// Location: app/[locale]/census/[code]/census-form-client.tsx
useEffect(() => {
  // Wait for progress to be loaded before making decision
  if (isProgressLoading) {
    return;
  }

  // Only trigger when isRegistered becomes true for the first time this session
  if (isRegistered && !hasTriggeredWelcome) {
    // Debug logging (development only)
    if (process.env.NODE_ENV === 'development') {
      console.log('Welcome modal check - Progress:', progress, 'isDismissed:', isDismissed);
    }
    
    // Show modal if progress < 100% and not permanently dismissed
    if (!isDismissed && progress < 100) {
      if (process.env.NODE_ENV === 'development') {
        console.log('Showing welcome modal - Progress:', progress);
      }
      setWelcomeOpen(true);
    }
    setHasTriggeredWelcome(true); // Prevent multiple triggers this session
  }
}, [isRegistered, hasTriggeredWelcome, isDismissed, progress, isProgressLoading, setWelcomeOpen]);
```

### State Management

**Key State Variables:**
- `isRegistered`: Determines if user has completed household registration
- `hasTriggeredWelcome`: Prevents multiple modal shows per session
- `progress`: Real-time completion percentage (0-100%)
- `isProgressLoading`: Ensures accurate progress data before decisions
- `isDismissed`: Permanent dismissal status from localStorage

### User Flow Scenarios

#### First-Time Users
1. **Login** → Registration form (`isRegistered = false`)
2. **Complete registration** → `setIsRegistered(true)` → **TRANSITION DETECTED**
3. **Progress check** → If < 100%, modal shows
4. **Session tracking** → `hasTriggeredWelcome = true` prevents duplicates

#### Returning Users (Incomplete Progress)
1. **Login** → Main form immediately (`isRegistered = true`) → **TRANSITION DETECTED**
2. **Progress check** → If < 100%, modal shows
3. **Session tracking** → `hasTriggeredWelcome = true` prevents duplicates

#### Returning Users (Complete Progress)
1. **Login** → Main form immediately (`isRegistered = true`) → **TRANSITION DETECTED**
2. **Progress check** → 100% complete, modal doesn't show
3. **Session tracking** → `hasTriggeredWelcome = true` for consistency

#### Page Refresh Behaviour
1. **Page refresh** → `hasTriggeredWelcome` resets to `false` (component state)
2. **No transition** → `isRegistered` doesn't change (stays `true`)
3. **No modal** → Transition detection doesn't trigger

## Progress Calculation System

### Progress Components (25% each)
1. **Household Registration** (25%) - Always complete after login
2. **Hobbies** (25%) - Household head has hobby field filled
3. **Sacraments** (25%) - Household head has sacrament records
4. **Family Members** (25%) - Household has >1 member OR clicked "Add Member"

### Real-Time Updates
Progress updates automatically when users:
- Add/edit/delete household members
- Update hobby information
- Add/edit/delete sacraments
- Click "Add Member" button (tracks intent)

### API Integration
```typescript
// Progress API endpoint
GET /api/census/progress

// Returns:
{
  "success": true,
  "data": {
    "progress": 75,
    "completionState": {
      "householdRegistration": true,
      "hobbies": true,
      "sacraments": true,
      "familyMembers": false
    }
  }
}
```

## Security & Performance

### Environment-Aware Debugging
- Debug logs only appear in development (`process.env.NODE_ENV === 'development'`)
- Production builds contain no debug output
- Sensitive data never logged

### Performance Optimisations
- Lazy-loaded modal component
- Progress caching with localStorage
- Event-driven updates (no polling)
- SSR-compatible hydration
- Abort controller for API request cleanup

### Security Measures
- Authentication-gated API calls
- Session validation for progress data
- Cross-session cache pollution prevention
- Proper error handling without data exposure

## Future Compatibility

### Planned Catholic Screening Flow
```
Login → Catholic Screening → Registration → Main Census Form
```

**Impact Assessment:**
- ✅ **No changes needed** - Catholic screening occurs before registration
- ✅ **Transition point unchanged** - `isRegistered` still transitions at same moment
- ✅ **Progress calculation adaptable** - Can exclude sacraments for non-Catholics
- ✅ **Modal timing preserved** - Shows when reaching main form regardless of Catholic status

### Extensibility Points
- Progress calculation can be modified for different user types
- Modal content can be customized based on user status
- Additional screening steps can be added before registration
- Transition detection works with any pre-registration flow

## Testing Strategy

### Manual Testing Scenarios
1. **First-time registration** → Modal should show after registration completion
2. **Returning user (incomplete)** → Modal should show on login
3. **Returning user (complete)** → Modal should not show
4. **Page refresh** → Modal should not show again
5. **Permanent dismissal** → Modal should never show again
6. **Progress regression** → Modal should show on next login after member deletion

### Debug Information
Enable development mode to see transition detection logs:
```bash
NODE_ENV=development npm run dev
```

Look for console messages:
- `Welcome modal check - Progress: X, isDismissed: Y`
- `Showing welcome modal - Progress: X`

## Troubleshooting

### Modal Shows at 100% Progress
- Check progress loading state - ensure `isProgressLoading` is false
- Verify progress calculation accuracy
- Check for race conditions between progress loading and transition detection

### Modal Shows Multiple Times
- Verify `hasTriggeredWelcome` state management
- Check for component remounting issues
- Ensure useEffect dependencies are correct

### Modal Doesn't Show for Returning Users
- Verify `isRegistered` state initialization from session data
- Check progress calculation for returning users
- Ensure transition detection useEffect is running

### Performance Issues
- Monitor API call frequency
- Check for memory leaks in progress provider
- Verify abort controller cleanup

## Code Locations

### Primary Implementation
- **Main Logic**: `app/[locale]/census/[code]/census-form-client.tsx`
- **Modal Hook**: `src/components/census/welcome-modal/useWelcomeModal.ts`
- **Modal Component**: `src/components/census/welcome-modal/CensusWelcomeModal.tsx`
- **Progress Hook**: `src/hooks/use-census-progress.tsx`
- **Progress API**: `app/api/census/progress/route.ts`

### Supporting Files
- **Modal Content**: `src/components/census/welcome-modal/WelcomeModalContent.tsx`
- **Progress Types**: `src/types/census-progress.ts`
- **Validation**: `lib/validation/client/census-client.ts`

## Implementation Examples

### Complete Integration Example
```typescript
// In census-form-client.tsx
export function CensusFormClient({ code, initialSessionData }: CensusFormClientProps) {
  // Progress tracking with loading state
  const { refreshProgress, progress, isLoading: isProgressLoading } = useCensusProgress();

  // Welcome modal management
  const { isOpen: isWelcomeOpen, setIsOpen: setWelcomeOpen, isDismissed, dismissModal } = useWelcomeModal();

  // Registration and session state
  const [isRegistered, setIsRegistered] = useState<boolean>(!!initialSessionData.householdId);
  const [hasTriggeredWelcome, setHasTriggeredWelcome] = useState(false);

  // Transition detection with progress validation
  useEffect(() => {
    if (isProgressLoading) return; // Wait for accurate progress

    if (isRegistered && !hasTriggeredWelcome) {
      if (!isDismissed && progress < 100) {
        setWelcomeOpen(true);
      }
      setHasTriggeredWelcome(true);
    }
  }, [isRegistered, hasTriggeredWelcome, isDismissed, progress, isProgressLoading, setWelcomeOpen]);

  // Registration completion handler
  const handleRegistrationComplete = async () => {
    setIsRegistered(true); // Triggers transition detection
    await updateSession();
    // Note: Modal trigger removed - handled by transition detection
  };

  return (
    <div>
      {!isRegistered ? (
        <HouseholdRegistrationForm onRegistrationComplete={handleRegistrationComplete} />
      ) : (
        <div>
          {/* Main census form content */}
          <Suspense fallback={<div>Loading...</div>}>
            <CensusWelcomeModal
              isOpen={isWelcomeOpen}
              onClose={() => setWelcomeOpen(false)}
              onDismiss={dismissModal}
            />
          </Suspense>
        </div>
      )}
    </div>
  );
}
```

### Progress Hook Usage
```typescript
// Event-driven progress updates
const handleAddMember = async (data) => {
  // ... member creation logic
  await refreshProgress(); // Trigger real-time update
  showSuccess('memberAdded');
};

const handleEditMember = async (data) => {
  // ... member update logic
  await refreshProgress(); // Trigger real-time update
  showSuccess('memberUpdated');
};
```

### Modal Hook Implementation
```typescript
// useWelcomeModal.ts
export function useWelcomeModal() {
  const [isOpen, setIsOpen] = useState(false);
  const [isDismissed, setIsDismissed] = useState(() => {
    if (typeof window === 'undefined') return false;
    return localStorage.getItem('census-welcome-dismissed') === 'true';
  });

  const dismissModal = useCallback(() => {
    setIsOpen(false);
    setIsDismissed(true);
    localStorage.setItem('census-welcome-dismissed', 'true');
  }, []);

  return { isOpen, setIsOpen, isDismissed, dismissModal };
}
```

## Migration Notes

### From Previous Implementation
If migrating from a simpler modal system:

1. **Remove manual modal triggers** from registration completion
2. **Add transition detection logic** with progress validation
3. **Implement session-based prevention** with `hasTriggeredWelcome`
4. **Add progress loading checks** to prevent race conditions
5. **Update modal component** to use responsive design pattern

### Breaking Changes
- Modal no longer shows immediately after registration (waits for progress loading)
- Progress-based display logic replaces simple dismissal logic
- Session-based prevention replaces page-based prevention

## Best Practices

### Development Guidelines
1. **Always check `isProgressLoading`** before making modal decisions
2. **Use environment-aware debugging** for troubleshooting
3. **Implement proper cleanup** in useEffect hooks
4. **Test all user flow scenarios** thoroughly
5. **Monitor performance** with React DevTools

### Code Quality Standards
- TypeScript strict mode compliance
- Comprehensive error handling
- Accessible component design
- Mobile-first responsive implementation
- Clean separation of concerns

### Testing Recommendations
- Unit tests for hook logic
- Integration tests for user flows
- E2E tests for critical paths
- Performance testing for progress calculations
- Accessibility testing for modal components

## Conclusion

The Welcome Modal System provides a robust, user-friendly onboarding experience that adapts to different user scenarios while maintaining high performance and security standards. The transition-based detection ensures the modal appears at the optimal moment, while real-time progress tracking ensures relevance and prevents unnecessary interruptions.

This implementation serves as a foundation for future enhancements and can be easily extended to support additional user flows and requirements.

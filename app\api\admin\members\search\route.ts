import { type NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { getTranslations } from 'next-intl/server';
import { z } from 'zod/v4';
import { authOptions } from '@/lib/auth/auth-options';
import { getMembersWithDetails } from '@/lib/db/members';
import { getLocaleFromCookies } from '@/lib/utils/server-messages';

// Validation schema for search parameters
const searchParamsSchema = z.object({
  searchTerm: z.string().optional(),
  gender: z.string().optional(),
  relationship: z.string().optional(),
  censusYearId: z.coerce.number().optional(),
  sacramentStatus: z.string().optional(),
  page: z.coerce.number().min(1).default(1),
  pageSize: z.coerce.number().min(1).max(100).default(20),
  sortBy: z.string().default('memberId'),
  sortOrder: z.enum(['asc', 'desc']).default('asc'),
});

/**
 * GET /api/admin/members/search
 *
 * Direct database search for members without caching
 * Used by client-side components for real-time search and filtering
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Require admin authentication
    if (!session || session.user.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse and validate query parameters
    const { searchParams } = new URL(request.url);
    const params = searchParamsSchema.parse({
      searchTerm: searchParams.get('searchTerm') || undefined,
      gender: searchParams.get('gender') || undefined,
      relationship: searchParams.get('relationship') || undefined,
      censusYearId: searchParams.get('censusYearId') || undefined,
      sacramentStatus: searchParams.get('sacramentStatus') || undefined,
      page: searchParams.get('page') || undefined,
      pageSize: searchParams.get('pageSize') || undefined,
      sortBy: searchParams.get('sortBy') || undefined,
      sortOrder: searchParams.get('sortOrder') || undefined,
    });

    // Direct database access without caching for real-time results
    const result = await getMembersWithDetails({
      searchTerm: params.searchTerm,
      gender: params.gender,
      relationship: params.relationship,
      censusYearId: params.censusYearId,
      sacramentStatus: params.sacramentStatus,
      page: params.page,
      pageSize: params.pageSize,
      sortBy: params.sortBy,
      sortOrder: params.sortOrder,
    });

    // Calculate pagination metadata
    const totalPages = Math.ceil(result.total / params.pageSize);

    return NextResponse.json({
      members: result.members,
      pagination: {
        page: params.page,
        pageSize: params.pageSize,
        total: result.total,
        totalPages,
      },
    });
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
    }

    const locale = await getLocaleFromCookies();
    const t = await getTranslations({ locale, namespace: 'admin' });

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: t('invalidParameters'), details: error.issues },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: t('memberFetchFailed') },
      { status: 500 }
    );
  }
}

import type { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import { requireAdmin } from '@/lib/auth/auth-utils';
import { getCensusYears } from '@/lib/db/census-years';
import {
  getHouseholdsCount,
  getHouseholdsWithDetails,
} from '@/lib/db/households';
import { HouseholdClient } from './household-client';

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations('metadata');

  return {
    title: t('householdManagementTitle'),
    description: t('householdManagementDescription'),
  };
}

export default async function HouseholdPage() {
  // Server-side authentication check
  await requireAdmin();

  // Fetch census years for the filter dropdown
  const censusYears = await getCensusYears();

  // Fetch initial household data directly from database
  // This provides immediate data without API calls and cache delays
  const pageSize = 20;
  const initialHouseholds = await getHouseholdsWithDetails({
    limit: pageSize,
    offset: 0,
    searchTerm: '',
    censusYearId: undefined,
  });

  // Get total count for pagination
  const totalHouseholds = await getHouseholdsCount();

  return (
    <HouseholdClient
      censusYears={censusYears}
      initialHouseholds={initialHouseholds}
      initialPageSize={pageSize}
      initialTotal={totalHouseholds}
    />
  );
}

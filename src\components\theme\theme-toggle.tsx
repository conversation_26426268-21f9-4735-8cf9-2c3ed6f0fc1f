'use client';

import { <PERSON>, <PERSON> } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useTheme } from 'next-themes';
import * as React from 'react';
import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';

interface ThemeToggleProps {
  variant?: 'default' | 'sidebar' | 'header';
}

export function ThemeToggle({ variant = 'default' }: ThemeToggleProps) {
  const { theme, setTheme } = useTheme();
  const [mounted, setMounted] = useState(false);
  const tCommon = useTranslations('common');
  const tNavigation = useTranslations('navigation');

  // useEffect to handle hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  const toggleTheme = () => {
    setTheme(theme === 'light' ? 'dark' : 'light');
  };

  // Prevent hydration mismatch
  if (!mounted) {
    return (
      <Button
        aria-label={tCommon('toggleTheme')}
        className={
          variant === 'sidebar'
            ? 'rounded-md hover:bg-sidebar-accent hover:text-sidebar-accent-foreground'
            : variant === 'header'
              ? 'h-8 w-8 rounded-full hover:bg-slate-100 dark:hover:bg-slate-800'
              : 'rounded-full'
        }
        size="icon"
        style={{ cursor: 'pointer' }}
        variant="ghost"
      >
        <div className="h-[1.2rem] w-[1.2rem]" />
        <span className="sr-only">{tCommon('toggleTheme')}</span>
      </Button>
    );
  }

  return (
    <Button
      aria-label={tCommon('toggleTheme')}
      className={
        variant === 'sidebar'
          ? 'rounded-md hover:bg-sidebar-accent hover:text-sidebar-accent-foreground'
          : variant === 'header'
            ? 'h-8 w-8 rounded-full hover:bg-muted'
            : 'rounded-full'
      }
      onClick={toggleTheme}
      size="icon"
      style={{ cursor: 'pointer' }}
      variant="ghost"
    >
      <Sun className="dark:-rotate-90 h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:scale-0" />
      <Moon className="absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
      <span className="sr-only">
        {theme === 'dark' ? tNavigation('darkMode') : tNavigation('lightMode')}
      </span>
    </Button>
  );
}

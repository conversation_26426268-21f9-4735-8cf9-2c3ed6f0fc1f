/**
 * Success message translation mappings
 * Maps success operation codes to translation keys in the 'notifications' namespace
 * Used by useMessage hook and server-side translation routes
 */

export const successMessageKeys: Record<string, string> = {
  // Household operations
  householdRegistered: 'householdRegisteredSuccessfully',
  householdUpdated: 'householdUpdatedSuccessfully',
  householdDeleted: 'householdDeletedSuccessfully',

  // Member operations
  memberAdded: 'memberAddedSuccessfully',
  memberUpdated: 'memberUpdatedSuccessfully',
  memberDeleted: 'memberDeletedSuccessfully',
  memberAndHouseholdDeleted: 'memberAndHouseholdDeletedSuccessfully',

  // Bulk operations
  bulkDeleteSuccess: 'bulkDeleteSuccess',
  bulkDeletePartialSuccess: 'bulkDeletePartialSuccess',

  // Member-specific bulk operations
  bulkDeleteMembersSuccess: 'bulkDeleteMembersSuccess',
  bulkDeleteMembersPartialSuccess: 'bulkDeleteMembersPartialSuccess',

  // Code operations
  codesGenerated: 'successfullyGeneratedUniqueCodes',
  codesDeleted: 'successfullyDeletedUniqueCodes',
  codesPrepared: 'preparedCodesForPrinting',

  // Settings operations
  settingsUpdated: 'settingsUpdatedSuccessfully',
  churchInfoUpdated: 'churchInformationUpdatedSuccessfully',
  censusYearUpdated: 'censusYearUpdatedSuccessfully',
  censusControlsUpdated: 'censusControlsUpdatedSuccessfully',
  rateLimitSettingsUpdated: 'rateLimitSettingsUpdatedSuccessfully',
  siteUrlUpdated: 'siteUrlUpdatedSuccessfully',
  passwordChanged: 'passwordChangedSuccessfully',
  twoFactorEnabled: 'twoFactorEnabled',
  twoFactorDisabled: 'twoFactorAuthenticationDisabled',
  twoFactorSettingsUpdated: 'twoFactorSettingsUpdated',
  qrCodeGenerated: 'qrCodeGenerated',
  backupCodesGenerated: 'backupCodesGenerated',
  backupCodesCopied: 'backupCodesCopied',

  // Database operations
  backupCreated: 'backupCreated',
  connectionReset: 'connectionReset',
  databaseImported: 'databaseImported',

  // Census operations
  censusCodeValidated: 'censusCodeValidated',
  registrationComplete: 'registrationComplete',
  accountDeleted: 'accountDeleted',
  formCompletedSuccessfully: 'formCompletedSuccessfully',
  formSavedSuccessfully: 'formSavedSuccessfully',

  // Sacrament operations
  sacramentAdded: 'sacramentAddedSuccessfully',
  sacramentUpdated: 'sacramentUpdatedSuccessfully',
  sacramentDeleted: 'sacramentDeletedSuccessfully',

  // Export operations
  chartExported: 'chartExportedSuccessfully',
  dataExported: 'dataExportedSuccessfully',

  // Copy operations
  uniqueCodeCopied: 'uniqueCodeCopied',

  // Authentication operations
  loginSuccessful: 'loginSuccessful',

  // Default fallback
  default: 'operationCompleted',
};

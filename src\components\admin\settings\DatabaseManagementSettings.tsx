'use client';

import { AlertCircle, Download } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod/v4';
import { FormFieldWithTooltip } from '@/components/admin/settings/FormFieldWithTooltip';
import { SettingsCard } from '@/components/admin/settings/SettingsCard';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useFormSubmit } from '@/hooks/useFormSubmit';
import { useMessage } from '@/hooks/useMessage';
import { formatForFilename } from '@/lib/utils/date-time';
import { zodResolver } from '@/lib/utils/zod-resolver-compat';

// Define schema for export form
const createExportFormSchema = (tValidation: any) =>
  z.object({
    exportFormat: z
      .string()
      .min(1, { message: tValidation('exportFormatRequired') }),
    exportTables: z
      .string()
      .min(1, { message: tValidation('tablesSelectionRequired') }),
  });

type ExportFormValues = z.infer<ReturnType<typeof createExportFormSchema>>;

// Define schema for import form
const importFormSchema = z.object({
  importFile: z.any().optional(),
});

type ImportFormValues = z.infer<typeof importFormSchema>;

export function DatabaseManagementSettings() {
  const t = useTranslations('admin');
  const tCommon = useTranslations('common');
  const tValidation = useTranslations('validation');
  const tErrors = useTranslations('errors');
  const { showSuccess, showError } = useMessage();
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);
  const [importWarnings, setImportWarnings] = useState<string[]>([]);
  const [importDetails, setImportDetails] = useState<{
    tables: string[];
    recordCount: number;
  } | null>(null);

  // Create schema with translations
  const exportFormSchema = createExportFormSchema(tValidation);

  // Export form
  const exportForm = useForm<ExportFormValues>({
    resolver: zodResolver(exportFormSchema),
    defaultValues: {
      exportFormat: 'sql',
      exportTables: 'all',
    },
  });

  // Import form
  const importForm = useForm<ImportFormValues>({
    resolver: zodResolver(importFormSchema),
    defaultValues: {
      importFile: undefined,
    },
  });

  // Use our custom hook for export form submission
  const { handleSubmit: submitExport, isSubmitting: isExporting } =
    useFormSubmit<ExportFormValues>({
      onSubmit: async (data) => {
        try {
          // Call the export API
          const response = await fetch('/api/database/export', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              format: data.exportFormat,
              tables: data.exportTables,
            }),
          });

          if (!response.ok) {
            showError('UpdateFailed', 'settings');
            return {
              success: false,
              suppressAlert: true,
            };
          }

          // Handle different response types
          if (data.exportFormat === 'json') {
            // For JSON, we need to handle the response as JSON
            const jsonData = await response.json();

            // Create a Blob and download it
            const blob = new Blob([JSON.stringify(jsonData, null, 2)], {
              type: 'application/json',
            });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `wsccc_census_${data.exportTables}_${formatForFilename(new Date())}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
          } else {
            // For SQL and CSV (ZIP), we can use the blob directly
            const blob = await response.blob();
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;

            // Get filename from Content-Disposition header if available
            const contentDisposition = response.headers.get(
              'Content-Disposition'
            );
            let filename = `wsccc_census_${data.exportTables}_${formatForFilename(new Date())}.${data.exportFormat}`;

            if (contentDisposition) {
              const filenameMatch = contentDisposition.match(/filename="(.+)"/);
              if (filenameMatch && filenameMatch[1]) {
                filename = filenameMatch[1];
              }
            }

            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
          }

          // Success - use centralized alert system
          showSuccess('dataExported');
          return {
            success: true,
            suppressAlert: true,
          };
        } catch (error) {
          console.error('Export error:', error);
          showError('UpdateFailed', 'settings');
          return {
            success: false,
            suppressAlert: true,
          };
        }
      },
    });

  // Validate import before submission
  const validateImport = async () => {
    // Reset warnings and details
    setImportWarnings([]);
    setImportDetails(null);

    // Show confirmation dialogue
    setIsConfirmDialogOpen(true);
  };

  // Handle actual import submission after confirmation
  const handleImportSubmit = async () => {
    setIsConfirmDialogOpen(false);

    const importFile = importForm.getValues().importFile;
    if (!importFile) {
      showError('NoFileSelected');
      return;
    }

    try {
      // Create form data for file upload
      const formData = new FormData();
      formData.append('file', importFile);

      // Call the import API
      const response = await fetch('/api/database/import', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();

      if (!(response.ok && result.success)) {
        showError('DatabaseImportFailed');
        return;
      }

      // Store warnings and details for display
      if (result.warnings && result.warnings.length > 0) {
        setImportWarnings(result.warnings);
      }

      setImportDetails({
        tables: result.tables || [],
        recordCount: result.recordCount || 0,
      });

      showSuccess('databaseImported');

      // Reset the form
      importForm.reset();
    } catch (error) {
      console.error('Import error:', error);
      showError('DatabaseImportFailed');
    }
  };

  // Use our custom hook for import form submission
  const { isSubmitting: isImporting } = useFormSubmit<ImportFormValues>({
    onSubmit: async () => {
      // This is just a placeholder as we're handling the submission manually
      return { success: true };
    },
  });

  // Handle file input change
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      importForm.setValue('importFile', file);
    }
  };

  return (
    <div className="space-y-6">
      {/* Backup & Export Card */}
      <SettingsCard
        description={t('createBackupForSafekeeping')}
        form={exportForm}
        isSubmitting={isExporting}
        onFormSubmit={submitExport}
        saveButtonText={t('exportDatabase')}
        title={t('backupExport')}
      >
        <div className="space-y-6">
          <div className="grid gap-4 sm:grid-cols-2">
            <FormFieldWithTooltip
              id="exportFormat"
              label={t('exportFormat')}
              tooltip={t('chooseFormatForExport')}
            >
              <Select
                onValueChange={(value) =>
                  exportForm.setValue('exportFormat', value, {
                    shouldValidate: true,
                  })
                }
                value={exportForm.watch('exportFormat')}
              >
                <SelectTrigger id="exportFormat">
                  <SelectValue placeholder={t('selectFormat')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="sql">SQL</SelectItem>
                  <SelectItem value="csv">CSV</SelectItem>
                  <SelectItem value="json">JSON</SelectItem>
                </SelectContent>
              </Select>
            </FormFieldWithTooltip>

            <FormFieldWithTooltip
              id="exportTables"
              label={t('tablesToExport')}
              tooltip={t('selectTablesToInclude')}
            >
              <Select
                onValueChange={(value) =>
                  exportForm.setValue('exportTables', value, {
                    shouldValidate: true,
                  })
                }
                value={exportForm.watch('exportTables')}
              >
                <SelectTrigger id="exportTables">
                  <SelectValue placeholder={t('selectTables')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t('allTables')}</SelectItem>
                  <SelectItem value="members">{t('membersOnly')}</SelectItem>
                  <SelectItem value="households">
                    {t('householdsOnly')}
                  </SelectItem>
                  <SelectItem value="settings">{t('settingsOnly')}</SelectItem>
                </SelectContent>
              </Select>
            </FormFieldWithTooltip>
          </div>

          <div className="flex items-start space-x-2 rounded-md border border-blue-200 bg-blue-50 p-3 dark:border-blue-800 dark:bg-blue-950/50">
            <Download className="mt-0.5 h-5 w-5 text-blue-600 dark:text-blue-400" />
            <div className="text-blue-800 text-sm dark:text-blue-300">
              <p className="font-medium">{t('exportInformation')}</p>
              <p>{t('exportFormatDescription')}</p>
            </div>
          </div>
        </div>
      </SettingsCard>

      {/* Import Data Card */}
      <SettingsCard
        description={t('importDataFromBackup')}
        footerContent={
          <Button
            disabled={isImporting || !importForm.getValues().importFile}
            onClick={() => {
              const importFile = importForm.getValues().importFile;
              if (importFile) {
                validateImport();
              } else {
                showError('NoFileSelected');
              }
            }}
          >
            {isImporting ? t('importing') : t('importDatabase')}
          </Button>
        }
        saveButtonText={t('importDatabase')}
        title={t('importData')}
      >
        <div className="space-y-6">
          <FormFieldWithTooltip
            id="importFile"
            label={t('selectBackupFile')}
            tooltip={t('chooseBackupFileToImport')}
          >
            <Input
              accept=".sql,.csv,.json,.zip"
              id="importFile"
              onChange={handleFileChange}
              type="file"
            />
          </FormFieldWithTooltip>

          <div className="flex items-start space-x-2 rounded-md border border-amber-200 bg-amber-50 p-3 dark:border-amber-800 dark:bg-amber-950/50">
            <AlertCircle className="mt-0.5 h-5 w-5 text-amber-600 dark:text-amber-400" />
            <div className="text-amber-800 text-sm dark:text-amber-300">
              <p className="font-medium">{tCommon('warning')}</p>
              <p>{t('importWillOverwriteWarning')}</p>
            </div>
          </div>

          {importWarnings.length > 0 && (
            <div className="mt-4 space-y-2">
              <h4 className="font-medium text-sm">
                {tCommon('importWarnings')}
              </h4>
              <ul className="list-disc space-y-1 pl-5 text-amber-600 text-sm dark:text-amber-400">
                {importWarnings.map((warning, index) => (
                  <li key={index}>{warning}</li>
                ))}
              </ul>
            </div>
          )}

          {importDetails && (
            <div className="mt-4 rounded-md border border-green-200 bg-green-50 p-3 dark:border-green-800 dark:bg-green-950/50">
              <h4 className="font-medium text-green-800 text-sm dark:text-green-300">
                {tCommon('importSummary')}
              </h4>
              <p className="mt-1 text-green-700 text-sm dark:text-green-400">
                {importDetails.recordCount} records imported across{' '}
                {importDetails.tables.length} tables.
              </p>
            </div>
          )}
        </div>
      </SettingsCard>

      {/* Import Confirmation Dialog */}
      <Dialog onOpenChange={setIsConfirmDialogOpen} open={isConfirmDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{tCommon('confirmDatabaseImport')}</DialogTitle>
            <DialogDescription>
              {t('importWillOverwriteWarning')}
            </DialogDescription>
          </DialogHeader>

          <div className="my-2 rounded-md border border-amber-200 bg-amber-50 p-3 dark:border-amber-800 dark:bg-amber-950/50">
            <p className="font-medium text-amber-800 text-sm dark:text-amber-300">
              A backup of your current database will be created automatically
              before import.
            </p>
          </div>

          <DialogFooter>
            <Button
              onClick={() => setIsConfirmDialogOpen(false)}
              variant="outline"
            >
              {tCommon('cancel')}
            </Button>
            <Button onClick={handleImportSubmit} variant="destructive">
              {t('yesImportDatabase')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

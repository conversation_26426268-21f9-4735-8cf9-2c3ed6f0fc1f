/**
 * Input Validation and Security Module
 *
 * This module provides input validation and prompt injection detection
 * to protect against malicious user inputs.
 */

// Fast prompt injection detection (before AI analysis)
export function detectPromptInjection(userMessage: string): boolean {
  const suspiciousPatterns = [
    /ignore.*(previous|above|system|earlier).*(instruction|prompt|rule)/i,
    /set.*dataType.*to/i,
    /return.*admin|user|session|setting/i,
    /dataType.*['"]\s*(admin|user|session|setting)/i,
    /override.*security|bypass.*validation/i,
    /execute.*sql|drop.*table|delete.*from/i,
    /system.*prompt|assistant.*instructions/i,
  ];

  return suspiciousPatterns.some((pattern) => pattern.test(userMessage));
}

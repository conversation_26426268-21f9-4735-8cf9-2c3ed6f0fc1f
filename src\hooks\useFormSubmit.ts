import { useState } from 'react';
import { useAlert } from '@/contexts/AlertContext';

interface FormSubmitOptions<TFormValues> {
  /**
   * The function that will be called when the form is submitted.
   * This should handle the actual submission logic (API calls, etc.)
   */
  onSubmit: (data: TFormValues) => Promise<{
    success: boolean;
    message?: string;
    suppressAlert?: boolean;
  }>;

  /**
   * Default success message to show when the submission is successful.
   * This can be overridden by the message returned from onSubmit.
   */
  defaultSuccessMessage?: string;

  /**
   * Default error message to show when the submission fails.
   * This can be overridden by the message returned from onSubmit.
   */
  defaultErrorMessage?: string;

  /**
   * Callback to run after a successful submission.
   * Receives the result object from onSubmit.
   */
  onSuccess?: (result: {
    success: boolean;
    message?: string;
    suppressAlert?: boolean;
  }) => void;

  /**
   * Callback to run when an error occurs during submission.
   * If provided, this will be called instead of showing an error alert.
   * The callback receives either:
   * 1. The error result object from onSubmit (when submission fails with success: false)
   * 2. The exception thrown during submission (usually an Error instance)
   */
  onError?: (
    error:
      | Error
      | { success: boolean; message?: string; suppressAlert?: boolean }
  ) => void;
}

/**
 * A custom hook for handling form submissions with loading state and alerts.
 */
export function useFormSubmit<TFormValues>({
  onSubmit,
  defaultSuccessMessage = 'Settings saved successfully',
  defaultErrorMessage = 'Failed to save settings',
  onSuccess,
  onError,
}: FormSubmitOptions<TFormValues>) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { showAlert } = useAlert();

  // The submit handler that will be passed to the form
  const handleSubmit = async (data: TFormValues) => {
    setIsSubmitting(true);

    try {
      const result = await onSubmit(data);

      if (result.success) {
        // Show success alert unless suppressAlert is true
        if (!result.suppressAlert) {
          showAlert('success', result.message || defaultSuccessMessage);
        }
        // Pass the result to the onSuccess callback
        if (onSuccess) {
          onSuccess(result);
        }
      } else {
        // If onError callback is provided, use it instead of showing an alert
        if (onError) {
          onError(
            result.success === false
              ? result
              : { success: false, message: result.message || 'Unknown error' }
          );
        }
        // Otherwise, show error alert unless suppressAlert is true
        else if (!result.suppressAlert) {
          showAlert('error', result.message || defaultErrorMessage);
        }
      }

      return result;
    } catch (error) {
      console.error('Form submission error:', error);

      // If onError callback is provided, use it instead of showing an alert
      if (onError) {
        onError(error as Error);
      } else {
        const errorMessage =
          error instanceof Error ? error.message : defaultErrorMessage;
        showAlert('error', errorMessage);
      }

      throw error;
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    isSubmitting,
    handleSubmit,
  };
}

'use client';

import { Loader2, Lock, ScanQrCode } from 'lucide-react';
// Dynamic import to prevent SSR issues - no loading component for better UX
import dynamic from 'next/dynamic';
import { useTranslations } from 'next-intl';
import React, { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

const QrScannerModal = dynamic(
  () =>
    import('@/components/ui/qr-scanner').then((module) => ({
      default: module.QrScannerModal,
    })),
  {
    ssr: false,
  }
);

interface CountdownInputProps {
  isLocked: boolean;
  remainingTime: string;
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  onSubmit?: () => void;
  disabled?: boolean;
  className?: string;
  error?: boolean;
  showQrScanner?: boolean;
  onQrCodeScanned?: (code: string) => void;
}

/**
 * Professional countdown input component that transforms between normal input and lockout display
 * Features beautiful animations, professional styling, and optional QR code scanner
 */
export function CountdownInput({
  isLocked,
  remainingTime,
  placeholder,
  value = '',
  onChange,
  onSubmit,
  disabled = false,
  className,
  error = false,
  showQrScanner = true,
  onQrCodeScanned,
}: CountdownInputProps) {
  const t = useTranslations('common');
  const defaultPlaceholder = placeholder || t('enterUniqueCode');
  const [isQrScannerOpen, setIsQrScannerOpen] = useState(false);
  const [isCameraSupported, setIsCameraSupported] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const [detectionStatus, setDetectionStatus] = useState<
    'checking' | 'complete'
  >('checking');

  // Clear loading state when modal opens (truly responsive)
  useEffect(() => {
    if (isQrScannerOpen && isLoading) {
      // Small delay to ensure smooth transition
      const timer = setTimeout(() => setIsLoading(false), 100);
      return () => clearTimeout(timer);
    }
  }, [isQrScannerOpen, isLoading]);

  // Camera detection system
  React.useEffect(() => {
    const checkCameraSupport = async () => {
      try {
        setDetectionStatus('checking');

        // Check secure context (HTTPS/localhost requirement)
        const isSecureContext =
          window.isSecureContext ||
          window.location.protocol === 'https:' ||
          window.location.hostname === 'localhost' ||
          window.location.hostname === '127.0.0.1' ||
          window.location.hostname === '[::1]' ||
          window.location.hostname.startsWith('192.168.') ||
          window.location.hostname.startsWith('10.') ||
          window.location.hostname.startsWith('172.');

        if (!(isSecureContext && navigator.mediaDevices?.getUserMedia)) {
          setIsCameraSupported(false);
          setDetectionStatus('complete');
          return;
        }

        // Try to enumerate devices
        let videoInputs: MediaDeviceInfo[] = [];
        try {
          const devices = await navigator.mediaDevices.enumerateDevices();
          videoInputs = devices.filter(
            (device) => device.kind === 'videoinput'
          );
        } catch (error) {
          // Device enumeration failed, using fallback detection
        }

        // Platform detection
        const isMobile =
          /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
            navigator.userAgent
          );

        // Decision logic
        const cameraSupported =
          videoInputs.length > 0 || (isMobile && isSecureContext);

        setIsCameraSupported(cameraSupported);
        setDetectionStatus('complete');
      } catch (error) {
        setIsCameraSupported(false);
        setDetectionStatus('complete');
      }
    };

    checkCameraSupport();
  }, []);
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !isLocked && !disabled && onSubmit) {
      onSubmit();
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!isLocked && onChange) {
      onChange(e.target.value);
    }
  };

  const handleQrCodeScanned = (code: string) => {
    // Extract code from URL if it's a full URL
    let extractedCode = code;
    try {
      if (code.includes('http') && code.includes('code=')) {
        const url = new URL(code);
        const codeParam = url.searchParams.get('code');
        if (codeParam) {
          extractedCode = codeParam;
        }
      }
    } catch (error) {
      // Could not parse as URL, using raw code
    }

    if (onChange) {
      onChange(extractedCode);
    }
    if (onQrCodeScanned) {
      onQrCodeScanned(extractedCode);
    }
    setIsQrScannerOpen(false);
    setIsLoading(false); // Clear loading state when closing
  };

  const handleQrScannerClick = () => {
    if (!(isLocked || disabled)) {
      setIsLoading(true);
      setIsQrScannerOpen(true);
      // Loading state will be cleared when modal actually opens
      // No artificial delay - truly responsive
    }
  };

  if (isLocked) {
    return (
      <div
        aria-label={t('accountLocked', { time: remainingTime })}
        aria-readonly="true"
        className={cn(
          // Base styling exactly matching shadcn/ui input default variant
          'flex w-full min-w-0 border-input bg-transparent text-base outline-none transition-[color,box-shadow] selection:bg-primary selection:text-primary-foreground file:text-foreground placeholder:text-muted-foreground disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm dark:bg-input/30',
          // Default variant styling
          'h-9 rounded-md border px-3 py-1 shadow-xs focus-visible:border-ring focus-visible:ring-[3px] focus-visible:ring-ring/50',
          // Locked state styling
          'cursor-not-allowed bg-muted/50 text-muted-foreground',
          // Error state
          error &&
            'aria-invalid:border-destructive aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40',
          className
        )}
        role="textbox"
      >
        <div className="flex w-full items-center justify-center gap-2">
          <Lock
            aria-hidden="true"
            className="h-4 w-4 animate-pulse text-muted-foreground"
          />
          <span className="font-medium font-mono text-sm">
            {t('lockedRemaining', { time: remainingTime })}
          </span>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className={cn('relative', className)}>
        <input
          aria-invalid={error}
          aria-label={placeholder}
          autoComplete="off"
          className={cn(
            // Base styling exactly matching shadcn/ui input default variant
            'flex w-full min-w-0 border-input bg-transparent text-base outline-none transition-[color,box-shadow] selection:bg-primary selection:text-primary-foreground file:text-foreground placeholder:text-muted-foreground disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm dark:bg-input/30',
            // Default variant styling with padding adjustment for QR button
            'h-9 rounded-md border px-3 py-1 shadow-xs focus-visible:border-ring focus-visible:ring-[3px] focus-visible:ring-ring/50',
            // Add right padding when QR scanner is shown and supported
            showQrScanner && isCameraSupported && 'pr-10',
            // Error state
            error &&
              'aria-invalid:border-destructive aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40'
          )}
          disabled={disabled}
          onChange={handleChange}
          onKeyPress={handleKeyPress}
          placeholder={defaultPlaceholder}
          spellCheck="false"
          type="text"
          value={value}
        />

        {/* QR Scanner Button */}
        {showQrScanner &&
          detectionStatus === 'complete' &&
          isCameraSupported && (
            <Button
              className={cn(
                '-translate-y-1/2 absolute top-1/2 right-1 h-7 w-7 p-0 hover:bg-muted/50',
                (isLocked || disabled || isLoading) &&
                  'cursor-not-allowed opacity-50'
              )}
              disabled={isLocked || disabled || isLoading}
              onClick={handleQrScannerClick}
              size="sm"
              type="button"
              variant="ghost"
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <ScanQrCode className="h-4 w-4" />
              )}
            </Button>
          )}

        {/* Detection Loading State */}
        {showQrScanner && detectionStatus === 'checking' && (
          <div className="-translate-y-1/2 absolute top-1/2 right-1 flex h-7 w-7 items-center justify-center">
            <div className="h-3 w-3 animate-spin rounded-full border-2 border-muted-foreground border-t-transparent" />
          </div>
        )}
      </div>

      {/* QR Scanner Modal - Only render when actually opening */}
      {showQrScanner && isCameraSupported && isQrScannerOpen && (
        <QrScannerModal
          onOpenChange={setIsQrScannerOpen}
          onQrCodeScanned={handleQrCodeScanned}
          open={isQrScannerOpen}
        />
      )}
    </>
  );
}

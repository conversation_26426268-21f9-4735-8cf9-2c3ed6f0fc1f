'use client';

import {
  CartesianGrid,
  Cell,
  <PERSON><PERSON><PERSON>,
  <PERSON>atter<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  XAxis,
  <PERSON>A<PERSON>s,
} from 'recharts';
import { type ChartConfig, ChartContainer } from '@/components/ui/chart';
import { type BaseChartProps, registerChart } from './chart-registry';
import { CHART_CONFIGS, CHART_DEFAULTS, getChartColor } from './constants';
import { processScatterData } from './utils';

function ScatterChartComponent({
  data,
  isAnimationActive = true,
  className,
}: BaseChartProps) {
  const config = data.config || {};
  const { showGrid = true } = config;

  // Process data using utility function
  const processedData = processScatterData(data.data);

  // Get unique categories for legend
  const categories = Array.from(
    new Set(processedData.map((d) => d.category || 'default'))
  );

  // Create chart config for shadcn using theme approach
  const chartConfig: ChartConfig = categories.reduce((acc, category, index) => {
    const lightColors = [
      'oklch(0.646 0.222 41.116)', // chart-1
      'oklch(0.6 0.118 184.704)', // chart-2
      'oklch(0.398 0.07 227.392)', // chart-3
      'oklch(0.828 0.189 84.429)', // chart-4
      'oklch(0.769 0.188 70.08)', // chart-5
    ];
    const darkColors = [
      'oklch(0.488 0.243 264.376)', // chart-1
      'oklch(0.696 0.17 162.48)', // chart-2
      'oklch(0.769 0.188 70.08)', // chart-3
      'oklch(0.627 0.265 303.9)', // chart-4
      'oklch(0.645 0.246 16.439)', // chart-5
    ];

    acc[category] = {
      label: category,
      theme: {
        light: lightColors[index % 5],
        dark: darkColors[index % 5],
      },
    };
    return acc;
  }, {} as ChartConfig);

  // Custom tooltip
  const CustomTooltip = ({
    active,
    payload,
  }: {
    active?: boolean;
    payload?: Array<{ payload: (typeof processedData)[0] }>;
  }) => {
    if (active && payload && payload.length) {
      const data = payload[0]?.payload;
      if (
        !data ||
        data.x === undefined ||
        data.x === null ||
        data.y === undefined ||
        data.y === null
      ) {
        return null;
      }

      return (
        <div className="rounded-lg border border-slate-200 bg-white p-3 shadow-lg dark:border-slate-600 dark:bg-slate-800">
          <p className="mb-2 font-medium text-slate-900 dark:text-slate-100">
            {data.name || 'Data Point'}
          </p>
          <div className="space-y-1 text-sm">
            <p className="text-slate-600 dark:text-slate-400">
              X: <span className="font-medium">{data.x}</span>
            </p>
            <p className="text-slate-600 dark:text-slate-400">
              Y: <span className="font-medium">{data.y}</span>
            </p>
            {data.size && (
              <p className="text-slate-600 dark:text-slate-400">
                Size: <span className="font-medium">{data.size}</span>
              </p>
            )}
            {data.category && (
              <p className="text-slate-600 dark:text-slate-400">
                Category: <span className="font-medium">{data.category}</span>
              </p>
            )}
          </div>
        </div>
      );
    }
    return null;
  };

  return (
    <div className={`w-full ${className || ''}`}>
      <ChartContainer className="h-[400px] w-full" config={chartConfig}>
        <ScatterChart
          data={processedData}
          margin={CHART_DEFAULTS.MARGINS.DEFAULT}
        >
          {showGrid && (
            <CartesianGrid className="opacity-30" strokeDasharray="3 3" />
          )}
          <XAxis
            className="text-xs"
            dataKey="x"
            name={data.xKey || 'X'}
            tick={{ fontSize: 11 }}
            type="number"
          />
          <YAxis
            className="text-xs"
            dataKey="y"
            name={data.yKey || 'Y'}
            tick={{ fontSize: 11 }}
            type="number"
          />
          <Tooltip content={<CustomTooltip />} />

          {/* Render scatter points grouped by category */}
          {categories.map((category, categoryIndex) => {
            const categoryData = processedData.filter(
              (d) => (d.category || 'default') === category
            );
            return (
              <Scatter
                animationBegin={
                  categoryIndex * CHART_CONFIGS.SCATTER.CATEGORY_SPACING
                }
                animationDuration={CHART_DEFAULTS.ANIMATION_DURATION}
                data={categoryData}
                fill={`var(--color-${category})`}
                isAnimationActive={isAnimationActive}
                key={category}
                name={category}
              >
                {categoryData.map((entry, index) => (
                  <Cell
                    fill={entry.fill || `var(--color-${category})`}
                    key={`cell-${categoryIndex}-${index}`}
                  />
                ))}
              </Scatter>
            );
          })}
        </ScatterChart>
      </ChartContainer>

      {/* Legend */}
      {categories.length > 1 && (
        <div className="mt-4 flex flex-wrap justify-center gap-4 text-sm">
          {categories.map((category, index) => (
            <div className="flex items-center gap-2" key={category}>
              <div
                className="h-3 w-3 rounded-full"
                style={{ backgroundColor: getChartColor(index) }}
              />
              <span className="text-slate-700 dark:text-slate-300">
                {category}
              </span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

// Register the component
registerChart('scatter', ScatterChartComponent);

export default ScatterChartComponent;

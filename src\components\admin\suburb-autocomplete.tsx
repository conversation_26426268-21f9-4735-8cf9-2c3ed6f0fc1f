'use client';

import { Check, ChevronsUpDown, Loader2, MapPin } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useState } from 'react';
import {
  type Control,
  Controller,
  type FieldError,
  type FieldErrorsImpl,
  type FieldValues,
  type Merge,
  type Path,
} from 'react-hook-form';
import { ErrorBoundaryWrapper } from '@/components/shared/ErrorBoundaryWrapper';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import { DialogCompatiblePopoverContent } from '@/components/ui/dialog-compatible-popover';
import { Label } from '@/components/ui/label';
import { Popover, PopoverTrigger } from '@/components/ui/popover';
import {
  type SuburbOption,
  useAdminSuburbSearch,
} from '@/hooks/useAdminSuburbSearch';
import { cn } from '@/lib/utils';

// Proper TypeScript interface with FieldValues constraint
interface AdminSuburbAutocompleteProps<
  TFormValues extends FieldValues = FieldValues,
> {
  control: Control<TFormValues>;
  name: Path<TFormValues>;
  label: string;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  error?:
    | FieldError
    | Merge<FieldError, FieldErrorsImpl<Record<string, unknown>>>;
  className?: string;
}

export function AdminSuburbAutocomplete<
  TFormValues extends FieldValues = FieldValues,
>({
  control,
  name,
  label,
  placeholder,
  required = false,
  disabled = false,
  error,
  className,
}: AdminSuburbAutocompleteProps<TFormValues>) {
  const t = useTranslations();
  // UI state only (display logic)
  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  // Business logic handled by custom hook
  const { suburbs, isLoading, searchError, searchSuburbs, clearResults } =
    useAdminSuburbSearch();

  // Handle search query changes (display logic only)
  const handleSearchChange = (value: string) => {
    setSearchQuery(value);
    searchSuburbs(value);
  };

  // Handle selection (display logic only)
  const handleSelection = (
    suburb: SuburbOption,
    field: { onChange: (value: string) => void }
  ) => {
    field.onChange(suburb.displayName);
    setOpen(false);
    setSearchQuery('');
    clearResults();
  };

  return (
    <ErrorBoundaryWrapper>
      <div className={cn('grid gap-2', className)}>
        <Label className={error ? 'text-destructive' : ''} htmlFor={name}>
          {label}
          {required && <span className="ml-1 text-destructive">*</span>}
        </Label>

        <Controller
          control={control}
          name={name}
          render={({ field }) => (
            <Popover onOpenChange={setOpen} open={open}>
              <PopoverTrigger asChild>
                <Button
                  aria-expanded={open}
                  className={cn(
                    'justify-between',
                    error && 'border-destructive',
                    !field.value && 'text-muted-foreground'
                  )}
                  disabled={disabled}
                  role="combobox"
                  variant="outline"
                >
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 shrink-0" />
                    <span className="truncate">
                      {field.value || placeholder}
                    </span>
                  </div>
                  <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                </Button>
              </PopoverTrigger>
              <DialogCompatiblePopoverContent
                align="start"
                className="p-0"
                style={{ width: 'var(--radix-popover-trigger-width)' }}
              >
                <Command shouldFilter={false}>
                  <CommandInput
                    className="h-9"
                    onValueChange={handleSearchChange}
                    placeholder={t('forms.typeAtLeast2CharactersToSearch')}
                    value={searchQuery}
                  />
                  <CommandList className="max-h-[250px]">
                    {isLoading && (
                      <div className="flex items-center justify-center py-6">
                        <Loader2 className="h-4 w-4 animate-spin" />
                        <span className="ml-2 text-muted-foreground text-sm">
                          {t('common.searchingSuburbs')}
                        </span>
                      </div>
                    )}

                    {searchError && (
                      <div className="py-6 text-center text-destructive text-sm">
                        {searchError}
                      </div>
                    )}

                    {!(isLoading || searchError) &&
                      searchQuery.length >= 2 &&
                      suburbs.length === 0 && (
                        <CommandEmpty>
                          {t('common.noSuburbsFound')}
                        </CommandEmpty>
                      )}

                    {!(isLoading || searchError) && searchQuery.length < 2 && (
                      <div className="py-6 text-center text-muted-foreground text-sm">
                        {t('forms.typeAtLeast2CharactersToSearchSuburbs')}
                      </div>
                    )}

                    {!(isLoading || searchError) && suburbs.length > 0 && (
                      <CommandGroup>
                        {suburbs.map((suburb) => (
                          <CommandItem
                            className="flex cursor-pointer items-center gap-2"
                            key={suburb.id}
                            onSelect={() => handleSelection(suburb, field)}
                            value={suburb.displayName}
                          >
                            <Check
                              className={cn(
                                'h-4 w-4',
                                field.value === suburb.displayName
                                  ? 'opacity-100'
                                  : 'opacity-0'
                              )}
                            />
                            <MapPin className="h-4 w-4 text-muted-foreground" />
                            <span>{suburb.displayName}</span>
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    )}
                  </CommandList>
                </Command>
              </DialogCompatiblePopoverContent>
            </Popover>
          )}
        />

        {error && (
          <p className="text-destructive text-xs">
            {String(error.message || 'Validation error')}
          </p>
        )}
      </div>
    </ErrorBoundaryWrapper>
  );
}

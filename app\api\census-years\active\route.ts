import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { getTranslations } from 'next-intl/server';
import { authOptions } from '@/lib/auth/auth-options';
import { getActiveCensusYear } from '@/lib/db/census-years';
import { getLocaleFromCookies } from '@/lib/utils/server-messages';

export async function GET() {
  // Get locale from centralized utility at the beginning
  const locale = await getLocaleFromCookies();
  const t = await getTranslations({ locale, namespace: 'errors' });

  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Require admin authentication
    if (!session || session.user.role !== 'admin') {
      return NextResponse.json({ error: t('unauthorized') }, { status: 401 });
    }

    // Get active census year
    const activeCensusYear = await getActiveCensusYear();

    if (!activeCensusYear) {
      return NextResponse.json(
        { error: t('noActiveCensusYearFound') },
        { status: 404 }
      );
    }

    return NextResponse.json(activeCensusYear);
  } catch (_error) {
    // Environment-aware logging - only in development
    if (process.env.NODE_ENV === 'development') {
    }
    return NextResponse.json(
      { error: t('failedToFetchActiveCensusYear') },
      { status: 500 }
    );
  }
}

// Table CSV export functionality for analytics data

// Utility function to safely format CSV cells and prevent injection attacks
function formatSafeCSVCell(value: string): string {
  // Prevent CSV injection by checking for dangerous characters at the start
  const dangerousChars = /^[=+\-@\t\r]/;
  let sanitizedValue = value;

  if (dangerousChars.test(value)) {
    // Prepend a space to neutralize formula injection
    sanitizedValue = ` ${value}`;
  }

  // Escape quotes by doubling them and wrap in quotes
  return `"${sanitizedValue.replace(/"/g, '""')}"`;
}

export interface TableExportOptions {
  filename?: string;
  includeMetadata?: boolean;
  includeTimestamp?: boolean;
  customHeaders?: Record<string, string>;
  dateFormat?: 'iso' | 'australian' | 'us';
  phoneFormat?: 'raw' | 'formatted';
  organisationName?: string; // Add this option
}

export interface TableExportData {
  data: Record<string, unknown>[];
  title?: string;
  description?: string;
  queryType?: string;
  totalRecords?: number;
}

/**
 * Export table data to CSV format
 * Handles Australian date formats, phone numbers, and church-specific data
 */
export function exportTableToCSV(
  tableData: TableExportData,
  options: TableExportOptions = {}
): void {
  const {
    filename = 'table-data',
    includeMetadata = true,
    includeTimestamp = true,
    customHeaders = {},
    dateFormat = 'australian',
    phoneFormat = 'formatted',
    organisationName = 'Data Export', // Default fallback
  } = options;

  let csvContent = '';

  // Add metadata header if requested
  if (includeMetadata) {
    csvContent += `# ${organisationName}\n`;
    if (tableData.title) {
      csvContent += `# Title: ${tableData.title}\n`;
    }
    if (tableData.description) {
      csvContent += `# Description: ${tableData.description}\n`;
    }
    if (tableData.queryType) {
      csvContent += `# Query Type: ${tableData.queryType}\n`;
    }
    if (includeTimestamp) {
      csvContent += `# Exported: ${formatTimestamp(new Date(), dateFormat)}\n`;
    }
    csvContent += `# Total Records: ${tableData.totalRecords || tableData.data.length}\n`;

    // Add custom headers
    Object.entries(customHeaders).forEach(([key, value]) => {
      csvContent += `# ${key}: ${value}\n`;
    });

    csvContent += '\n';
  }

  if (tableData.data.length === 0) {
    csvContent += 'No data available\n';
  } else {
    // Get headers with deterministic order
    const headers =
      tableData.data.length > 0
        ? // take the key order of the first item to keep a stable header order
          Object.keys(tableData.data[0])
        : [];

    // fallback: merge in keys that do not appear in the first row
    for (const row of tableData.data) {
      for (const k of Object.keys(row)) {
        if (!headers.includes(k)) {
          headers.push(k);
        }
      }
    }

    // Add human-readable headers with proper formatting (protect against injection)
    const formattedHeaders = headers.map((header) => formatHeaderName(header));
    csvContent +=
      formattedHeaders.map((header) => formatSafeCSVCell(header)).join(',') +
      '\n';

    // Add data rows with proper formatting
    tableData.data.forEach((item) => {
      const row = headers.map((header) => {
        const value = item[header];
        return formatCellValue(value, header, { dateFormat, phoneFormat });
      });
      csvContent += `${row.join(',')}\n`;
    });
  }

  // Create and download file
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  downloadBlob(blob, `${filename}.csv`);
}

/**
 * Format header names to be more human-readable
 */
function formatHeaderName(header: string): string {
  const headerMappings: Record<string, string> = {
    firstName: 'First Name',
    lastName: 'Last Name',
    dateOfBirth: 'Date of Birth',
    mobilePhone: 'Mobile Phone',
    createdAt: 'Created Date',
    updatedAt: 'Updated Date',
    census_year: 'Census Year',
    householdId: 'Household ID',
    id: 'ID',
    sacrament_type: 'Sacrament Type',
    isAssigned: 'Is Assigned',
    assignedAt: 'Assigned Date',
    member_count: 'Member Count',
    sacrament_count: 'Sacrament Count',
  };

  return (
    headerMappings[header] ||
    header
      .split('_')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ')
  );
}

/**
 * Format cell values based on data type and column name
 */
function formatCellValue(
  value: unknown,
  columnName: string,
  options: { dateFormat: string; phoneFormat: string }
): string {
  if (value === null || value === undefined) {
    return '';
  }

  // Handle dates
  if (value instanceof Date || isDateString(value)) {
    const date = value instanceof Date ? value : new Date(value as string);
    return formatSafeCSVCell(formatDate(date, options.dateFormat));
  }

  // Handle phone numbers
  if (
    columnName.toLowerCase().includes('phone') ||
    columnName.toLowerCase().includes('mobile')
  ) {
    return formatPhoneNumber(String(value), options.phoneFormat);
  }

  // Handle booleans
  if (typeof value === 'boolean') {
    return formatSafeCSVCell(value ? 'Yes' : 'No');
  }

  // Handle numbers
  if (typeof value === 'number') {
    return formatSafeCSVCell(String(value));
  }

  // Handle all other values (including strings)
  return formatSafeCSVCell(String(value));
}

/**
 * Check if a value is a date string
 */
function isDateString(value: unknown): boolean {
  if (typeof value !== 'string') {
    return false;
  }
  const date = new Date(value);
  return !Number.isNaN(date.getTime()) && value.includes('-');
}

/**
 * Format date according to specified format
 */
function formatDate(date: Date, format: string): string {
  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const year = date.getFullYear();

  switch (format) {
    case 'australian':
      return `${day}/${month}/${year}`;
    case 'us':
      return `${month}/${day}/${year}`;
    default:
      return date.toISOString().split('T')[0];
  }
}

/**
 * Format timestamp for metadata
 */
function formatTimestamp(date: Date, format: string): string {
  const dateStr = formatDate(date, format);
  const time = date.toLocaleTimeString('en-AU', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  });
  return `${dateStr} ${time}`;
}

/**
 * Format phone numbers
 */
function formatPhoneNumber(phone: string, format: string): string {
  if (format === 'raw') {
    return formatSafeCSVCell(phone);
  }

  // Format Australian mobile numbers
  const cleaned = phone.replace(/\D/g, '');
  if (cleaned.length === 10 && cleaned.startsWith('0')) {
    const formatted = `${cleaned.substring(0, 4)} ${cleaned.substring(4, 7)} ${cleaned.substring(7)}`;
    return formatSafeCSVCell(formatted);
  }

  return formatSafeCSVCell(phone);
}

/**
 * Utility function to download blob
 */
function downloadBlob(blob: Blob, filename: string): void {
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  link.style.display = 'none';

  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);

  // Clean up the URL object
  setTimeout(() => URL.revokeObjectURL(url), 100);
}

/**
 * Generate filename based on query type and current date
 */
export function generateExportFilename(
  queryType?: string,
  customName?: string,
  includeTimestamp = true,
  prefix?: string
): string {
  const base = customName || queryType || 'census-data';
  const timestamp = includeTimestamp
    ? `-${new Date().toISOString().split('T')[0]}`
    : '';

  const filePrefix = prefix ? `${prefix}-` : '';
  return `${filePrefix}${base}${timestamp}`;
}

/**
 * Prepare data for export from various sources
 */
export function prepareTableDataForExport(
  data: Record<string, unknown>[],
  metadata?: {
    title?: string;
    description?: string;
    queryType?: string;
    totalRecords?: number;
  }
): TableExportData {
  return {
    data,
    title: metadata?.title,
    description: metadata?.description,
    queryType: metadata?.queryType,
    totalRecords: metadata?.totalRecords || data.length,
  };
}

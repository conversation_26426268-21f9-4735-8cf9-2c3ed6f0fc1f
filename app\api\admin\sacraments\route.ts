import { type NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { getTranslations } from 'next-intl/server';
import { authOptions } from '@/lib/auth/auth-options';
// import { ISacrament } from '@/types'; // Unused import
import { prisma } from '@/lib/db/prisma';
import {
  createSacrament,
  deleteSacrament,
  updateSacrament,
} from '@/lib/db/sacraments';
import { getTodayInSydney, startOfDay } from '@/lib/utils/date-time';
import { getLocaleFromCookies } from '@/lib/utils/server-messages';

/**
 * POST /api/admin/sacraments
 * Create a new sacrament for a member
 */
export async function POST(request: NextRequest) {
  // Get locale from centralized utility at the beginning
  const locale = await getLocaleFromCookies();
  const t = await getTranslations({ locale, namespace: 'errors' });
  const _tNotifications = await getTranslations({
    locale,
    namespace: 'notifications',
  });

  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    if (!session || session.user.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const data = await request.json();
    const { memberId, sacramentTypeId, date, place } = data;

    // Validate required fields
    if (!(memberId && sacramentTypeId)) {
      return NextResponse.json(
        {
          error: t('memberIdAndSacramentTypeRequired'),
        },
        { status: 400 }
      );
    }

    // Validate date is not in the future
    if (date) {
      const sacramentDate = startOfDay(new Date(date));
      const today = getTodayInSydney();
      if (sacramentDate > today) {
        return NextResponse.json(
          {
            error: t('sacramentDateCannotBeInFuture'),
          },
          { status: 400 }
        );
      }
    }

    // Get the active census year
    const activeCensusYear = await prisma.censusYear.findFirst({
      where: { isActive: true },
      select: { id: true },
    });

    if (!activeCensusYear) {
      return NextResponse.json(
        {
          error: t('noActiveCensusYearFound'),
        },
        { status: 400 }
      );
    }

    // Check if this sacrament type already exists for this member
    const existingSacrament = await prisma.sacrament.findFirst({
      where: {
        memberId,
        sacramentTypeId,
      },
    });

    if (existingSacrament) {
      return NextResponse.json(
        {
          error: t('sacramentTypeAlreadyRecorded'),
        },
        { status: 400 }
      );
    }

    // Create the sacrament
    const sacramentData = {
      memberId,
      sacramentTypeId,
      date: date ? new Date(date) : null,
      place: place || null,
      notes: null,
      censusYearId: activeCensusYear.id,
    };

    const sacrament = await createSacrament(sacramentData);
    const sacramentId = sacrament.id;

    // Get the created sacrament with type details
    const createdSacrament = await prisma.sacrament.findUnique({
      where: { id: sacramentId },
      include: {
        sacramentType: {
          select: {
            name: true,
            code: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      message: t('sacramentCreatedSuccessfully'),
      sacrament: createdSacrament
        ? {
            ...createdSacrament,
            memberId: createdSacrament.memberId,
            sacramentTypeId: createdSacrament.sacramentTypeId,
            censusYearId: createdSacrament.censusYearId,
            sacrament_name: createdSacrament.sacramentType.name,
            sacrament_code: createdSacrament.sacramentType.code,
            createdAt: createdSacrament.createdAt,
            updatedAt: createdSacrament.updatedAt,
          }
        : null,
    });
  } catch (_error) {
    return NextResponse.json(
      { error: t('failedToCreateSacrament') },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/admin/sacraments
 * Update an existing sacrament
 */
export async function PUT(request: NextRequest) {
  // Get locale from centralized utility at the beginning
  const locale = await getLocaleFromCookies();
  const t = await getTranslations({ locale, namespace: 'errors' });
  const _tNotifications = await getTranslations({
    locale,
    namespace: 'notifications',
  });

  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    if (!session || session.user.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const data = await request.json();
    const { id, date, place } = data;

    // Validate required fields
    if (!id) {
      return NextResponse.json(
        {
          error: t('sacramentIdRequired'),
        },
        { status: 400 }
      );
    }

    // Validate date is not in the future
    if (date) {
      const sacramentDate = startOfDay(new Date(date));
      const today = getTodayInSydney();
      if (sacramentDate > today) {
        return NextResponse.json(
          {
            error: t('sacramentDateCannotBeInFuture'),
          },
          { status: 400 }
        );
      }
    }

    // Update the sacrament
    await updateSacrament(id, {
      date: date ? new Date(date) : null,
      place: place || null,
    });

    // Get the updated sacrament with type details
    const updatedSacrament = await prisma.sacrament.findUnique({
      where: { id: Number.parseInt(id, 10) },
      include: {
        sacramentType: {
          select: {
            name: true,
            code: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      message: t('sacramentUpdatedSuccessfully'),
      sacrament: updatedSacrament
        ? {
            ...updatedSacrament,
            memberId: updatedSacrament.memberId,
            sacramentTypeId: updatedSacrament.sacramentTypeId,
            censusYearId: updatedSacrament.censusYearId,
            sacrament_name: updatedSacrament.sacramentType.name,
            sacrament_code: updatedSacrament.sacramentType.code,
            createdAt: updatedSacrament.createdAt,
            updatedAt: updatedSacrament.updatedAt,
          }
        : null,
    });
  } catch (_error) {
    return NextResponse.json(
      { error: t('sacramentUpdateFailed') },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/admin/sacraments
 * Delete a sacrament
 */
export async function DELETE(request: NextRequest) {
  // Get locale from centralized utility at the beginning
  const locale = await getLocaleFromCookies();
  const t = await getTranslations({ locale, namespace: 'errors' });
  const tNotifications = await getTranslations({
    locale,
    namespace: 'notifications',
  });

  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    if (!session || session.user.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        {
          error: t('sacramentIdRequired'),
        },
        { status: 400 }
      );
    }

    // Delete the sacrament
    await deleteSacrament(Number.parseInt(id, 10));

    return NextResponse.json({
      success: true,
      message: tNotifications('sacramentDeletedSuccessfully'),
    });
  } catch (_error) {
    return NextResponse.json(
      { error: t('sacramentDeleteFailed') },
      { status: 500 }
    );
  }
}

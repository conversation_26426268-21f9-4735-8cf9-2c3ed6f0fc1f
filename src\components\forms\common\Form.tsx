'use client';

import { useTranslations } from 'next-intl';
import type { ReactNode } from 'react';
import type {
  FieldVal<PERSON>,
  SubmitHandler,
  UseFormReturn,
} from 'react-hook-form';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface FormProps<T extends FieldValues> {
  form: UseFormReturn<T>;
  onSubmit: SubmitHandler<T>;
  children: ReactNode;
  submitText?: string;
  /**
   * Controls the loading state of the submit button.
   * When true, the button will be disabled and show "Processing..."
   * This should only be true during actual form submission, not during initial page load.
   */
  isLoading?: boolean;
  className?: string;
}

export function Form<T extends FieldValues>({
  form,
  onSubmit,
  children,
  submitText,
  isLoading = false,
  className,
}: FormProps<T>) {
  const t = useTranslations('common');

  // Use translation as default if no submitText provided
  const buttonText = submitText || t('submit');

  return (
    <form
      className={cn('space-y-4', className)}
      onSubmit={form.handleSubmit(onSubmit)}
    >
      {children}
      {/*
        The button is only disabled during actual form submission (isLoading=true),
        not during the initial NextAuth session check
      */}
      <Button className="w-full" disabled={isLoading} type="submit">
        {isLoading ? t('processing') : buttonText}
      </Button>
    </form>
  );
}

/**
 * TypeScript interfaces for Census Welcome Modal components
 * Follows established codebase patterns in src/types/ directory
 */

// Welcome modal state interface
export interface IWelcomeModalState {
  isOpen: boolean;
  isDismissed: boolean;
}

// Welcome modal actions interface
export interface IWelcomeModalActions {
  setIsOpen: (open: boolean) => void;
  dismissModal: () => void;
  resetPreferences: () => void;
}

// Combined hook return type
export interface IUseWelcomeModalReturn
  extends IWelcomeModalState,
    IWelcomeModalActions {}

// Main modal component props
export interface ICensusWelcomeModalProps {
  isOpen: boolean;
  onClose: () => void;
  onDismiss: () => void;
}

// Welcome modal content component props
export interface IWelcomeModalContentProps {
  onGetStarted: () => void;
  progress?: number; // Progress percentage for liquid progress circle
}

// Next steps section component props
export interface INextStepsSectionProps {
  onGetStarted: () => void;
}

// Translation key types for better type safety
export type OnboardingTranslationKey =
  | 'welcomeDescription'
  | 'nextStepsTitle'
  | 'hobbyFieldsTitle'
  | 'hobbyFieldsDescription'
  | 'occupationTitle'
  | 'occupationDescription'
  | 'sacramentsTitle'
  | 'sacramentsDescription'
  | 'householdMembersTitle'
  | 'householdMembersDescription'
  | 'householdRegistrationTitle'
  | 'householdRegistrationDescription'
  | 'communityFeedbackTitle'
  | 'communityFeedbackDescription'
  | 'getStarted'
  | 'continueProgress'
  | 'almostDone'
  | 'complete'
  | 'skipForNow';

// Individual next step item definition
export interface INextStepItem {
  id: string;
  titleKey: OnboardingTranslationKey;
  descriptionKey: OnboardingTranslationKey;
  icon?: React.ComponentType<{ className?: string }>; // Made optional since we're using step numbers instead
  isOptional: boolean;
}

// Welcome modal configuration
export interface IWelcomeModalConfig {
  storageKey: string;
  showOnFirstVisit: boolean;
  respectDismissal: boolean;
  mobileBreakpoint: number;
}

// Constants for localStorage keys (census-specific prefix)
export const WELCOME_MODAL_STORAGE_KEY = 'census-welcome-dismissed';

export const WELCOME_MODAL_CONFIG: IWelcomeModalConfig = {
  storageKey: WELCOME_MODAL_STORAGE_KEY,
  showOnFirstVisit: true,
  respectDismissal: true,
  mobileBreakpoint: 768,
};

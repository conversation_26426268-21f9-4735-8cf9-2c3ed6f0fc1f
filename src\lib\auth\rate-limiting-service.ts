import { createHash } from 'crypto';
import { prisma } from '@/lib/db/prisma';
import { getRateLimitSettings } from '@/lib/db/settings';

// Rate limiting configuration constants (fallback values)
export const RATE_LIMIT_CONFIG = {
  MAX_ATTEMPTS: 5,
  BASE_LOCKOUT_MINUTES: 15,
  ESCALATION_INCREMENT_MINUTES: 15,
} as const;

/**
 * Get current rate limiting configuration from database with fallback to constants
 */
export async function getCurrentRateLimitConfig(): Promise<{
  maxAttempts: number;
  lockoutMinutes: number;
  escalationMinutes: number;
}> {
  try {
    const settings = await getRateLimitSettings();
    return {
      maxAttempts: settings.maxAttempts,
      lockoutMinutes: settings.lockoutMinutes,
      escalationMinutes: settings.escalationMinutes,
    };
  } catch {
    // Fallback to hardcoded values if database is unavailable
    // Only log in development to prevent information leakage
    if (process.env.NODE_ENV === 'development') {
      console.warn(
        'Failed to fetch rate limit settings, using fallback values'
      );
    }
    return {
      maxAttempts: RATE_LIMIT_CONFIG.MAX_ATTEMPTS,
      lockoutMinutes: RATE_LIMIT_CONFIG.BASE_LOCKOUT_MINUTES,
      escalationMinutes: RATE_LIMIT_CONFIG.ESCALATION_INCREMENT_MINUTES,
    };
  }
}

// Rate limiting data types
export interface AuthRateLimit {
  id: number;
  sessionToken: string;
  failedAttempts: number;
  lockoutUntil: Date | null;
  escalationLevel: number;
  lastFailedAttempt: Date | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface RateLimitStatus {
  isLocked: boolean;
  remainingTime: number; // milliseconds
  attemptsRemaining: number;
  escalationLevel: number;
}

export interface EnhancedRateLimitStatus extends RateLimitStatus {
  lockoutExpiry: number | null; // Absolute timestamp when lockout expires (null if not locked)
}

export interface RateLimitResult {
  allowed: boolean;
  status: RateLimitStatus;
  lockoutTriggered: boolean;
}

/**
 * Hash session token with SHA256 for database storage
 * This ensures consistent 64-character length and prevents token collision
 */
export function hashSessionToken(sessionToken: string): string {
  // Validate session token input
  if (!sessionToken || typeof sessionToken !== 'string') {
    throw new Error('Invalid session token: must be a non-empty string');
  }

  if (sessionToken.length < 10) {
    // Provide more detailed error information for debugging
    const safeToken =
      sessionToken.length > 0
        ? sessionToken.substring(0, Math.min(20, sessionToken.length))
        : '[empty]';
    throw new Error(
      `Invalid session token: too short (length: ${sessionToken.length}, token: "${safeToken}")`
    );
  }

  if (sessionToken.length > 1000) {
    throw new Error('Invalid session token: too long');
  }

  return createHash('sha256').update(sessionToken).digest('hex');
}

/**
 * Get secure session token from NextAuth cookies
 * NOTE: This function is kept for potential future use but is not currently used by any system.
 * Admin authentication does not use rate limiting (protected by 2FA)
 * Census authentication uses IP-based rate limiting via census-rate-limit-utils.ts
 */
export function getSessionToken(cookies: {
  get: (name: string) => { value: string } | undefined;
}): string {
  const sessionToken =
    cookies.get('admin-session-token')?.value ||
    cookies.get('__Secure-admin-session-token')?.value;

  if (!sessionToken) {
    throw new Error('No session token found');
  }

  return sessionToken;
}

/**
 * Calculate lockout duration based on escalation level and current configuration
 * Escalation pattern: baseLockout, baseLockout + increment, baseLockout + 2*increment, etc. (capped at 24 hours)
 */
export function calculateLockoutDuration(
  escalationLevel: number,
  config: {
    maxAttempts: number;
    lockoutMinutes: number;
    escalationMinutes: number;
  }
): number {
  // Validate inputs to prevent security issues
  if (!Number.isInteger(escalationLevel) || escalationLevel < 0) {
    escalationLevel = 0; // Fallback to safe value
  }

  if (escalationLevel > 100) {
    escalationLevel = 100; // Cap at reasonable maximum to prevent overflow
  }

  if (
    !config.lockoutMinutes ||
    config.lockoutMinutes < 1 ||
    config.lockoutMinutes > 1440
  ) {
    config.lockoutMinutes = 15; // Fallback to safe default
  }

  if (
    !config.escalationMinutes ||
    config.escalationMinutes < 0 ||
    config.escalationMinutes > 120
  ) {
    config.escalationMinutes = 15; // Fallback to safe default
  }

  // escalationLevel starts at 0, so we add the base lockout plus escalation increments
  // Level 0 → baseLockout
  // Level 1 → baseLockout + increment
  // Level 2 → baseLockout + 2*increment
  const minutes =
    config.lockoutMinutes + escalationLevel * config.escalationMinutes;

  // Cap at 24 hours (1440 minutes) for security
  const maxMinutes = 1440;
  return Math.min(minutes, maxMinutes) * 60 * 1000; // Convert to milliseconds
}

/**
 * Get current rate limit status for a session
 */
export async function getRateLimitStatus(
  sessionToken: string,
  config?: {
    maxAttempts: number;
    lockoutMinutes: number;
    escalationMinutes: number;
  }
): Promise<RateLimitStatus> {
  try {
    // Get current configuration if not provided
    const rateLimitConfig = config || (await getCurrentRateLimitConfig());

    // Hash the session token for database lookup
    const hashedToken = hashSessionToken(sessionToken);

    const record = await prisma.authRateLimit.findUnique({
      where: { sessionToken: hashedToken },
    });

    if (!record) {
      return {
        isLocked: false,
        remainingTime: 0,
        attemptsRemaining: rateLimitConfig.maxAttempts,
        escalationLevel: 0,
      };
    }

    const now = new Date();
    const nowTime = now.getTime(); // Use consistent timestamp

    // Check if currently locked out
    if (
      record.lockoutUntil &&
      new Date(record.lockoutUntil).getTime() > nowTime
    ) {
      const remainingTime = Math.max(
        0,
        new Date(record.lockoutUntil).getTime() - nowTime
      );
      return {
        isLocked: true,
        remainingTime,
        attemptsRemaining: 0,
        escalationLevel: Math.max(0, record.escalationLevel || 0),
      };
    }

    // Not locked out
    return {
      isLocked: false,
      remainingTime: 0,
      attemptsRemaining: Math.max(
        0,
        rateLimitConfig.maxAttempts - (record.failedAttempts || 0)
      ),
      escalationLevel: Math.max(0, record.escalationLevel || 0),
    };
  } catch (error) {
    // Log detailed error only in development, generic message in production
    if (process.env.NODE_ENV === 'development') {
      console.error('Error getting rate limit status:', error);
    } else {
      console.error('Rate limit status check error occurred');
    }
    // Fail securely - allow access but log error
    // Try to use provided config, otherwise fall back to constants
    const fallbackMaxAttempts =
      config?.maxAttempts || RATE_LIMIT_CONFIG.MAX_ATTEMPTS;
    return {
      isLocked: false,
      remainingTime: 0,
      attemptsRemaining: fallbackMaxAttempts,
      escalationLevel: 0,
    };
  }
}

/**
 * Get enhanced rate limit status for hybrid client-server approach
 * Includes server timestamp and lockout expiry for client-side countdown
 */
export async function getEnhancedRateLimitStatus(
  sessionToken: string,
  config?: {
    maxAttempts: number;
    lockoutMinutes: number;
    escalationMinutes: number;
  }
): Promise<EnhancedRateLimitStatus> {
  try {
    // Validate session token input
    if (
      !sessionToken ||
      typeof sessionToken !== 'string' ||
      sessionToken.length < 10
    ) {
      throw new Error('Invalid session token provided');
    }

    // Get current configuration if not provided
    const rateLimitConfig = config || (await getCurrentRateLimitConfig());

    // Hash the session token for database lookup
    const hashedToken = hashSessionToken(sessionToken);

    const record = await prisma.authRateLimit.findUnique({
      where: { sessionToken: hashedToken },
    });

    const now = new Date();
    const nowTimestamp = now.getTime();

    if (!record) {
      return {
        isLocked: false,
        remainingTime: 0,
        attemptsRemaining: rateLimitConfig.maxAttempts,
        escalationLevel: 0,
        lockoutExpiry: null,
      };
    }

    // Check if currently locked out
    if (
      record.lockoutUntil &&
      new Date(record.lockoutUntil).getTime() > nowTimestamp
    ) {
      const lockoutExpiry = new Date(record.lockoutUntil).getTime();
      const remainingTime = Math.max(0, lockoutExpiry - nowTimestamp);

      return {
        isLocked: true,
        remainingTime,
        attemptsRemaining: 0,
        escalationLevel: Math.max(0, record.escalationLevel || 0),
        lockoutExpiry,
      };
    }

    // Not locked out
    return {
      isLocked: false,
      remainingTime: 0,
      attemptsRemaining: Math.max(
        0,
        rateLimitConfig.maxAttempts - (record.failedAttempts || 0)
      ),
      escalationLevel: Math.max(0, record.escalationLevel || 0),
      lockoutExpiry: null,
    };
  } catch (error) {
    // Log detailed error only in development, generic message in production
    if (process.env.NODE_ENV === 'development') {
      console.error('Error getting enhanced rate limit status:', error);
    } else {
      console.error('Enhanced rate limit status check error occurred');
    }

    // Fail securely - allow access but log error
    const fallbackMaxAttempts =
      config?.maxAttempts || RATE_LIMIT_CONFIG.MAX_ATTEMPTS;

    return {
      isLocked: false,
      remainingTime: 0,
      attemptsRemaining: fallbackMaxAttempts,
      escalationLevel: 0,
      lockoutExpiry: null,
    };
  }
}

/**
 * Record a failed authentication attempt
 */
export async function recordFailedAttempt(
  sessionToken: string
): Promise<RateLimitResult> {
  try {
    // Safe debug logging helper (for potential future use)
    // const safeTokenLog = (token: string) => {
    //   if (typeof token === 'string' && token.length >= 20) {
    //     return token.substring(0, 20) + '...';
    //   }
    //   return '[invalid-token]';
    // };

    // Get configuration once at the beginning to avoid multiple calls
    const config = await getCurrentRateLimitConfig();

    const now = new Date();
    // Hash the session token for database storage
    const hashedToken = hashSessionToken(sessionToken);

    // Use Prisma upsert for atomic operation
    const record = await prisma.authRateLimit.upsert({
      where: { sessionToken: hashedToken },
      create: {
        sessionToken: hashedToken,
        failedAttempts: 1,
        escalationLevel: 0,
        lastFailedAttempt: now,
        createdAt: now,
        updatedAt: now,
      },
      update: {
        failedAttempts: {
          increment: 1,
        },
        lastFailedAttempt: now,
        updatedAt: now,
      },
    });

    // Check if currently locked out (could have been set by another request)
    if (record.lockoutUntil && new Date(record.lockoutUntil) > now) {
      const remainingTime = Math.max(
        0,
        new Date(record.lockoutUntil).getTime() - now.getTime()
      );
      return {
        allowed: false,
        status: {
          isLocked: true,
          remainingTime,
          attemptsRemaining: 0,
          escalationLevel: Math.max(0, record.escalationLevel || 0),
        },
        lockoutTriggered: false, // We didn't trigger it, another request did
      };
    }

    // Check if WE should trigger a lockout
    if (record.failedAttempts >= config.maxAttempts) {
      const lockoutDuration = calculateLockoutDuration(
        record.escalationLevel,
        config
      );
      const lockoutUntil = new Date(now.getTime() + lockoutDuration);

      // Atomic lockout trigger with race condition protection
      // Safely increment escalation level with bounds checking
      const nextEscalationLevel = Math.min(
        100,
        Math.max(0, (record.escalationLevel || 0) + 1)
      );

      try {
        await prisma.authRateLimit.update({
          where: {
            sessionToken: hashedToken,
            OR: [{ lockoutUntil: null }, { lockoutUntil: { lte: now } }],
          },
          data: {
            lockoutUntil,
            escalationLevel: nextEscalationLevel,
            failedAttempts: 0,
            updatedAt: now,
          },
        });

        return {
          allowed: false,
          status: {
            isLocked: true,
            remainingTime: lockoutDuration,
            attemptsRemaining: 0,
            escalationLevel: nextEscalationLevel,
          },
          lockoutTriggered: true,
        };
      } catch {
        // If update failed, another request might have triggered lockout
        return {
          allowed: false,
          status: {
            isLocked: true,
            remainingTime: lockoutDuration,
            attemptsRemaining: 0,
            escalationLevel: nextEscalationLevel,
          },
          lockoutTriggered: false,
        };
      }
    }

    // Not locked out yet
    const attemptsRemaining = Math.max(
      0,
      config.maxAttempts - (record.failedAttempts || 0)
    );

    return {
      allowed: true,
      status: {
        isLocked: false,
        remainingTime: 0,
        attemptsRemaining,
        escalationLevel: Math.max(0, record.escalationLevel || 0),
      },
      lockoutTriggered: false,
    };
  } catch (error) {
    // Log detailed error only in development, generic message in production
    if (process.env.NODE_ENV === 'development') {
      console.error('Error recording failed attempt:', error);
    } else {
      console.error('Rate limiting service error occurred');
    }

    // Fail securely - allow access but log error
    // Use hardcoded fallback to avoid recursive errors
    return {
      allowed: true,
      status: {
        isLocked: false,
        remainingTime: 0,
        attemptsRemaining: RATE_LIMIT_CONFIG.MAX_ATTEMPTS,
        escalationLevel: 0,
      },
      lockoutTriggered: false,
    };
  }
}

/**
 * Reset rate limiting for successful authentication
 */
export async function resetRateLimit(sessionToken: string): Promise<void> {
  try {
    // Hash the session token for database lookup
    const hashedToken = hashSessionToken(sessionToken);

    await prisma.authRateLimit.updateMany({
      where: { sessionToken: hashedToken },
      data: {
        failedAttempts: 0,
        lockoutUntil: null,
        escalationLevel: 0,
        updatedAt: new Date(),
      },
    });
  } catch (error) {
    // Log detailed error only in development, generic message in production
    if (process.env.NODE_ENV === 'development') {
      console.error('Error resetting rate limit:', error);
    } else {
      console.error('Rate limiting reset error occurred');
    }
    // Don't throw - this is not critical for authentication flow
  }
}

/**
 * Check if session is currently locked out (optimised version)
 */
export async function isSessionLocked(sessionToken: string): Promise<boolean> {
  try {
    // Hash the session token for database lookup
    const hashedToken = hashSessionToken(sessionToken);

    const record = await prisma.authRateLimit.findUnique({
      where: { sessionToken: hashedToken },
      select: { lockoutUntil: true },
    });

    if (!record) {
      return false; // No record means not locked
    }

    // Check if currently locked out (use consistent timestamp)
    const now = new Date();
    if (record.lockoutUntil && new Date(record.lockoutUntil) > now) {
      return true;
    }

    return false;
  } catch (error) {
    // Log detailed error only in development, generic message in production
    if (process.env.NODE_ENV === 'development') {
      console.error('Error checking session lock status:', error);
    } else {
      console.error('Session lock status check error occurred');
    }
    // Fail securely - assume not locked to allow access
    return false;
  }
}

/**
 * Get rate limiting statistics for monitoring
 * Admin-only function for security monitoring
 */
export async function getRateLimitingStats(): Promise<{
  totalLocked: number;
  totalAttempts: number;
  recentAttempts: number; // Last hour
}> {
  try {
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);

    const [lockedCount, totalAttempts, recentAttempts] = await Promise.all([
      prisma.authRateLimit.count({
        where: {
          lockoutUntil: {
            gt: now,
          },
        },
      }),
      prisma.authRateLimit.aggregate({
        _sum: {
          failedAttempts: true,
        },
      }),
      prisma.authRateLimit.count({
        where: {
          lastFailedAttempt: {
            gt: oneHourAgo,
          },
        },
      }),
    ]);

    return {
      totalLocked: lockedCount,
      totalAttempts: totalAttempts._sum.failedAttempts || 0,
      recentAttempts,
    };
  } catch (error) {
    // Log detailed error only in development, generic message in production
    if (process.env.NODE_ENV === 'development') {
      console.error('Error getting rate limiting stats:', error);
    } else {
      console.error('Rate limiting stats error occurred');
    }
    return {
      totalLocked: 0,
      totalAttempts: 0,
      recentAttempts: 0,
    };
  }
}

'use client';

import { Check, ChevronsUpDown, Loader2, MapPin } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import {
  type Control,
  Controller,
  type FieldError,
  type FieldValues,
} from 'react-hook-form';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import { Label } from '@/components/ui/label';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { SUBURB_AUTOCOMPLETE_CONFIG } from '@/config/suburb-autocomplete';
import { cn } from '@/lib/utils';

// Interface for suburb data from API
interface SuburbOption {
  id: number;
  displayName: string;
  suburbName: string;
  stateCode: string;
}

// API response interface
interface SuburbLoadResponse {
  success: boolean;
  suburbs: SuburbOption[];
  count: number;
  timestamp: string;
}

interface SuburbAutocompleteOptimizedProps<
  T extends FieldValues = FieldValues,
> {
  control: Control<T>;
  name: keyof T | string;
  label: string;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  error?: FieldError;
  className?: string;
  fallbackToSearch?: boolean; // Option to fallback to search API if load fails
}

export function SuburbAutocompleteOptimized<
  T extends FieldValues = FieldValues,
>({
  control,
  name,
  label,
  placeholder,
  required = false,
  disabled = false,
  error,
  className,
}: Omit<SuburbAutocompleteOptimizedProps<T>, 'fallbackToSearch'>) {
  const t = useTranslations();
  const tCommon = useTranslations('common');
  const tForms = useTranslations('forms');
  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('');
  const [allSuburbs, setAllSuburbs] = useState<SuburbOption[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [loadError, setLoadError] = useState<string | null>(null);

  // Refs for cleanup
  const abortControllerRef = useRef<AbortController | null>(null);
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Load all suburbs once on component mount
  const loadAllSuburbs = useCallback(async () => {
    try {
      // Cancel previous request if any
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      // Create new abort controller
      abortControllerRef.current = new AbortController();

      setIsLoading(true);
      setLoadError(null);

      const response = await fetch('/api/census/suburbs/all', {
        signal: abortControllerRef.current.signal,
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        const errorMessage =
          errorData.details || errorData.error || `HTTP ${response.status}`;
        throw new Error(errorMessage);
      }

      const data: SuburbLoadResponse = await response.json();

      if (data.success) {
        setAllSuburbs(data.suburbs);

        // Log success in development
        if (process.env.NODE_ENV === 'development') {
          console.log(`Loaded ${data.count} suburbs for autocomplete`);
        }
      } else {
        throw new Error('Failed to load suburbs');
      }
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        // Request was cancelled, ignore
        return;
      }

      console.error('Suburb load error:', error);
      setLoadError(
        error instanceof Error ? error.message : 'Failed to load suburbs'
      );
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Load suburbs on component mount
  useEffect(() => {
    loadAllSuburbs();
  }, [loadAllSuburbs]);

  // Debounce search query for performance
  useEffect(() => {
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    debounceTimeoutRef.current = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 300);

    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, [searchQuery]);

  // Filter suburbs based on debounced search query (frontend filtering)
  const filteredSuburbs = useMemo(() => {
    if (
      !debouncedSearchQuery ||
      debouncedSearchQuery.length < SUBURB_AUTOCOMPLETE_CONFIG.MIN_SEARCH_CHARS
    ) {
      return [];
    }

    const query = debouncedSearchQuery.toLowerCase().trim();

    // Optimize filtering for large datasets
    const exactMatches: SuburbOption[] = [];
    const prefixMatches: SuburbOption[] = [];
    const containsMatches: SuburbOption[] = [];

    // Early exit when we have enough results
    for (const suburb of allSuburbs) {
      if (
        exactMatches.length + prefixMatches.length + containsMatches.length >=
        SUBURB_AUTOCOMPLETE_CONFIG.MAX_RESULTS
      )
        break;

      const displayLower = suburb.displayName.toLowerCase();
      const suburbLower = suburb.suburbName.toLowerCase();

      // Exact match (highest priority)
      if (displayLower === query || suburbLower === query) {
        exactMatches.push(suburb);
      }
      // Prefix match (second priority)
      else if (
        displayLower.startsWith(query) ||
        suburbLower.startsWith(query)
      ) {
        prefixMatches.push(suburb);
      }
      // Contains match (lowest priority)
      else if (displayLower.includes(query) || suburbLower.includes(query)) {
        containsMatches.push(suburb);
      }
    }

    // Combine results in priority order and limit
    return [
      ...exactMatches.slice(0, 10), // Max 10 exact matches
      ...prefixMatches.slice(0, 25), // Max 25 prefix matches
      ...containsMatches.slice(0, 15), // Max 15 contains matches
    ].slice(0, SUBURB_AUTOCOMPLETE_CONFIG.MAX_RESULTS);
  }, [allSuburbs, debouncedSearchQuery]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  return (
    <div className={cn('space-y-2', className)}>
      <Label className="font-medium text-sm">
        {label}
        {required && <span className="ml-1 text-destructive">*</span>}
      </Label>

      <Controller
        control={control}
        name={name as any}
        render={({ field }) => (
          <Popover onOpenChange={setOpen} open={open}>
            <PopoverTrigger asChild>
              <Button
                aria-expanded={open}
                aria-label={`Select ${label.toLowerCase()}`}
                className={cn(
                  'w-full cursor-pointer justify-between hover:bg-accent hover:text-accent-foreground',
                  !field.value && 'text-muted-foreground',
                  error && 'border-destructive',
                  disabled && 'cursor-not-allowed hover:bg-transparent'
                )}
                disabled={disabled || isLoading}
                role="combobox"
                variant="outline"
              >
                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4 shrink-0" />
                  <span className="truncate">
                    {String(
                      field.value ||
                        (isLoading
                          ? t('common.loadingSuburbs')
                          : placeholder) ||
                        ''
                    )}
                  </span>
                </div>
                {isLoading ? (
                  <Loader2 className="ml-2 h-4 w-4 shrink-0 animate-spin" />
                ) : (
                  <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent
              align="start"
              className="p-0"
              style={{ width: 'var(--radix-popover-trigger-width)' }}
            >
              <Command shouldFilter={false}>
                <CommandInput
                  className="h-9"
                  disabled={isLoading}
                  onValueChange={setSearchQuery}
                  placeholder={
                    isLoading
                      ? 'Loading...'
                      : placeholder || t('forms.searchForYourSuburb')
                  }
                  value={searchQuery}
                />
                <CommandList className="max-h-[250px]">
                  {isLoading && (
                    <div className="flex items-center justify-center py-6">
                      <Loader2 className="h-4 w-4 animate-spin" />
                      <span className="ml-2 text-muted-foreground text-sm">
                        Loading suburbs...
                      </span>
                    </div>
                  )}

                  {loadError && (
                    <div className="py-6 text-center">
                      <p className="mb-2 text-destructive text-sm">
                        {loadError}
                      </p>
                      <Button
                        className="text-xs"
                        onClick={loadAllSuburbs}
                        size="sm"
                        variant="outline"
                      >
                        {tCommon('retry')}
                      </Button>
                    </div>
                  )}

                  {!(isLoading || loadError) &&
                    searchQuery.length >=
                      SUBURB_AUTOCOMPLETE_CONFIG.MIN_SEARCH_CHARS &&
                    filteredSuburbs.length === 0 && (
                      <CommandEmpty>{t('common.noSuburbsFound')}</CommandEmpty>
                    )}

                  {!(isLoading || loadError) &&
                    searchQuery.length <
                      SUBURB_AUTOCOMPLETE_CONFIG.MIN_SEARCH_CHARS && (
                      <div className="py-6 text-center text-muted-foreground text-sm">
                        {tForms('typeAtLeastNCharacters', {
                          count:
                            SUBURB_AUTOCOMPLETE_CONFIG.MIN_SEARCH_CHARS.toString(),
                        })}
                      </div>
                    )}

                  {!(isLoading || loadError) && filteredSuburbs.length > 0 && (
                    <CommandGroup>
                      {filteredSuburbs.map((suburb) => (
                        <CommandItem
                          className="flex cursor-pointer items-center gap-2"
                          key={suburb.id}
                          onSelect={() => {
                            field.onChange(suburb.displayName);
                            setOpen(false);
                            setSearchQuery('');
                          }}
                          value={suburb.displayName}
                        >
                          <Check
                            className={cn(
                              'h-4 w-4',
                              field.value === suburb.displayName
                                ? 'opacity-100'
                                : 'opacity-0'
                            )}
                          />
                          <MapPin className="h-4 w-4 text-muted-foreground" />
                          <span>{suburb.displayName}</span>
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  )}
                </CommandList>
              </Command>
            </PopoverContent>
          </Popover>
        )}
      />

      {error && <p className="text-destructive text-xs">{error.message}</p>}
    </div>
  );
}

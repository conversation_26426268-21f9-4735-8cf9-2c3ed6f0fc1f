import <PERSON><PERSON><PERSON><PERSON> from 'jszip';
import { type NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { getTranslations } from 'next-intl/server';
import { authOptions } from '@/lib/auth/auth-options';
import {
  createPreImportBackup,
  importCSV,
  importJSON,
  importSQL,
  logImport,
  validateCSVImport,
  validateJSONImport,
  validateSQLImport,
} from '@/lib/db/import-prisma';
import { getErrorMessage } from '@/lib/utils/error-handling';
import { getLocaleFromCookies } from '@/lib/utils/server-messages';

export async function POST(request: NextRequest) {
  // Get locale from centralized utility at the beginning
  const locale = await getLocaleFromCookies();
  const t = await getTranslations({ locale, namespace: 'errors' });
  const tAdmin = await getTranslations({ locale, namespace: 'admin' });

  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Require admin authentication
    if (!session || session.user.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get admin ID from session
    const adminId = Number.parseInt(session.user.id, 10);

    // Get client IP address
    const ipAddress =
      request.headers.get('x-forwarded-for') ||
      request.headers.get('x-real-ip') ||
      'unknown';

    // Get the form data
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    // Determine file type
    const fileType = file.name.split('.').pop()?.toLowerCase();

    if (!(fileType && ['sql', 'csv', 'json', 'zip'].includes(fileType))) {
      return NextResponse.json(
        {
          error: t('unsupportedFileType'),
          details: 'Only SQL, CSV, JSON, or ZIP files are supported',
        },
        { status: 400 }
      );
    }

    // Read file content
    const fileBuffer = await file.arrayBuffer();

    // Create a backup before import
    await createPreImportBackup(adminId);

    // Process based on file type
    if (fileType === 'sql') {
      // Handle SQL import
      const sqlContent = new TextDecoder().decode(fileBuffer);

      // Validate SQL
      const validationResult = await validateSQLImport(sqlContent);

      if (!validationResult.isValid) {
        await logImport(
          adminId,
          'sql',
          false,
          0,
          {
            errors: validationResult.errors,
            warnings: validationResult.warnings,
          },
          ipAddress as string
        );

        return NextResponse.json(
          {
            success: false,
            errors: validationResult.errors,
            warnings: validationResult.warnings,
          },
          { status: 400 }
        );
      }

      // Import SQL
      const importResult = await importSQL();

      await logImport(
        adminId,
        'sql',
        importResult.success,
        validationResult.recordCount,
        {
          message: importResult.message,
          warnings: validationResult.warnings,
        },
        ipAddress as string
      );

      return NextResponse.json({
        success: importResult.success,
        message: importResult.message,
        warnings: validationResult.warnings,
        tables: validationResult.tables,
        recordCount: validationResult.recordCount,
      });
    }
    if (fileType === 'json') {
      // Handle JSON import
      const jsonContent = new TextDecoder().decode(fileBuffer);
      interface ImportData {
        [tableName: string]: Record<string, unknown>[];
      }
      let jsonData: ImportData;

      try {
        jsonData = JSON.parse(jsonContent);
      } catch (error) {
        await logImport(
          adminId,
          'json',
          false,
          0,
          {
            errors: ['Invalid JSON format'],
          },
          ipAddress as string
        );

        return NextResponse.json(
          {
            success: false,
            errors: [`Invalid JSON format: ${getErrorMessage(error)}`],
          },
          { status: 400 }
        );
      }

      // Validate JSON
      const validationResult = await validateJSONImport(jsonData);

      if (!validationResult.isValid) {
        await logImport(
          adminId,
          'json',
          false,
          0,
          {
            errors: validationResult.errors,
            warnings: validationResult.warnings,
          },
          ipAddress as string
        );

        return NextResponse.json(
          {
            success: false,
            errors: validationResult.errors,
            warnings: validationResult.warnings,
          },
          { status: 400 }
        );
      }

      // Import JSON
      const importResult = await importJSON(jsonData);

      await logImport(
        adminId,
        'json',
        importResult.success,
        validationResult.recordCount,
        {
          message: importResult.message,
          warnings: validationResult.warnings,
        },
        ipAddress as string
      );

      return NextResponse.json({
        success: importResult.success,
        message: importResult.message,
        warnings: validationResult.warnings,
        tables: validationResult.tables,
        recordCount: validationResult.recordCount,
      });
    }
    if (fileType === 'csv' || fileType === 'zip') {
      // Handle CSV or ZIP import
      const csvFiles: Record<string, string> = {};

      if (fileType === 'csv') {
        // Single CSV file - use filename as table name
        const tableName = file.name.replace(/\.csv$/i, '');
        const csvContent = new TextDecoder().decode(fileBuffer);
        csvFiles[tableName] = csvContent;
      } else {
        // ZIP file - extract multiple CSV files
        const zip = new JSZip();
        const zipContent = await zip.loadAsync(fileBuffer);

        const filePromises = Object.keys(zipContent.files)
          .filter(
            (filename) =>
              filename.endsWith('.csv') && !zipContent.files[filename].dir
          )
          .map(async (filename) => {
            const content = await zipContent.files[filename].async('string');
            const tableName = filename.replace(/\.csv$/i, '');
            return { tableName, content };
          });

        const files = await Promise.all(filePromises);

        if (files.length === 0) {
          await logImport(
            adminId,
            'csv',
            false,
            0,
            {
              errors: ['No CSV files found in the ZIP archive'],
            },
            ipAddress as string
          );

          return NextResponse.json(
            {
              success: false,
              errors: ['No CSV files found in the ZIP archive'],
            },
            { status: 400 }
          );
        }

        // Convert to record
        files.forEach((file) => {
          csvFiles[file.tableName] = file.content;
        });
      }

      // Validate CSV files
      const validationResult = await validateCSVImport(csvFiles);

      if (!validationResult.isValid) {
        await logImport(
          adminId,
          'csv',
          false,
          0,
          {
            errors: validationResult.errors,
            warnings: validationResult.warnings,
          },
          ipAddress as string
        );

        return NextResponse.json(
          {
            success: false,
            errors: validationResult.errors,
            warnings: validationResult.warnings,
          },
          { status: 400 }
        );
      }

      // Import CSV
      const importResult = await importCSV(csvFiles);

      await logImport(
        adminId,
        'csv',
        importResult.success,
        validationResult.recordCount,
        {
          message: importResult.message,
          warnings: validationResult.warnings,
        },
        ipAddress as string
      );

      return NextResponse.json({
        success: importResult.success,
        message: importResult.message,
        warnings: validationResult.warnings,
        tables: validationResult.tables,
        recordCount: validationResult.recordCount,
      });
    }

    // Should never reach here due to file type check above
    return NextResponse.json(
      { error: 'Unsupported file type' },
      { status: 400 }
    );
  } catch (error) {
    return NextResponse.json(
      {
        error: tAdmin('failedToImportDatabase'),
        details: getErrorMessage(error),
      },
      { status: 500 }
    );
  }
}

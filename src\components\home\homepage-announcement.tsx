'use client';

import { useEffect, useState } from 'react';
import {
  type CensusData,
  processPlaceholders,
} from '@/lib/homepage/placeholder-processor';

interface HomepageAnnouncementProps {
  censusData: CensusData;
  locale?: string;
}

interface AnnouncementSettings {
  enabled: boolean;
  text: string;
  type: 'info' | 'warning' | 'success' | 'destructive';
}

export function HomepageAnnouncement({
  censusData,
  locale = 'en-AU',
}: HomepageAnnouncementProps) {
  const [settings, setSettings] = useState<AnnouncementSettings | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchSettings = async () => {
      try {
        const response = await fetch('/api/homepage-announcement');

        if (response.ok) {
          const data = await response.json();
          setSettings(data);
        }
      } catch (error) {
        // Silent failure - announcements are non-critical
      } finally {
        setIsLoading(false);
      }
    };

    fetchSettings();
  }, []);

  // Don't render anything while loading or if disabled
  if (isLoading || !settings?.enabled || !settings.text.trim()) {
    return null;
  }

  // Process placeholders
  const processedText = processPlaceholders(settings.text, {
    censusData,
    locale,
  });

  // Don't render if processed text is empty
  if (!processedText.trim()) {
    return null;
  }

  // Process markdown-style links [text](url)
  const processLinks = (text: string) => {
    return text.replace(
      /\[([^\]]+)\]\(([^)]+)\)/g,
      '<a href="$2" class="underline hover:no-underline font-medium" target="_blank" rel="noopener noreferrer">$1</a>'
    );
  };

  const processedTextWithLinks = processLinks(processedText);

  // Simplified styling for inline announcement - more subtle
  const getInlineStyle = (type: string) => {
    switch (type) {
      case 'warning':
        return 'text-amber-700 bg-amber-50/50';
      case 'success':
        return 'text-green-700 bg-green-50/50';
      case 'destructive':
        return 'text-red-700 bg-red-50/50';
      default: // info
        return 'text-blue-700 bg-blue-50/50';
    }
  };

  const inlineStyle = getInlineStyle(settings.type);

  return (
    <div className="fade-in mx-auto my-3 w-full max-w-md animate-in duration-500 ease-out">
      <div className={`${inlineStyle} rounded-md px-3 py-2 text-center`}>
        <p
          className="font-medium text-xs leading-relaxed"
          dangerouslySetInnerHTML={{ __html: processedTextWithLinks }}
        />
      </div>
    </div>
  );
}

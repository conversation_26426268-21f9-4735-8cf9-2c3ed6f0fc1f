import { type NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { getTranslations } from 'next-intl/server';
import { authOptions } from '@/lib/auth/auth-options';
import { getHouseholdMemberWithDetails } from '@/lib/db/household-members';
import { getHouseholdById } from '@/lib/db/households';
import { getErrorMessage } from '@/lib/utils/error-handling';
import { getLocaleFromCookies } from '@/lib/utils/server-messages';

/**
 * GET /api/admin/households/:id/deletion-info
 *
 * Gets household deletion information for validation and UX
 * Only accessible to admin users
 */
export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Get locale for translations (moved up to use in auth error)
    const locale = await getLocaleFromCookies();
    const tWarnings = await getTranslations({ locale, namespace: 'warnings' });
    const tErrors = await getTranslations({ locale, namespace: 'errors' });

    // Require admin authentication
    if (!session || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: tErrors('unauthorized') },
        { status: 401 }
      );
    }

    // In Next.js 15+, params is a Promise that must be awaited
    const { id: idString } = await params;
    const id = Number.parseInt(idString, 10);

    if (Number.isNaN(id)) {
      return NextResponse.json(
        { error: tErrors('invalidHouseholdId') },
        { status: 400 }
      );
    }

    // Check if the household exists
    const household = await getHouseholdById(id);
    if (!household) {
      return NextResponse.json(
        { error: tErrors('householdNotFound') },
        { status: 404 }
      );
    }

    // Get household members (only current members for deletion validation)
    const members = await getHouseholdMemberWithDetails(id);
    const nonHeadMembers = members.filter(
      (member) => member.relationship !== 'head'
    );
    const householdHead = members.find(
      (member) => member.relationship === 'head'
    );

    // Determine deletion info
    if (nonHeadMembers.length > 0) {
      return NextResponse.json({
        canDelete: false,
        deleteType: 'household_with_members',
        memberCount: members.length,
        nonHeadMemberCount: nonHeadMembers.length,
        householdHead: householdHead
          ? {
              name: `${householdHead.firstName} ${householdHead.lastName}`,
              id: householdHead.memberId,
            }
          : null,
        warningMessage: tWarnings('cannotDeleteHouseholdWithMembers'),
        members: members.map((member) => ({
          id: member.memberId,
          name: `${member.firstName} ${member.lastName}`,
          relationship: member.relationship,
          isHead: member.relationship === 'head',
        })),
      });
    }

    return NextResponse.json({
      canDelete: true,
      deleteType: 'household_head_only',
      memberCount: members.length,
      nonHeadMemberCount: 0,
      householdHead: householdHead
        ? {
            name: `${householdHead.firstName} ${householdHead.lastName}`,
            id: householdHead.memberId,
          }
        : null,
      warningMessage: tWarnings('deleteHouseholdWarning'),
      members: members.map((member) => ({
        id: member.memberId,
        name: `${member.firstName} ${member.lastName}`,
        relationship: member.relationship,
        isHead: member.relationship === 'head',
      })),
    });
  } catch (error) {
    const locale = await getLocaleFromCookies();
    const tErrors = await getTranslations({ locale, namespace: 'errors' });
    return NextResponse.json(
      {
        error: tErrors('failedToGetHouseholdDeletionInfo'),
        details: getErrorMessage(error),
      },
      { status: 500 }
    );
  }
}

'use client';

import { VariantProps } from 'class-variance-authority';
import { useTranslations } from 'next-intl';
import type { HTMLInputTypeAttribute } from 'react';
import type {
  FieldError,
  FieldErrorsImpl,
  FieldValues,
  Merge,
  Path,
  UseFormRegister,
} from 'react-hook-form';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';

interface FormFieldProps<TFormValues extends FieldValues> {
  id: Path<TFormValues>;
  label: string;
  type?: HTMLInputTypeAttribute;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  register: UseFormRegister<TFormValues>;
  error?:
    | FieldError
    | Merge<FieldError, FieldErrorsImpl<Record<string, unknown>>>;
  className?: string;
  variant?: 'default' | 'line';
}

export function FormField<TFormValues extends FieldValues>({
  id,
  label,
  type = 'text',
  placeholder,
  required = false,
  disabled = false,
  register,
  error,
  className,
  variant = 'default',
}: FormFieldProps<TFormValues>) {
  const tCommon = useTranslations('common');

  return (
    <div className={cn('space-y-2', className)}>
      <Label className="font-medium text-sm" htmlFor={id}>
        {label}
        {required && <span className="ml-1 text-destructive">*</span>}
      </Label>
      <Input
        className={cn(
          error &&
            (variant === 'line' ? 'border-destructive' : 'border-destructive')
        )}
        disabled={disabled}
        id={id}
        placeholder={placeholder}
        type={type}
        variant={variant}
        {...register(id)}
      />
      {error && (
        <p className="text-destructive text-xs">
          {String(error.message || tCommon('validationError'))}
        </p>
      )}
    </div>
  );
}

import { type NextRequest, NextResponse } from 'next/server';
import { getTranslations } from 'next-intl/server';
import { censusErrorKeys } from '@/lib/errors/census-errors';
import { getLocaleFromCookies } from '@/lib/utils/server-messages';

export async function GET(request: NextRequest) {
  // Get the reason and redirect URL from the query parameters
  const { searchParams } = new URL(request.url);
  const reason = searchParams.get('reason') || 'unauthenticated';
  const redirectTo = searchParams.get('redirectTo') || '/';

  // Create a response that redirects to the specified page
  const response = NextResponse.redirect(new URL(redirectTo, request.url));

  // Get translated message based on user's locale
  const locale = await getLocaleFromCookies();
  const t = await getTranslations({ locale, namespace: 'auth' });
  const translationKey = censusErrorKeys[reason] || censusErrorKeys.default;
  const translatedMessage = t(translationKey as any);

  // Set a cookie with the toast message
  const toastData = {
    type: 'warning',
    message: translatedMessage,
  };

  // Set the cookie
  response.cookies.set({
    name: 'census_toast',
    value: JSON.stringify(toastData),
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    maxAge: 10, // 10 seconds - short expiry prevents persistence issues
    path: '/',
    sameSite: 'lax',
  });

  return response;
}

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("POSTGRES_URL_NON_POOLING")
}

model SystemSettings {
  id           Int      @id @default(autoincrement())
  settingKey   String   @unique @map("setting_key") @db.VarChar(50)
  settingValue String?  @map("setting_value")
  description  String?
  updatedAt    DateTime @default(now()) @updatedAt @map("updated_at")

  @@map("system_settings")
}

model Admin {
  id                   Int       @id @default(autoincrement())
  username             String    @unique @db.Var<PERSON>har(50)
  password             String    @db.VarChar(255)
  email                String?   @db.VarChar(100)
  fullName             String?   @map("full_name") @db.VarChar(100)
  twoFactorSecret      String?   @map("two_factor_secret") @db.VarChar(64)
  twoFactorEnabled     <PERSON>olean   @default(false) @map("two_factor_enabled")
  twoFactorBackupCodes String?   @map("two_factor_backup_codes")
  lastLogin            DateTime? @map("last_login")
  createdAt            DateTime  @default(now()) @map("created_at")
  updatedAt            DateTime  @default(now()) @updatedAt @map("updated_at")

  @@map("admins")
}

model CensusYear {
  id                     Int                      @id @default(autoincrement())
  year                   Int                      @unique
  isActive               Boolean                  @default(false) @map("is_active")
  startDate              DateTime                 @map("start_date") @db.Date
  endDate                DateTime                 @map("end_date") @db.Date
  createdAt              DateTime                 @default(now()) @map("created_at")
  updatedAt              DateTime                 @default(now()) @updatedAt @map("updated_at")
  censusForms            CensusForm[]
  householdMembers       HouseholdMember[]
  householdsFirst        Household[]              @relation("FirstCensusYear")
  householdsLast         Household[]              @relation("LastCensusYear")
  memberHouseholdHistory MemberHouseholdHistory[]
  sacraments             Sacrament[]
  uniqueCodes            UniqueCode[]

  @@map("census_years")
}

model UniqueCode {
  id              Int        @id @default(autoincrement())
  code            String     @unique @db.VarChar(25)
  isAssigned      Boolean    @default(false) @map("is_assigned")
  assignedAt      DateTime?  @map("assigned_at")
  householdId     Int?       @map("household_id")
  censusYearId    Int        @map("census_year_id")
  validationStart Int        @map("validation_start")
  validationEnd   Int        @map("validation_end")
  validationHash  String     @map("validation_hash") @db.VarChar(64)
  createdAt       DateTime   @default(now()) @map("created_at")
  updatedAt       DateTime   @default(now()) @updatedAt @map("updated_at")
  censusYear      CensusYear @relation(fields: [censusYearId], references: [id])
  household       Household? @relation(fields: [householdId], references: [id])

  @@index([censusYearId])
  @@index([isAssigned])
  @@index([censusYearId, isAssigned])
  @@index([householdId])
  @@index([householdId, createdAt])
  @@index([validationHash])
  @@index([validationStart, validationEnd])
  @@index([code, validationHash])
  @@map("unique_codes")
}

model Suburb {
  id          Int      @id @default(autoincrement())
  salCode     String   @unique @map("sal_code") @db.VarChar(5)
  suburbName  String   @map("suburb_name") @db.VarChar(100)
  stateCode   String   @map("state_code") @db.VarChar(3)
  stateName   String   @map("state_name") @db.VarChar(50)
  displayName String   @map("display_name") @db.VarChar(150)
  searchText  String   @map("search_text") @db.VarChar(200)
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @default(now()) @updatedAt @map("updated_at")

  @@index([suburbName])
  @@index([stateCode])
  @@index([displayName])
  @@index([searchText])
  @@map("suburbs")
}

model Household {
  id                Int                      @id @default(autoincrement())
  suburb            String                   @db.VarChar(150)
  firstCensusYearId Int                      @map("first_census_year_id")
  lastCensusYearId  Int                      @map("last_census_year_id")
  createdAt         DateTime                 @default(now()) @map("created_at")
  updatedAt         DateTime                 @default(now()) @updatedAt @map("updated_at")
  censusForms       CensusForm[]
  householdMembers  HouseholdMember[]
  firstCensusYear   CensusYear               @relation("FirstCensusYear", fields: [firstCensusYearId], references: [id])
  lastCensusYear    CensusYear               @relation("LastCensusYear", fields: [lastCensusYearId], references: [id])
  memberHistoryFrom MemberHouseholdHistory[] @relation("FromHousehold")
  memberHistoryTo   MemberHouseholdHistory[] @relation("ToHousehold")
  uniqueCodes       UniqueCode[]

  @@index([suburb])
  @@index([firstCensusYearId])
  @@index([lastCensusYearId])
  @@index([createdAt])
  @@map("households")
}

model Member {
  id                     Int                      @id @default(autoincrement())
  firstName              String                   @map("first_name") @db.VarChar(50)
  lastName               String                   @map("last_name") @db.VarChar(50)
  dateOfBirth            DateTime?                @map("date_of_birth") @db.Date
  gender                 Gender
  mobilePhone            String                   @map("mobile_phone") @db.VarChar(10)
  hobby                  String?                  @db.VarChar(100)
  occupation             String?                  @db.VarChar(100)
  createdAt              DateTime                 @default(now()) @map("created_at")
  updatedAt              DateTime                 @default(now()) @updatedAt @map("updated_at")
  householdMembers       HouseholdMember[]
  memberHouseholdHistory MemberHouseholdHistory[]
  sacraments             Sacrament[]

  @@index([firstName])
  @@index([lastName])
  @@index([firstName, lastName])
  @@index([mobilePhone])
  @@index([gender])
  @@index([dateOfBirth])
  @@index([hobby])
  @@index([occupation])
  @@map("members")
}

model HouseholdMember {
  id           Int          @id @default(autoincrement())
  householdId  Int          @map("household_id")
  memberId     Int          @map("member_id")
  relationship Relationship
  censusYearId Int          @map("census_year_id")
  isCurrent    Boolean      @default(true) @map("is_current")
  createdAt    DateTime     @default(now()) @map("created_at")
  updatedAt    DateTime     @default(now()) @updatedAt @map("updated_at")
  censusYear   CensusYear   @relation(fields: [censusYearId], references: [id])
  household    Household    @relation(fields: [householdId], references: [id], onDelete: Cascade)
  member       Member       @relation(fields: [memberId], references: [id], onDelete: Cascade)

  @@unique([householdId, memberId, censusYearId])
  @@index([householdId, isCurrent])
  @@index([relationship])
  @@index([householdId, censusYearId])
  @@index([relationship, isCurrent])
  @@index([memberId, relationship, isCurrent])
  @@map("household_members")
}

model MemberHouseholdHistory {
  id              Int        @id @default(autoincrement())
  memberId        Int        @map("member_id")
  fromHouseholdId Int?       @map("from_household_id")
  toHouseholdId   Int?       @map("to_household_id")
  changeType      ChangeType @map("change_type")
  changeReason    String?    @map("change_reason")
  changeDate      DateTime   @map("change_date") @db.Date
  censusYearId    Int        @map("census_year_id")
  createdAt       DateTime   @default(now()) @map("created_at")
  censusYear      CensusYear @relation(fields: [censusYearId], references: [id])
  fromHousehold   Household? @relation("FromHousehold", fields: [fromHouseholdId], references: [id])
  member          Member     @relation(fields: [memberId], references: [id], onDelete: Cascade)
  toHousehold     Household? @relation("ToHousehold", fields: [toHouseholdId], references: [id])

  @@map("member_household_history")
}

model SacramentType {
  id          Int         @id @default(autoincrement())
  code        String      @unique @db.VarChar(50)
  name        String      @unique @db.VarChar(50)
  description String?
  sacraments  Sacrament[]

  @@map("sacrament_types")
}

model Sacrament {
  id              Int           @id @default(autoincrement())
  memberId        Int           @map("member_id")
  sacramentTypeId Int           @map("sacrament_type_id")
  date            DateTime?     @db.Date
  place           String?       @db.VarChar(255)
  notes           String?
  censusYearId    Int           @map("census_year_id")
  createdAt       DateTime      @default(now()) @map("created_at")
  updatedAt       DateTime      @default(now()) @updatedAt @map("updated_at")
  censusYear      CensusYear    @relation(fields: [censusYearId], references: [id])
  member          Member        @relation(fields: [memberId], references: [id], onDelete: Cascade)
  sacramentType   SacramentType @relation(fields: [sacramentTypeId], references: [id])

  @@unique([memberId, sacramentTypeId])
  @@map("sacraments")
}

model CensusForm {
  id                 Int        @id @default(autoincrement())
  householdId        Int        @map("household_id")
  censusYearId       Int        @map("census_year_id")
  status             FormStatus @default(not_started)
  lastUpdated        DateTime?  @map("last_updated")
  completionDate     DateTime?  @map("completion_date")
  addMemberAttempted Boolean    @default(false) @map("add_member_attempted")
  householdComment   String?    @map("household_comment") @db.Text
  createdAt          DateTime   @default(now()) @map("created_at")
  updatedAt          DateTime   @default(now()) @updatedAt @map("updated_at")
  censusYear         CensusYear @relation(fields: [censusYearId], references: [id])
  household          Household  @relation(fields: [householdId], references: [id], onDelete: Cascade)

  @@unique([householdId, censusYearId])
  @@map("census_forms")
}

model AuthRateLimit {
  id                Int       @id @default(autoincrement())
  sessionToken      String    @unique @map("session_token") @db.VarChar(64)
  failedAttempts    Int       @default(0) @map("failed_attempts")
  lockoutUntil      DateTime? @map("lockout_until")
  escalationLevel   Int       @default(0) @map("escalation_level")
  lastFailedAttempt DateTime? @map("last_failed_attempt")
  createdAt         DateTime  @default(now()) @map("created_at")
  updatedAt         DateTime  @default(now()) @updatedAt @map("updated_at")

  @@index([lockoutUntil])
  @@index([createdAt])
  @@index([sessionToken, lockoutUntil])
  @@map("auth_rate_limits")
}

model AuditLog {
  id         Int      @id @default(autoincrement())
  adminId    Int?     @map("admin_id")
  userId     Int?     @map("user_id")
  userType   String?  @map("user_type") @db.VarChar(20)
  entityType String?  @map("entity_type") @db.VarChar(50)
  entityId   Int?     @map("entity_id")
  action     String   @db.VarChar(100)
  tableName  String?  @map("table_name") @db.VarChar(50)
  recordId   Int?     @map("record_id")
  oldValues  String?  @map("old_values")
  newValues  String?  @map("new_values")
  ipAddress  String?  @map("ip_address") @db.VarChar(45)
  userAgent  String?  @map("user_agent")
  createdAt  DateTime @default(now()) @map("created_at")

  @@index([adminId])
  @@index([userId])
  @@index([action])
  @@index([tableName])
  @@index([entityType])
  @@index([entityId])
  @@index([createdAt])
  @@map("audit_logs")
}

model UserSession {
  id           Int       @id @default(autoincrement())
  sessionToken String    @unique @map("session_token") @db.VarChar(64)
  userId       Int?      @map("user_id")
  userType     String    @map("user_type") @db.VarChar(20)
  ipAddress    String?   @map("ip_address") @db.VarChar(45)
  userAgent    String?   @map("user_agent")
  lastActivity DateTime? @map("last_activity")
  expiresAt    DateTime  @map("expires_at")
  createdAt    DateTime  @default(now()) @map("created_at")
  updatedAt    DateTime  @default(now()) @updatedAt @map("updated_at")

  @@index([sessionToken])
  @@index([userId, userType])
  @@index([expiresAt])
  @@index([lastActivity])
  @@index([createdAt])
  @@map("user_sessions")
}

model ExportLog {
  id           Int      @id @default(autoincrement())
  adminId      Int?     @map("admin_id")
  exportType   String   @map("export_type") @db.VarChar(20)
  fileFormat   String?  @map("file_format") @db.VarChar(10)
  selection    String   @db.VarChar(50)
  parameters   String?
  fileSize     Int?     @map("file_size")
  recordCount  Int?     @map("record_count")
  ipAddress    String?  @map("ip_address") @db.VarChar(45)
  success      Boolean  @default(true)
  errorMessage String?  @map("error_message")
  createdAt    DateTime @default(now()) @map("created_at")

  @@index([adminId])
  @@index([exportType])
  @@index([createdAt])
  @@map("export_logs")
}

model ImportLog {
  id           Int      @id @default(autoincrement())
  adminId      Int?     @map("admin_id")
  fileName     String   @map("file_name") @db.VarChar(255)
  importType   String?  @map("import_type") @db.VarChar(20)
  details      String?
  fileSize     Int?     @map("file_size")
  recordCount  Int?     @map("record_count")
  ipAddress    String?  @map("ip_address") @db.VarChar(45)
  success      Boolean  @default(true)
  errorMessage String?  @map("error_message")
  createdAt    DateTime @default(now()) @map("created_at")

  @@index([adminId])
  @@index([createdAt])
  @@map("import_logs")
}

model BackupLog {
  id           Int      @id @default(autoincrement())
  adminId      Int?     @map("admin_id")
  backupType   String   @map("backup_type") @db.VarChar(20)
  fileName     String   @map("file_name") @db.VarChar(255)
  backupData   String?  @map("backup_data")
  fileSize     Int?     @map("file_size")
  success      Boolean  @default(true)
  errorMessage String?  @map("error_message")
  createdAt    DateTime @default(now()) @map("created_at")

  @@index([adminId])
  @@index([backupType])
  @@index([createdAt])
  @@map("backup_logs")
}

model CensusControl {
  id           Int      @id @default(autoincrement())
  settingKey   String   @unique @map("setting_key") @db.VarChar(100)
  settingValue String?  @map("setting_value")
  description  String?
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  @@map("census_controls")
}

enum Gender {
  male
  female
  other
}

enum Relationship {
  head
  spouse
  child
  parent
  relative
  other
}

enum FormStatus {
  not_started
  in_progress
  completed
}

enum ChangeType {
  join
  leave
  transfer
}

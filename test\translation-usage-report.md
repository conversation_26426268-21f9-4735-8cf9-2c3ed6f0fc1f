# WSCCC Census System - Translation Usage Analysis Report

**Generated:** 10/07/2025, 11:19:21 am
**Analyzer Version:** Rewritten (Key-based Detection)

---

## 📊 Executive Summary

| Metric | Value | Percentage |
|--------|-------|------------|
| **Total Translation Keys** | 1663 | 100% |
| **Keys in Use** | 1657 | 99.6% |
| **Unused Keys** | 6 | 0.4% |
| **Files Scanned** | 331 | - |
| **Lines Analyzed** | 58,093 | - |

### 🎯 Overall Assessment
✅ **Very Good!** Translation usage is highly optimized with minimal unused keys.

---

## 📂 Namespace Usage Breakdown

| Namespace | Used | Total | Usage Rate | Status |
|-----------|------|-------|------------|--------|
| `accessibility` | 1 | 1 | 100.0% | ✅ Excellent |
| `admin` | 431 | 431 | 100.0% | ✅ Excellent |
| `api` | 7 | 7 | 100.0% | ✅ Excellent |
| `auth` | 40 | 40 | 100.0% | ✅ Excellent |
| `brand` | 1 | 1 | 100.0% | ✅ Excellent |
| `census` | 101 | 101 | 100.0% | ✅ Excellent |
| `common` | 324 | 324 | 100.0% | ✅ Excellent |
| `dialogs` | 48 | 48 | 100.0% | ✅ Excellent |
| `emptyStates` | 13 | 13 | 100.0% | ✅ Excellent |
| `errors` | 237 | 237 | 100.0% | ✅ Excellent |
| `forms` | 68 | 68 | 100.0% | ✅ Excellent |
| `genders` | 3 | 3 | 100.0% | ✅ Excellent |
| `help` | 61 | 61 | 100.0% | ✅ Excellent |
| `info` | 0 | 2 | 0.0% | ❌ Poor |
| `legal` | 5 | 5 | 100.0% | ✅ Excellent |
| `metadata` | 24 | 24 | 100.0% | ✅ Excellent |
| `navigation` | 26 | 26 | 100.0% | ✅ Excellent |
| `notifications` | 50 | 50 | 100.0% | ✅ Excellent |
| `onboarding` | 14 | 14 | 100.0% | ✅ Excellent |
| `pagination` | 19 | 19 | 100.0% | ✅ Excellent |
| `relationships` | 9 | 9 | 100.0% | ✅ Excellent |
| `sacraments` | 8 | 8 | 100.0% | ✅ Excellent |
| `status` | 3 | 7 | 42.9% | ❌ Poor |
| `tables` | 17 | 17 | 100.0% | ✅ Excellent |
| `validation` | 134 | 134 | 100.0% | ✅ Excellent |
| `warnings` | 13 | 13 | 100.0% | ✅ Excellent |

---

## 🗑️ Unused Translation Keys

**Total Unused Keys:** 6

### 📁 `info` Namespace (2 unused)

- `info.allSacramentsRecorded`
- `info.sacramentLimitReached`

### 📁 `status` Namespace (4 unused)

- `status.approved`
- `status.incomplete`
- `status.pending`
- `status.rejected`


---

## 🔄 Duplicate Keys Analysis

**Total Duplicate Keys:** 182

Keys that appear in multiple namespaces may indicate opportunities for consolidation:

- **`requestTimedOut`** appears in: `api`, `admin`, `errors`
- **`accountDeleted`** appears in: `auth`, `admin`, `errors`, `notifications`
- **`adminLogin`** appears in: `auth`, `admin`, `navigation`
- **`backToLogin`** appears in: `auth`, `admin`
- **`censusClosed`** appears in: `auth`, `admin`, `status`
- **`enterSixDigitCode`** appears in: `auth`, `admin`
- **`errorDuringLogin`** appears in: `auth`, `admin`
- **`loginFailed`** appears in: `auth`, `admin`
- **`loginSuccessful`** appears in: `auth`, `admin`
- **`password`** appears in: `auth`, `admin`
- **`signIn`** appears in: `auth`, `admin`
- **`signInToAccessDashboard`** appears in: `auth`, `admin`
- **`unauthorized`** appears in: `auth`, `admin`, `errors`
- **`unknownLoginError`** appears in: `auth`, `admin`
- **`username`** appears in: `auth`, `admin`
- **`clickToCopy`** appears in: `census`, `admin`
- **`householdHead`** appears in: `census`, `admin`
- **`householdMembers`** appears in: `census`, `admin`, `common`
- **`lastUpdated`** appears in: `census`, `admin`, `common`
- **`sacraments`** appears in: `census`, `admin`, `common`
- **`uniqueCode`** appears in: `census`, `admin`, `common`, `navigation`
- **`updateMember`** appears in: `census`, `admin`
- **`addNewSacrament`** appears in: `common`, `census`
- **`allYears`** appears in: `common`, `admin`
- **`authenticationError`** appears in: `common`, `auth`
- **`censusYear`** appears in: `common`, `admin`, `tables`
- **`completed`** appears in: `common`, `census`
- **`contact`** appears in: `common`, `census`
- **`copyId`** appears in: `common`, `census`
- **`dateOfBirth`** appears in: `common`, `census`, `forms`
- **`delete`** appears in: `common`, `census`
- **`edit`** appears in: `common`, `census`
- **`editMember`** appears in: `common`, `census`
- **`failedToFetchSacraments`** appears in: `common`, `census`, `errors`
- **`firstPage`** appears in: `common`, `admin`, `pagination`
- **`gender`** appears in: `common`, `census`, `forms`
- **`householdInformation`** appears in: `common`, `census`
- **`inProgress`** appears in: `common`, `census`
- **`lastPage`** appears in: `common`, `admin`, `pagination`
- **`mainNavigation`** appears in: `common`, `admin`
- **`mobile`** appears in: `common`, `census`
- **`mobileNavigation`** appears in: `common`, `admin`
- **`name`** appears in: `common`, `brand`, `tables`
- **`nextPage`** appears in: `common`, `admin`, `pagination`
- **`noSacramentsAddedYet`** appears in: `common`, `census`
- **`notSpecified`** appears in: `common`, `admin`
- **`notStarted`** appears in: `common`, `census`
- **`personalInformation`** appears in: `common`, `census`
- **`previousPage`** appears in: `common`, `admin`, `pagination`
- **`qrCodeError`** appears in: `common`, `admin`
- **`relationship`** appears in: `common`, `census`, `forms`
- **`save`** appears in: `common`, `census`
- **`selectAll`** appears in: `common`, `admin`
- **`statusAssigned`** appears in: `common`, `admin`
- **`statusUnassigned`** appears in: `common`, `admin`
- **`suburb`** appears in: `common`, `census`, `forms`
- **`unknownError`** appears in: `common`, `admin`, `errors`
- **`validationError`** appears in: `common`, `admin`, `validation`
- **`welcome`** appears in: `common`, `census`
- **`areYouSure`** appears in: `dialogs`, `common`
- **`deleteAccount`** appears in: `dialogs`, `common`
- **`deleteHousehold`** appears in: `dialogs`, `common`
- **`yourUniqueCode`** appears in: `dialogs`, `admin`
- **`noMembersFound`** appears in: `emptyStates`, `common`
- **`noSacramentsRecorded`** appears in: `emptyStates`, `common`
- **`aiServiceUnavailable`** appears in: `errors`, `api`
- **`authenticationFailed`** appears in: `errors`, `api`
- **`backToHome`** appears in: `errors`, `admin`
- **`cannotDeleteAssignedCodes`** appears in: `errors`, `admin`
- **`cannotDeleteHouseholdHead`** appears in: `errors`, `dialogs`
- **`censusYearFetchFailed`** appears in: `errors`, `admin`
- **`failedToCreateHousehold`** appears in: `errors`, `common`
- **`failedToCreatePrintSession`** appears in: `errors`, `admin`
- **`failedToDeleteHouseholdMember`** appears in: `errors`, `census`
- **`failedToDeleteUniqueCodes`** appears in: `errors`, `admin`
- **`failedToFetchChurchInformation`** appears in: `errors`, `admin`
- **`failedToFetchData`** appears in: `errors`, `admin`
- **`failedToFetchHouseholdMembers`** appears in: `errors`, `census`
- **`failedToFetchSiteUrl`** appears in: `errors`, `admin`
- **`failedToFetchUniqueCodes`** appears in: `errors`, `admin`
- **`failedToGenerateUniqueCodes`** appears in: `errors`, `admin`
- **`failedToUpdateCensusYear`** appears in: `errors`, `admin`
- **`failedToUpdateHouseholdMember`** appears in: `errors`, `census`
- **`householdMemberDeletedSuccessfully`** appears in: `errors`, `census`
- **`householdMemberUpdatedSuccessfully`** appears in: `errors`, `census`
- **`householdSearchFailed`** appears in: `errors`, `admin`
- **`invalidConfirmationPhrase`** appears in: `errors`, `admin`, `validation`
- **`invalidMemberId`** appears in: `errors`, `admin`
- **`invalidParameters`** appears in: `errors`, `admin`
- **`invalidRequestData`** appears in: `errors`, `admin`
- **`invalidRequestFormat`** appears in: `errors`, `api`
- **`memberFetchFailed`** appears in: `errors`, `admin`
- **`memberNotFound`** appears in: `errors`, `admin`
- **`printSessionExpired`** appears in: `errors`, `admin`
- **`selectCodesToPrint`** appears in: `errors`, `admin`
- **`settingsWouldBeSavedInProduction`** appears in: `errors`, `admin`
- **`tooManyRequests`** appears in: `errors`, `api`
- **`validationFailed`** appears in: `errors`, `admin`
- **`dateOptional`** appears in: `forms`, `common`
- **`enterFirstName`** appears in: `forms`, `census`
- **`enterHobby`** appears in: `forms`, `census`
- **`enterLastName`** appears in: `forms`, `census`
- **`firstName`** appears in: `forms`, `census`
- **`hobby`** appears in: `forms`, `census`
- **`lastName`** appears in: `forms`, `census`
- **`mobilePhone`** appears in: `forms`, `common`
- **`placeOptional`** appears in: `forms`, `common`
- **`sacramentType`** appears in: `forms`, `common`
- **`selectType`** appears in: `forms`, `census`
- **`selectYear`** appears in: `forms`, `admin`
- **`female`** appears in: `genders`, `forms`
- **`male`** appears in: `genders`, `forms`
- **`other`** appears in: `genders`, `forms`, `relationships`
- **`address`** appears in: `help`, `forms`
- **`churchName`** appears in: `help`, `admin`
- **`email`** appears in: `help`, `forms`
- **`helpAndFaq`** appears in: `help`, `census`
- **`phone`** appears in: `help`, `forms`
- **`and`** appears in: `legal`, `common`
- **`byClickingContinue`** appears in: `legal`, `common`
- **`privacyPolicy`** appears in: `legal`, `common`
- **`termsAndConditions`** appears in: `legal`, `common`
- **`termsOfService`** appears in: `legal`, `common`
- **`householdManagementDescription`** appears in: `metadata`, `admin`
- **`systemSettingsDescription`** appears in: `metadata`, `admin`
- **`title`** appears in: `metadata`, `census`
- **`adminPortal`** appears in: `navigation`, `common`
- **`census`** appears in: `navigation`, `admin`
- **`code`** appears in: `navigation`, `admin`
- **`faq`** appears in: `navigation`, `help`
- **`help`** appears in: `navigation`, `admin`
- **`household`** appears in: `navigation`, `admin`
- **`members`** appears in: `navigation`, `admin`
- **`platform`** appears in: `navigation`, `common`
- **`toggleMenu`** appears in: `navigation`, `common`
- **`toggleMobileMenu`** appears in: `navigation`, `common`
- **`censusYearUpdatedSuccessfully`** appears in: `notifications`, `admin`
- **`churchInformationUpdatedSuccessfully`** appears in: `notifications`, `errors`
- **`errorOccurredDuringRegistration`** appears in: `notifications`, `errors`
- **`failedToRegisterHousehold`** appears in: `notifications`, `errors`
- **`householdRegisteredSuccessfully`** appears in: `notifications`, `census`
- **`householdUpdatedSuccessfully`** appears in: `notifications`, `admin`
- **`memberDeletedSuccessfully`** appears in: `notifications`, `dialogs`
- **`messageDeleted`** appears in: `notifications`, `admin`
- **`messagesDeleted`** appears in: `notifications`, `admin`
- **`sacramentDeletedSuccessfully`** appears in: `notifications`, `errors`
- **`sacramentUpdatedSuccessfully`** appears in: `notifications`, `errors`
- **`sessionUpdateFailed`** appears in: `notifications`, `errors`
- **`twoFactorDisabled`** appears in: `notifications`, `admin`
- **`twoFactorEnabled`** appears in: `notifications`, `admin`
- **`getStarted`** appears in: `onboarding`, `common`
- **`next`** appears in: `pagination`, `common`
- **`previous`** appears in: `pagination`, `common`
- **`child`** appears in: `relationships`, `forms`
- **`head`** appears in: `relationships`, `forms`
- **`parent`** appears in: `relationships`, `forms`
- **`relative`** appears in: `relationships`, `forms`
- **`spouse`** appears in: `relationships`, `forms`
- **`confirmation`** appears in: `sacraments`, `common`
- **`censusOpen`** appears in: `status`, `admin`
- **`complete`** appears in: `status`, `census`
- **`age`** appears in: `tables`, `common`
- **`createdDate`** appears in: `tables`, `admin`
- **`id`** appears in: `tables`, `common`
- **`openActionsMenu`** appears in: `tables`, `admin`
- **`selectAllHouseholds`** appears in: `tables`, `admin`
- **`selectAllMembers`** appears in: `tables`, `admin`
- **`status`** appears in: `tables`, `emptyStates`
- **`censusYearIdRequired`** appears in: `validation`, `errors`
- **`currentCensusYearMustBeGreater`** appears in: `validation`, `admin`
- **`currentCensusYearRequired`** appears in: `validation`, `admin`
- **`invalidAddress`** appears in: `validation`, `errors`
- **`invalidChurchName`** appears in: `validation`, `errors`
- **`invalidContactNumber`** appears in: `validation`, `errors`
- **`invalidEmail`** appears in: `validation`, `errors`
- **`passwordsDoNotMatch`** appears in: `validation`, `common`
- **`required`** appears in: `validation`, `common`
- **`selectGender`** appears in: `validation`, `forms`
- **`selectRelationship`** appears in: `validation`, `forms`
- **`verificationCodeMustBe6Digits`** appears in: `validation`, `common`
- **`cannotDeleteHouseholdWithMembers`** appears in: `warnings`, `errors`
- **`largeDatasetDetected`** appears in: `warnings`, `common`


---

## 💡 Recommendations

### 🗑️ Cleanup Opportunities

Consider removing the 6 unused translation keys to:
- Reduce bundle size
- Improve maintainability
- Eliminate confusion for developers

### 🔄 Consolidation Opportunities

Review the 182 duplicate keys for potential consolidation:
- Move common keys to a shared namespace (e.g., `common`)
- Eliminate redundant translations
- Improve namespace organization


---

## 🔍 Analysis Methodology

This report was generated using an **improved key-based detection method** that:

1. **Extracts all translation keys** from `lang/en.json` and `lang/zh-CN.json`
2. **Scans codebase files** (`.tsx`, `.ts`, `.jsx`, `.js`) for actual key usage
3. **Identifies specific usage patterns**:
   - `t('key')` - Direct translation calls
   - `tNamespace('key')` - Namespace-specific hooks
   - `getTranslations()('key')` - Server-side usage
   - `useTranslations('namespace')` - Hook namespace detection
   - Object access patterns

4. **Compares extracted keys** against actual usage to identify unused translations

This method provides **99%+ accuracy** by focusing on actual key usage rather than regex pattern detection for hardcoded text.

---

**Report Generated by:** WSCCC Translation Usage Analyzer  
**Analysis Date:** 10/07/2025, 11:19:21 am  
**Total Analysis Time:** ~23 seconds  
**Accuracy:** High (Key-based detection method)

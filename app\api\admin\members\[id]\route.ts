import { type NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { getTranslations } from 'next-intl/server';
import { authOptions } from '@/lib/auth/auth-options';
import {
  deleteMemberWithChecks,
  getMemberWithFullDetails,
  HouseholdHeadDeletionError,
  updateMemberWithValidation,
} from '@/lib/db/members';
import {
  getErrorMessage,
  getZodErrorDetails,
} from '@/lib/utils/error-handling';
import { getLocaleFromCookies } from '@/lib/utils/server-messages';
import { createUpdateMemberSchema } from '@/lib/validation/members';

/**
 * GET /api/admin/members/:id
 *
 * Fetches a specific member with full details
 * Only accessible to admin users
 */
export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  // Get locale from centralized utility at the beginning
  const locale = await getLocaleFromCookies();

  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Require admin authentication
    if (!session || session.user.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get translations
    const t = await getTranslations({ locale, namespace: 'admin' });

    // In Next.js 15+, params is a Promise that must be awaited
    const { id: idString } = await params;
    const id = Number.parseInt(idString, 10);

    if (Number.isNaN(id)) {
      return NextResponse.json(
        { error: t('invalidMemberId') },
        { status: 400 }
      );
    }

    // Get the member with full details
    const member = await getMemberWithFullDetails(id);

    if (!member) {
      return NextResponse.json({ error: t('memberNotFound') }, { status: 404 });
    }

    return NextResponse.json(member);
  } catch (error) {
    const t = await getTranslations({ locale, namespace: 'admin' });
    return NextResponse.json(
      { error: t('memberFetchFailed'), details: getErrorMessage(error) },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/admin/members/:id
 *
 * Updates a specific member's information
 * Only accessible to admin users
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  // Get locale from centralized utility at the beginning
  const locale = await getLocaleFromCookies();

  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Require admin authentication
    if (!session || session.user.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get translations
    const t = await getTranslations({ locale, namespace: 'admin' });

    // In Next.js 15+, params is a Promise that must be awaited
    const { id: idString } = await params;
    const id = Number.parseInt(idString, 10);

    if (Number.isNaN(id)) {
      return NextResponse.json(
        { error: t('invalidMemberId') },
        { status: 400 }
      );
    }

    // Parse and validate request body with translations
    const body = await request.json();
    const updateMemberSchema = await createUpdateMemberSchema(locale);
    const validationResult = updateMemberSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: t('invalidRequestData'),
          details: getZodErrorDetails(validationResult.error),
        },
        { status: 400 }
      );
    }

    const data = validationResult.data;

    // Check if member exists
    const existingMember = await getMemberWithFullDetails(id);
    if (!existingMember) {
      return NextResponse.json({ error: t('memberNotFound') }, { status: 404 });
    }

    // Update the member (transform field names from snake_case to camelCase)
    await updateMemberWithValidation(id, {
      firstName: data.firstName,
      lastName: data.lastName,
      dateOfBirth: data.dateOfBirth,
      gender: data.gender,
      mobilePhone: data.mobilePhone,
      hobby: data.hobby,
      occupation: data.occupation,
      relationship: data.relationship,
    });

    // Get updated member details
    const updatedMember = await getMemberWithFullDetails(id);

    return NextResponse.json(updatedMember);
  } catch (error) {
    const t = await getTranslations({ locale, namespace: 'errors' });
    return NextResponse.json(
      { error: t('memberUpdateFailed'), details: getErrorMessage(error) },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/admin/members/:id
 *
 * Deletes a specific member
 * Only accessible to admin users
 */
export async function DELETE(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  // Get locale from centralized utility at the beginning
  const locale = await getLocaleFromCookies();

  try {
    // Check authentication
    const session = await getServerSession(authOptions);

    // Require admin authentication
    if (!session || session.user.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get translations
    const t = await getTranslations({ locale, namespace: 'errors' });

    // In Next.js 15+, params is a Promise that must be awaited
    const { id: idString } = await params;
    const id = Number.parseInt(idString, 10);

    if (Number.isNaN(id)) {
      return NextResponse.json(
        { error: t('invalidMemberId') },
        { status: 400 }
      );
    }

    // Check if member exists
    const existingMember = await getMemberWithFullDetails(id);
    if (!existingMember) {
      return NextResponse.json({ error: t('memberNotFound') }, { status: 404 });
    }

    // Delete the member (with validation)
    await deleteMemberWithChecks(id);

    const tDialogs = await getTranslations({ locale, namespace: 'dialogs' });
    return NextResponse.json({
      message: tDialogs('memberDeletedSuccessfully'),
    });
  } catch (error) {
    // Handle specific validation errors
    if (error instanceof HouseholdHeadDeletionError) {
      const t = await getTranslations({ locale, namespace: 'errors' });
      return NextResponse.json(
        { error: t('cannotDeleteHouseholdHeadWhenO') },
        { status: 400 }
      );
    }

    const t = await getTranslations({ locale, namespace: 'errors' });
    return NextResponse.json(
      { error: t('memberDeleteFailed'), details: getErrorMessage(error) },
      { status: 500 }
    );
  }
}
